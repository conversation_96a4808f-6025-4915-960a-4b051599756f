# Management Group Structure Overview

## Visual Representation

```
Tenant Root Management Group
└── EWH (Enterprise-Wide Hub)
    ├── Platform
    │   ├── mg-Platform-Management
    │   │   └── [Management subscriptions]
    │   └── mg-Platform-Connectivity
    │       └── [Connectivity subscriptions]
    ├── Landing Zones
    │   ├── lz-prd
    │   │   ├── lz-prd-legacy
    │   │   │   └── [Legacy production workloads]
    │   │   └── lz-prd-microwave
    │   │       └── [Microwave production workloads]
    │   └── lz-non-prd
    │       ├── lz-non-prd-uat
    │       │   └── [UAT environment subscriptions]
    │       └── lz-non-prd-dev
    │           └── [Development environment subscriptions]
    ├── Decommissioned
    │   └── [Subscriptions being decommissioned]
    └── Sandbox
        └── [Sandbox/experimental subscriptions]
```

## Management Group Purposes

### EWH (Enterprise-Wide Hub)
- **Purpose**: Root management group cho toàn bộ organization
- **Policies**: Enterprise-wide governance policies
- **Scope**: Toàn bộ Azure environment

### Platform
- **Purpose**: Chứa các platform services và shared infrastructure
- **Policies**: Platform-specific governance và security policies

#### mg-Platform-Management
- **Purpose**: Management và monitoring services
- **Typical Subscriptions**: 
  - Log Analytics workspace
  - Azure Monitor
  - Azure Security Center
  - Backup services
- **Policies**: Management và monitoring compliance

#### mg-Platform-Connectivity
- **Purpose**: Network connectivity và shared networking services
- **Typical Subscriptions**:
  - Hub VNet
  - Azure Firewall
  - VPN Gateway
  - ExpressRoute
- **Policies**: Network security và connectivity policies

### Landing Zones
- **Purpose**: Application workloads và business services
- **Policies**: Workload-specific governance

#### lz-prd (Production)
- **Purpose**: Production workloads
- **Policies**: Strict security, backup requirements, monitoring

##### lz-prd-legacy
- **Purpose**: Legacy production applications
- **Policies**: Legacy-specific compliance requirements

##### lz-prd-microwave
- **Purpose**: Microwave-related production workloads
- **Policies**: Industry-specific compliance requirements

#### lz-non-prd (Non-Production)
- **Purpose**: Non-production environments
- **Policies**: Development-friendly policies với security guardrails

##### lz-non-prd-uat
- **Purpose**: User Acceptance Testing environment
- **Policies**: UAT-specific policies, data protection

##### lz-non-prd-dev
- **Purpose**: Development environment
- **Policies**: Developer-friendly policies với basic security

### Decommissioned
- **Purpose**: Subscriptions đang được decommission
- **Policies**: Restrictive policies để prevent new resources

### Sandbox
- **Purpose**: Experimentation và learning
- **Policies**: Restrictive policies để prevent costly resources

## Policy Strategy

### Root Level (EWH)
- Mandatory tagging
- Cost management
- Security baseline
- Compliance monitoring

### Platform Level
- Infrastructure-specific policies
- Network security
- Backup requirements
- Monitoring requirements

### Landing Zones Level
- Environment-specific policies
- Application security
- Data protection
- Performance monitoring

### Sandbox Level
- Resource type restrictions
- Cost limitations
- Network isolation
- Time-based cleanup

## Subscription Placement Guidelines

### Platform Subscriptions
- **Management**: Log Analytics, Security Center, Backup
- **Connectivity**: Hub networking, Firewall, VPN

### Production Subscriptions
- **Legacy**: Existing production applications
- **Microwave**: New microwave-related applications

### Non-Production Subscriptions
- **UAT**: User acceptance testing environments
- **Dev**: Development và testing environments

### Special Purpose Subscriptions
- **Decommissioned**: Subscriptions being retired
- **Sandbox**: Learning và experimentation

## Benefits

1. **Clear Separation**: Tách biệt rõ ràng giữa các environment
2. **Granular Governance**: Policies phù hợp cho từng environment
3. **Scalability**: Dễ dàng thêm management groups mới
4. **Compliance**: Đáp ứng requirements cho từng loại workload
5. **Cost Management**: Tracking và control costs theo environment
6. **Security**: Layered security approach với appropriate controls
