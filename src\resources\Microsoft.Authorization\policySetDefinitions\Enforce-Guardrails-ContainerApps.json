{"name": "Enforce-Guardrails-ContainerApps", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Container Apps", "description": "This policy initiative is a group of policies that ensures Container Apps is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Container <PERSON><PERSON>", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"containerAppsManagedIdentity": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "containerAppsVnetInjection": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8b346db6-85af-419b-8557-92cee2c0f9bb", "policyDefinitionReferenceId": "Deny-ContainerApp-Vnet-Injection", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('containerAppsVnetInjection')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b874ab2d-72dd-47f1-8cb5-4a306478a4e7", "policyDefinitionReferenceId": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>-Managed-Identity", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('containerAppsManagedIdentity')]"}}}], "policyDefinitionGroups": null}}