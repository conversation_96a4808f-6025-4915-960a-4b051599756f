{"name": "Enforce-Guardrails-SQL", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for SQL and SQL Managed Instance", "description": "This policy initiative is a group of policies that ensures SQL and SQL Managed Instance is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"sqlManagedAadOnly": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "sqlAadOnly": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "sqlManagedDefender": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "modifySqlPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c5a62eb0-c65a-4220-8a4d-f70dd4ca95dd", "policyDefinitionReferenceId": "<PERSON><PERSON>-Sql-Managed-Defender", "definitionVersion": "2.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('sqlManagedDefender')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/abda6d70-9778-44e7-84a8-06713e6db027", "policyDefinitionReferenceId": "Deny-Sql-Aad-Only", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('sqlAadOnly')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/78215662-041e-49ed-a9dd-5385911b3a1f", "policyDefinitionReferenceId": "Deny-Sql-Managed-Aad-Only", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('sqlManagedAadOnly')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6134c3db-786f-471e-87bc-8f479dc890f6", "policyDefinitionReferenceId": "Dine-Sql-Adv-Data", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/28b0b1e5-17ba-4963-a7a4-5a1ab4400a0b", "policyDefinitionReferenceId": "Modify-Sql-PublicNetworkAccess", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('modifySqlPublicNetworkAccess')]"}}}], "policyDefinitionGroups": null}}