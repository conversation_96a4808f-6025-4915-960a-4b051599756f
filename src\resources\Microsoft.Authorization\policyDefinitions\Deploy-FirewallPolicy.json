{"name": "Deploy-FirewallPolicy", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Deploy Azure Firewall Manager policy in the subscription", "description": "Deploys Azure Firewall Manager policy in subscription where the policy is assigned.", "metadata": {"version": "1.0.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"fwpolicy": {"type": "Object", "metadata": {"displayName": "fwpolicy", "description": "Object describing Azure Firewall Policy"}, "defaultValue": {}}, "fwPolicyRegion": {"type": "String", "metadata": {"displayName": "fwPolicyRegion", "description": "Select Azure region for Azure Firewall Policy", "strongType": "location"}}, "rgName": {"type": "String", "metadata": {"displayName": "rgName", "description": "Provide name for resource group."}}, "effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Resources/subscriptions"}]}, "then": {"effect": "[[parameters('effect')]", "details": {"type": "Microsoft.Network/firewallPolicies", "deploymentScope": "subscription", "existenceScope": "resourceGroup", "resourceGroupName": "[[parameters('rgName')]", "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/b24988ac-6180-42a0-ab88-20f7382dd24c"], "deployment": {"location": "northeurope", "properties": {"mode": "Incremental", "parameters": {"rgName": {"value": "[[parameters('rgName')]"}, "fwPolicy": {"value": "[[parameters('fwPolicy')]"}, "fwPolicyRegion": {"value": "[[parameters('fwPolicyRegion')]"}}, "template": {"$schema": "http://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json", "contentVersion": "*******", "parameters": {"rgName": {"type": "String"}, "fwPolicy": {"type": "object"}, "fwPolicyRegion": {"type": "String"}}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "apiVersion": "2018-05-01", "name": "[[parameters('rgName')]", "location": "[[deployment().location]", "properties": {}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2018-05-01", "name": "fwpolicies", "resourceGroup": "[[parameters('rgName')]", "dependsOn": ["[[resourceId('Microsoft.Resources/resourceGroups/', parameters('rgName'))]"], "properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json", "contentVersion": "*******", "parameters": {}, "variables": {}, "resources": [{"type": "Microsoft.Network/firewallPolicies", "apiVersion": "2019-09-01", "name": "[[parameters('fwpolicy').firewallPolicyName]", "location": "[[parameters('fwpolicy').location]", "dependsOn": [], "tags": {}, "properties": {}, "resources": [{"type": "ruleGroups", "apiVersion": "2019-09-01", "name": "[[parameters('fwpolicy').ruleGroups.name]", "dependsOn": ["[[resourceId('Microsoft.Network/firewallPolicies',parameters('fwpolicy').firewallPolicyName)]"], "properties": {"priority": "[[parameters('fwpolicy').ruleGroups.properties.priority]", "rules": "[[parameters('fwpolicy').ruleGroups.properties.rules]"}}]}], "outputs": {}}}}], "outputs": {}}}}}}}}}