{"name": "Deny-AzFw-Without-Policy", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Azure Firewall should have a default Firewall Policy", "description": "This policy denies the creation of Azure Firewall without a default Firewall Policy.", "metadata": {"version": "1.0.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/azureFirewalls"}, {"field": "Microsoft.Network/azureFirewalls/firewallPolicy.id", "exists": "false"}]}, "then": {"effect": "[[parameters('effect')]"}}}}