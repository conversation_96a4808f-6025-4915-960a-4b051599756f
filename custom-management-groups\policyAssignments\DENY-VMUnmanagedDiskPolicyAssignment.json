{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}}, "variables": {"policyDefinitions": {"denyVMUnmanagedDisk": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "policyVersion": "1.*.*"}, "policyAssignmentNames": {"denyVMUnmanagedDisk": "Deny-UnmanagedDisk", "description": "Deny virtual machines that do not use managed disk. It checks the managed disk property on virtual machine OS Disk fields.", "displayName": "Deny virtual machines and virtual machine scale sets that do not use managed disk"}, "nonComplianceMessage": {"message": "Virtual machines and virtual machine scales sets {enforcementMode} use a managed disk.", "Default": "must", "DoNotEnforce": "should"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').denyVMUnmanagedDisk]", "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').denyVMUnmanagedDisk]", "definitionVersion": "[variables('policyDefinitions').policyVersion]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "overrides": [{"kind": "policyEffect", "value": "<PERSON><PERSON>"}]}}], "outputs": {}}