# Generate ALZ Policies Script
# This script helps generate policy assignments based on Azure Landing Zones templates

param(
    [Parameter(Mandatory = $true)]
    [string]$ALZSourcePath,
    
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = ".\generated-policies",
    
    [Parameter(Mandatory = $false)]
    [string]$ManagementGroupPrefix = "EWH",
    
    [Parameter(Mandatory = $false)]
    [string[]]$PolicyCategories = @("Security", "Compute", "Network", "Storage", "SQL")
)

$ErrorActionPreference = "Stop"

Write-Host "Generating ALZ Policy Assignments..." -ForegroundColor Green
Write-Host "Source Path: $ALZSourcePath" -ForegroundColor Yellow
Write-Host "Output Path: $OutputPath" -ForegroundColor Yellow
Write-Host "Management Group Prefix: $ManagementGroupPrefix" -ForegroundColor Yellow

# Create output directory
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
}

# Define policy assignment mappings
$policyMappings = @{
    "AUDIT" = @{
        "Scope" = "LandingZones"
        "EnforcementMode" = "Default"
        "Description" = "Audit policies for compliance monitoring"
    }
    "DENY" = @{
        "Scope" = "LandingZones" 
        "EnforcementMode" = "Default"
        "Description" = "Deny policies for security enforcement"
    }
    "DINE" = @{
        "Scope" = "LandingZones"
        "EnforcementMode" = "Default"
        "Description" = "Deploy if not exists policies for automatic remediation"
    }
    "ENFORCE" = @{
        "Scope" = "LandingZones"
        "EnforcementMode" = "Default"
        "Description" = "Enforce guardrails policies"
    }
    "MODIFY" = @{
        "Scope" = "LandingZones"
        "EnforcementMode" = "Default"
        "Description" = "Modify policies for configuration changes"
    }
}

# Management group scope mappings
$scopeMappings = @{
    "Root" = $ManagementGroupPrefix
    "Platform" = "$ManagementGroupPrefix-Platform"
    "Management" = "$ManagementGroupPrefix-Platform-Management"
    "Connectivity" = "$ManagementGroupPrefix-Platform-Connectivity"
    "LandingZones" = "$ManagementGroupPrefix-LandingZones"
    "Production" = "$ManagementGroupPrefix-lz-prd"
    "NonProduction" = "$ManagementGroupPrefix-lz-non-prd"
    "Sandbox" = "$ManagementGroupPrefix-Sandbox"
    "Decommissioned" = "$ManagementGroupPrefix-Decommissioned"
}

try {
    # Find policy assignment files
    $policyAssignmentPath = Join-Path $ALZSourcePath "eslzArm\managementGroupTemplates\policyAssignments"
    
    if (-not (Test-Path $policyAssignmentPath)) {
        Write-Host "Policy assignments path not found: $policyAssignmentPath" -ForegroundColor Red
        exit 1
    }
    
    $policyFiles = Get-ChildItem -Path $policyAssignmentPath -Filter "*.json" | Where-Object { $_.Name -notlike "*china*" -and $_.Name -notlike "*gov*" }
    
    Write-Host "Found $($policyFiles.Count) policy assignment files" -ForegroundColor Green
    
    # Generate policy assignments template
    $template = @{
        '$schema' = "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#"
        contentVersion = "*******"
        metadata = @{
            description = "Generated policy assignments from Azure Landing Zones"
        }
        parameters = @{
            managementGroups = @{
                type = "object"
                metadata = @{
                    description = "Object containing all management group names"
                }
            }
            enforcementMode = @{
                type = "string"
                defaultValue = "Default"
                allowedValues = @("Default", "DoNotEnforce")
                metadata = @{
                    description = "Policy enforcement mode"
                }
            }
        }
        variables = @{
            scopeMappings = $scopeMappings
        }
        resources = @()
    }
    
    $resourceIndex = 0
    
    foreach ($file in $policyFiles) {
        try {
            Write-Host "Processing: $($file.Name)" -ForegroundColor Cyan
            
            $content = Get-Content -Path $file.FullName -Raw | ConvertFrom-Json
            
            # Extract policy information
            $policyType = ($file.BaseName -split "-")[0]
            $policyName = $file.BaseName
            
            # Determine scope based on policy type and name
            $scope = "LandingZones"
            if ($policyName -like "*ASB*" -or $policyName -like "*Activity*") {
                $scope = "Root"
            } elseif ($policyName -like "*Private*DNS*") {
                $scope = "Connectivity"
            } elseif ($policyName -like "*Sandbox*") {
                $scope = "Sandbox"
            } elseif ($policyName -like "*Decomm*") {
                $scope = "Decommissioned"
            }
            
            # Create policy assignment resource
            $policyAssignment = @{
                type = "Microsoft.Authorization/policyAssignments"
                apiVersion = "2024-04-01"
                name = "Generated-$policyName"
                scope = "[tenantResourceId('Microsoft.Management/managementGroups/', variables('scopeMappings').$scope)]"
                properties = @{
                    description = "Generated from $($file.Name)"
                    displayName = "Generated: $policyName"
                    enforcementMode = "[parameters('enforcementMode')]"
                }
            }
            
            # Add policy definition ID if found in original
            if ($content.variables.policyDefinitions) {
                $policyDefKeys = $content.variables.policyDefinitions | Get-Member -MemberType NoteProperty | Select-Object -ExpandProperty Name
                if ($policyDefKeys.Count -gt 0) {
                    $firstKey = $policyDefKeys[0]
                    $policyDefId = $content.variables.policyDefinitions.$firstKey
                    $policyAssignment.properties.policyDefinitionId = $policyDefId
                }
            }
            
            # Add identity if needed for DINE policies
            if ($policyType -eq "DINE" -or $policyType -eq "MODIFY") {
                $policyAssignment.properties.identity = @{
                    type = "SystemAssigned"
                }
            }
            
            $template.resources += $policyAssignment
            $resourceIndex++
            
        } catch {
            Write-Host "Error processing $($file.Name): $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
    
    # Save generated template
    $outputFile = Join-Path $OutputPath "generated-policy-assignments.json"
    $template | ConvertTo-Json -Depth 10 | Set-Content -Path $outputFile
    
    Write-Host "Generated policy assignments template: $outputFile" -ForegroundColor Green
    Write-Host "Total policy assignments: $resourceIndex" -ForegroundColor Green
    
    # Generate summary report
    $summaryFile = Join-Path $OutputPath "policy-summary.md"
    $summary = @"
# Generated Policy Assignments Summary

Generated on: $(Get-Date)
Source: $ALZSourcePath
Management Group Prefix: $ManagementGroupPrefix
Total Policies: $resourceIndex

## Policy Types Distribution

"@
    
    $policyTypes = $template.resources | Group-Object { ($_.name -split "-")[1] } | Sort-Object Count -Descending
    foreach ($type in $policyTypes) {
        $summary += "- $($type.Name): $($type.Count) policies`n"
    }
    
    $summary += @"

## Scope Distribution

"@
    
    $scopes = $template.resources | Group-Object { ($_.scope -split "'")[3] } | Sort-Object Count -Descending
    foreach ($scope in $scopes) {
        $summary += "- $($scope.Name): $($scope.Count) policies`n"
    }
    
    $summary | Set-Content -Path $summaryFile
    
    Write-Host "Generated summary report: $summaryFile" -ForegroundColor Green
    
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nGeneration completed successfully!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Review generated files in: $OutputPath" -ForegroundColor White
Write-Host "2. Customize policy assignments as needed" -ForegroundColor White
Write-Host "3. Deploy using Azure CLI or PowerShell" -ForegroundColor White
