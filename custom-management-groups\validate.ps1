# Validation Script for Custom Management Group Structure
# This script validates the deployed management groups and policies

param(
    [Parameter(Mandatory = $false)]
    [string]$ManagementGroupPrefix = "EWH"
)

$ErrorActionPreference = "Stop"

Write-Host "Validating Custom Management Group Structure..." -ForegroundColor Green
Write-Host "Management Group Prefix: $ManagementGroupPrefix" -ForegroundColor Yellow

try {
    # Check if user is logged in to Azure
    $context = Get-AzContext
    if (-not $context) {
        Write-Host "Please login to Azure first using Connect-AzAccount" -ForegroundColor Red
        exit 1
    }
    
    # Define expected management groups
    $expectedMgmtGroups = @(
        $ManagementGroupPrefix,
        "$ManagementGroupPrefix-Platform",
        "$ManagementGroupPrefix-Platform-Management",
        "$ManagementGroupPrefix-Platform-Connectivity",
        "$ManagementGroupPrefix-LandingZones",
        "$ManagementGroupPrefix-lz-prd",
        "$ManagementGroupPrefix-lz-non-prd",
        "$ManagementGroupPrefix-lz-prd-legacy",
        "$ManagementGroupPrefix-lz-prd-microwave",
        "$ManagementGroupPrefix-lz-non-prd-uat",
        "$ManagementGroupPrefix-lz-non-prd-dev",
        "$ManagementGroupPrefix-Decommissioned",
        "$ManagementGroupPrefix-Sandbox"
    )
    
    Write-Host "`nValidating Management Groups..." -ForegroundColor Yellow
    $mgmtGroupsValid = $true
    
    foreach ($mgName in $expectedMgmtGroups) {
        try {
            $mg = Get-AzManagementGroup -GroupName $mgName -ErrorAction SilentlyContinue
            if ($mg) {
                Write-Host "✓ Management Group '$mgName' exists" -ForegroundColor Green
            } else {
                Write-Host "✗ Management Group '$mgName' not found" -ForegroundColor Red
                $mgmtGroupsValid = $false
            }
        } catch {
            Write-Host "✗ Error checking Management Group '$mgName': $($_.Exception.Message)" -ForegroundColor Red
            $mgmtGroupsValid = $false
        }
    }
    
    # Validate policy definitions
    Write-Host "`nValidating Policy Definitions..." -ForegroundColor Yellow
    $expectedPolicies = @(
        "Audit-Tags-Mandatory-Rg",
        "Audit-PrivateLinkDnsZones",
        "Deploy-Sql-SecurityAlertPolicies"
    )
    
    $policiesValid = $true
    $mgScope = "/providers/Microsoft.Management/managementGroups/$ManagementGroupPrefix"
    
    foreach ($policyName in $expectedPolicies) {
        try {
            $policy = Get-AzPolicyDefinition -Name $policyName -ManagementGroupName $ManagementGroupPrefix -ErrorAction SilentlyContinue
            if ($policy) {
                Write-Host "✓ Policy Definition '$policyName' exists" -ForegroundColor Green
            } else {
                Write-Host "✗ Policy Definition '$policyName' not found" -ForegroundColor Red
                $policiesValid = $false
            }
        } catch {
            Write-Host "✗ Error checking Policy Definition '$policyName': $($_.Exception.Message)" -ForegroundColor Red
            $policiesValid = $false
        }
    }
    
    # Validate policy set definitions (initiatives)
    Write-Host "`nValidating Policy Set Definitions..." -ForegroundColor Yellow
    $expectedPolicySets = @(
        "Enforce-ALZ-Sandbox",
        "Enforce-Guardrails-Compute"
    )
    
    $policySetsValid = $true
    
    foreach ($policySetName in $expectedPolicySets) {
        try {
            $policySet = Get-AzPolicySetDefinition -Name $policySetName -ManagementGroupName $ManagementGroupPrefix -ErrorAction SilentlyContinue
            if ($policySet) {
                Write-Host "✓ Policy Set Definition '$policySetName' exists" -ForegroundColor Green
            } else {
                Write-Host "✗ Policy Set Definition '$policySetName' not found" -ForegroundColor Red
                $policySetsValid = $false
            }
        } catch {
            Write-Host "✗ Error checking Policy Set Definition '$policySetName': $($_.Exception.Message)" -ForegroundColor Red
            $policySetsValid = $false
        }
    }
    
    # Validate policy assignments
    Write-Host "`nValidating Policy Assignments..." -ForegroundColor Yellow
    $expectedAssignments = @(
        @{ Name = "Audit-Tags-RG"; Scope = "/providers/Microsoft.Management/managementGroups/$ManagementGroupPrefix" },
        @{ Name = "Audit-PrivateDns-Zones"; Scope = "/providers/Microsoft.Management/managementGroups/$ManagementGroupPrefix-LandingZones" },
        @{ Name = "Deploy-SQL-Security"; Scope = "/providers/Microsoft.Management/managementGroups/$ManagementGroupPrefix-lz-prd" },
        @{ Name = "Enforce-Sandbox-Policies"; Scope = "/providers/Microsoft.Management/managementGroups/$ManagementGroupPrefix-Sandbox" },
        @{ Name = "Enforce-Compute-Guardrails"; Scope = "/providers/Microsoft.Management/managementGroups/$ManagementGroupPrefix-lz-prd" }
    )
    
    $assignmentsValid = $true
    
    foreach ($assignment in $expectedAssignments) {
        try {
            $policyAssignment = Get-AzPolicyAssignment -Name $assignment.Name -Scope $assignment.Scope -ErrorAction SilentlyContinue
            if ($policyAssignment) {
                Write-Host "✓ Policy Assignment '$($assignment.Name)' exists at scope '$($assignment.Scope)'" -ForegroundColor Green
            } else {
                Write-Host "✗ Policy Assignment '$($assignment.Name)' not found at scope '$($assignment.Scope)'" -ForegroundColor Red
                $assignmentsValid = $false
            }
        } catch {
            Write-Host "✗ Error checking Policy Assignment '$($assignment.Name)': $($_.Exception.Message)" -ForegroundColor Red
            $assignmentsValid = $false
        }
    }
    
    # Summary
    Write-Host "`n" + "="*60 -ForegroundColor Cyan
    Write-Host "VALIDATION SUMMARY" -ForegroundColor Cyan
    Write-Host "="*60 -ForegroundColor Cyan
    
    if ($mgmtGroupsValid) {
        Write-Host "✓ Management Groups: PASSED" -ForegroundColor Green
    } else {
        Write-Host "✗ Management Groups: FAILED" -ForegroundColor Red
    }
    
    if ($policiesValid) {
        Write-Host "✓ Policy Definitions: PASSED" -ForegroundColor Green
    } else {
        Write-Host "✗ Policy Definitions: FAILED" -ForegroundColor Red
    }
    
    if ($policySetsValid) {
        Write-Host "✓ Policy Set Definitions: PASSED" -ForegroundColor Green
    } else {
        Write-Host "✗ Policy Set Definitions: FAILED" -ForegroundColor Red
    }
    
    if ($assignmentsValid) {
        Write-Host "✓ Policy Assignments: PASSED" -ForegroundColor Green
    } else {
        Write-Host "✗ Policy Assignments: FAILED" -ForegroundColor Red
    }
    
    $overallValid = $mgmtGroupsValid -and $policiesValid -and $policySetsValid -and $assignmentsValid
    
    Write-Host "`nOVERALL VALIDATION: " -NoNewline
    if ($overallValid) {
        Write-Host "PASSED" -ForegroundColor Green
        Write-Host "All components have been successfully deployed and validated!" -ForegroundColor Green
    } else {
        Write-Host "FAILED" -ForegroundColor Red
        Write-Host "Some components are missing or incorrectly configured. Please review the errors above." -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "Error occurred during validation:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    exit 1
}

Write-Host "`nValidation completed!" -ForegroundColor Green
