{"name": "Enforce-Guardrails-EventGrid", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Event Grid", "description": "This policy initiative is a group of policies that ensures Event Grid is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Event Grid", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"eventGridLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "eventGridPartnerNamespaceLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "eventGridPartnerNamespaceModifyLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "eventGridTopicLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "eventGridTopicModifyLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "eventGridDomainModifyLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "eventGridDomainModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "eventGridTopicModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2dd0e8b9-4289-4bb0-b813-1883298e9924", "policyDefinitionReferenceId": "Modify-EventGrid-Partner-Namespace-Local-Auth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('eventGridPartnerNamespaceModifyLocalAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8ac2748f-3bf1-4c02-a3b6-92ae68cf75b1", "policyDefinitionReferenceId": "Modify-EventGrid-Domain-Local-Auth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('eventGridDomainModifyLocalAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ae9fb87f-8a17-4428-94a4-8135d431055c", "policyDefinitionReferenceId": "Deny-EventGrid-Topic-Local-Auth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('eventGridTopicLocalAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1c8144d9-746a-4501-b08c-093c8d29ad04", "policyDefinitionReferenceId": "Modify-EventGrid-Topic-Local-Auth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('eventGridTopicModifyLocalAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8632b003-3545-4b29-85e6-b2b96773df1e", "policyDefinitionReferenceId": "Deny-EventGrid-Partner-Namespace-Local-Auth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('eventGridPartnerNamespaceLocalAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8bfadddb-ee1c-4639-8911-a38cb8e0b3bd", "policyDefinitionReferenceId": "Deny-EventGrid-Local-Auth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('eventGridLocalAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/898e9824-104c-4965-8e0e-5197588fa5d4", "policyDefinitionReferenceId": "Modify-EventGrid-Domain-Public-Network-Access", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('eventGridDomainModifyPublicNetworkAccess')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/36ea4b4b-0f7f-4a54-89fa-ab18f555a172", "policyDefinitionReferenceId": "Modify-EventGrid-Topic-Public-Network-Access", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('eventGridTopicModifyPublicNetworkAccess')]"}}}], "policyDefinitionGroups": null}}