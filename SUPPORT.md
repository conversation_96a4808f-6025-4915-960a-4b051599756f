# Support

## Microsoft Support Policy

If issues are encountered when deploying these reference implementations users will be able to engage Microsoft support via their usual channels. Please provide correlation IDs where possible when contacting support to be able to investigate issue effectively and in timely fashion. For instruction on how to get deployments and correlation ID, please follow this link [here](https://learn.microsoft.com/azure/azure-resource-manager/templates/deployment-history?tabs=azure-portal#get-deployments-and-correlation-id).

Following list of issues are within the scope of Microsoft support:

- Portal deployment of Reference implementations - WingTip, Contoso, AdventureWorks and Trey Research
- Underlying Resource or Resource Provider issues when deploying template (e.g. Management Groups, Policies, Log Analytics Workspace, Virtual WAN, Virtual Network) for any deployment failure
- Subscription Creation via portal experience
- UI elements (e.g., ArmApiController) used in ARM Template
- ARM Deployment Issues e.g. template validation, CheckAccess API etc.
- Customization of Portal Template/Policy and UI Definition by customer/partners

Any issues that deemed outside of the above list by Microsoft support and/or requires bugfix in the Template or Code in the repo, Microsoft support will redirect user to file the issue on GitHub.

Project maintainers and community aim to get issues resolved in timely fashion as per community support policy of this repo.

## Community Support Policy

Project maintainers will aim to respond within 3 business days to get a meaningful response for any new issues.

## How to file issues and get help

This project uses GitHub Issues to track bugs and feature requests. Please search the existing issues before filing new issues to avoid duplicates.  For new issues, file your bug or feature request as a new Issue.

For help and questions about using this project, please submit a Github issue with corresponding [Issue Labels found here](https://github.com/Azure/Enterprise-Scale/labels).
