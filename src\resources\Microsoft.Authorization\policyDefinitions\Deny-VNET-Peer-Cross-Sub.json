{"name": "Deny-VNET-Peer-Cross-Sub", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Deny vNet peering cross subscription.", "description": "This policy denies the creation of vNet Peerings outside of the same subscriptions under the assigned scope.", "metadata": {"version": "1.1.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "allowedVnets": {"type": "Array", "metadata": {"displayName": "Allowed vNets to peer with", "description": "Array of allowed vNets that can be peered with. Must be entered using their resource ID. Example: /subscriptions/{subId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Network/virtualNetworks/{vnetName}"}, "defaultValue": []}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/virtualNetworks/virtualNetworkPeerings"}, {"allOf": [{"field": "Microsoft.Network/virtualNetworks/virtualNetworkPeerings/remoteVirtualNetwork.id", "notIn": "[[parameters('allowedVnets')]"}, {"field": "Microsoft.Network/virtualNetworks/virtualNetworkPeerings/remoteVirtualNetwork.id", "notLike": "[[concat(subscription().id, '/*')]"}]}]}, "then": {"effect": "[[parameters('effect')]"}}}}