{"name": "Enforce-Guardrails-DataFactory", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Data Factory", "description": "This policy initiative is a group of policies that ensures Data Factory is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Data Factory", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"adfSqlIntegration": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adfLinkedServiceKeyVault": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adfGit": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adfManagedIdentity": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "adfModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f78ccdb4-7bf4-4106-8647-270491d2978a", "policyDefinitionReferenceId": "<PERSON><PERSON>-<PERSON><PERSON>-Managed-Identity", "definitionVersion": "2.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('adfManagedIdentity')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/77d40665-3120-4348-b539-3192ec808307", "policyDefinitionReferenceId": "<PERSON>y-<PERSON><PERSON><PERSON><PERSON><PERSON>", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('adfGit')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/127ef6d7-242f-43b3-9eef-947faf1725d0", "policyDefinitionReferenceId": "Deny-Adf-Linked-Service-Key-Vault", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('adfLinkedServiceKeyVault')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0088bc63-6dee-4a9c-9d29-91cfdc848952", "policyDefinitionReferenceId": "Deny-Adf-Sql-Integration", "definitionVersion": "2.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('adfSqlIntegration')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/08b1442b-7789-4130-8506-4f99a97226a7", "policyDefinitionReferenceId": "Modify-Adf-Public-Network-Access", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('adfModifyPublicNetworkAccess')]"}}}], "policyDefinitionGroups": null}}