{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"effect": {"type": "string", "allowedValues": ["<PERSON><PERSON>", "Audit", "Disabled"], "defaultValue": "Audit"}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}}, "variables": {"policyDefinitions": {"privateSubnet": "/providers/Microsoft.Authorization/policyDefinitions/7bca8353-aa3b-429b-904a-9229c4385837", "policyVersion": "1.*.*"}, "policyAssignmentNames": {"privateSubnet": "Enforce-Subnet-Private", "description": "Ensure your subnets are secure by default by preventing default outbound access. For more information go to https://aka.ms/defaultoutboundaccessretirement", "displayName": "Subnets should be private"}, "nonComplianceMessage": {"message": "Subnets {enforcementMode} be private.", "Default": "must", "DoNotEnforce": "should"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').privateSubnet]", "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').privateSubnet]", "definitionVersion": "[variables('policyDefinitions').policyVersion]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {"effect": {"value": "[parameters('effect')]"}}}}], "outputs": {}}