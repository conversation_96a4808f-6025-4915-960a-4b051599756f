{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}, "assignmentIndex": {"type": "int", "defaultValue": 0}, "ddosPlanResourceId": {"type": "string", "defaultValue": ""}}, "variables": {"policyDefinitions": {"enforceGuardrailsNetwork": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Enforce-Guardrails-Network_20250326')]"}, "policyAssignmentNames": {"enforceGuardrailsNetwork": "[concat('Enforce-GR-Network', parameters('assignmentIndex'))]", "description": "This initiative assignment enables additional ALZ guardrails for Network and Networking services.", "displayName": "Enforce recommended guardrails for Network and Networking services"}, "nonComplianceMessage": {"message": "Recommended guardrails {enforcementMode} be enforced for Network and Networking services.", "Default": "must", "DoNotEnforce": "should"}, "rbacContributor": "b24988ac-6180-42a0-ab88-20f7382dd24c", "roleAssignmentNames": {"deployRoles": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').enforceGuardrailsNetwork))]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentNames').enforceGuardrailsNetwork]", "location": "[deployment().location]", "identity": {"type": "SystemAssigned"}, "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').enforceGuardrailsNetwork]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {"ddosPlanResourceId": {"value": "[parameters('ddosPlanResourceId')]"}}, "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}]}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').deployRoles]", "dependsOn": ["[resourceId('Microsoft.Authorization/policyAssignments', variables('policyAssignmentNames').enforceGuardrailsNetwork)]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').enforceGuardrailsNetwork), '2019-09-01', 'Full' ).identity.principalId)]"}}], "outputs": {}}