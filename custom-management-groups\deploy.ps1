# Custom Management Group Deployment Script
# This script deploys the custom management group structure with Azure Landing Zone policies

param(
    [Parameter(Mandatory = $false)]
    [string]$ManagementGroupPrefix = "EWH",
    
    [Parameter(Mandatory = $false)]
    [string]$Location = "East US",
    
    [Parameter(Mandatory = $false)]
    [bool]$EnablePolicyDeployment = $true,
    
    [Parameter(Mandatory = $false)]
    [string]$EnforcementMode = "Default"
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "Starting Custom Management Group Deployment..." -ForegroundColor Green
Write-Host "Management Group Prefix: $ManagementGroupPrefix" -ForegroundColor Yellow
Write-Host "Location: $Location" -ForegroundColor Yellow
Write-Host "Enable Policy Deployment: $EnablePolicyDeployment" -ForegroundColor Yellow

try {
    # Check if user is logged in to Azure
    $context = Get-AzContext
    if (-not $context) {
        Write-Host "Please login to Azure first using Connect-AzAccount" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Current Azure Context: $($context.Account.Id)" -ForegroundColor Green
    
    # Get current directory
    $scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
    $templatePath = Join-Path $scriptPath "main-template.json"
    $parametersPath = Join-Path $scriptPath "parameters\main.parameters.json"
    
    # Check if template files exist
    if (-not (Test-Path $templatePath)) {
        Write-Host "Template file not found: $templatePath" -ForegroundColor Red
        exit 1
    }
    
    if (-not (Test-Path $parametersPath)) {
        Write-Host "Parameters file not found: $parametersPath" -ForegroundColor Red
        exit 1
    }
    
    # Create deployment name
    $deploymentName = "CustomMgmtGroups-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    
    Write-Host "Deploying management groups..." -ForegroundColor Yellow
    Write-Host "Deployment Name: $deploymentName" -ForegroundColor Yellow
    
    # Deploy the template
    $deployment = New-AzTenantDeployment `
        -Name $deploymentName `
        -Location $Location `
        -TemplateFile $templatePath `
        -TemplateParameterFile $parametersPath `
        -topLevelManagementGroupPrefix $ManagementGroupPrefix `
        -enablePolicyDeployment $EnablePolicyDeployment `
        -Verbose
    
    if ($deployment.ProvisioningState -eq "Succeeded") {
        Write-Host "Deployment completed successfully!" -ForegroundColor Green
        
        # Display management group structure
        Write-Host "`nManagement Group Structure Created:" -ForegroundColor Green
        Write-Host "└── $ManagementGroupPrefix (EWH - Enterprise-Wide Hub)" -ForegroundColor Cyan
        Write-Host "    ├── $ManagementGroupPrefix-Platform" -ForegroundColor Cyan
        Write-Host "    │   ├── $ManagementGroupPrefix-Platform-Management" -ForegroundColor Cyan
        Write-Host "    │   └── $ManagementGroupPrefix-Platform-Connectivity" -ForegroundColor Cyan
        Write-Host "    ├── $ManagementGroupPrefix-LandingZones" -ForegroundColor Cyan
        Write-Host "    │   ├── $ManagementGroupPrefix-lz-prd" -ForegroundColor Cyan
        Write-Host "    │   │   ├── $ManagementGroupPrefix-lz-prd-legacy" -ForegroundColor Cyan
        Write-Host "    │   │   └── $ManagementGroupPrefix-lz-prd-microwave" -ForegroundColor Cyan
        Write-Host "    │   └── $ManagementGroupPrefix-lz-non-prd" -ForegroundColor Cyan
        Write-Host "    │       ├── $ManagementGroupPrefix-lz-non-prd-uat" -ForegroundColor Cyan
        Write-Host "    │       └── $ManagementGroupPrefix-lz-non-prd-dev" -ForegroundColor Cyan
        Write-Host "    ├── $ManagementGroupPrefix-Decommissioned" -ForegroundColor Cyan
        Write-Host "    └── $ManagementGroupPrefix-Sandbox" -ForegroundColor Cyan
        
        if ($EnablePolicyDeployment) {
            Write-Host "`nPolicies Deployed:" -ForegroundColor Green
            Write-Host "✓ Audit mandatory tags on resource groups" -ForegroundColor Green
            Write-Host "✓ Audit Private Link DNS Zones" -ForegroundColor Green
            Write-Host "✓ Deploy SQL Security Alert Policies" -ForegroundColor Green
            Write-Host "✓ Enforce Sandbox Policies" -ForegroundColor Green
            Write-Host "✓ Enforce Compute Guardrails" -ForegroundColor Green
        }
        
        Write-Host "`nNext Steps:" -ForegroundColor Yellow
        Write-Host "1. Move subscriptions to appropriate management groups" -ForegroundColor White
        Write-Host "2. Review and adjust policy assignments as needed" -ForegroundColor White
        Write-Host "3. Configure additional policies based on requirements" -ForegroundColor White
        
    } else {
        Write-Host "Deployment failed with state: $($deployment.ProvisioningState)" -ForegroundColor Red
        Write-Host "Error details: $($deployment.Error)" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "Error occurred during deployment:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    exit 1
}

Write-Host "`nDeployment completed!" -ForegroundColor Green
