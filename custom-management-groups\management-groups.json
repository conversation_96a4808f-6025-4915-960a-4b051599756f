{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"description": "Creates custom management group structure"}, "parameters": {"managementGroups": {"type": "object", "metadata": {"description": "Object containing all management group names"}}}, "resources": [{"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').root]", "properties": {"displayName": "EWH (Enterprise-Wide Hub)", "details": {"parent": {"id": "/"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').platform]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').root)]"], "properties": {"displayName": "Platform", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').root)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').platformManagement]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').platform)]"], "properties": {"displayName": "mg-Platform-Management", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').platform)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').platformConnectivity]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').platform)]"], "properties": {"displayName": "mg-Platform-Connectivity", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').platform)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').landingZones]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').root)]"], "properties": {"displayName": "Landing Zones", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').root)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').lzPrd]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').landingZones)]"], "properties": {"displayName": "lz-prd", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').landingZones)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').lzNonPrd]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').landingZones)]"], "properties": {"displayName": "lz-non-prd", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').landingZones)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').lzPrdLegacy]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').lzPrd)]"], "properties": {"displayName": "lz-prd-legacy", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').lzPrd)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').lzPrdMicrowave]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').lzPrd)]"], "properties": {"displayName": "lz-prd-microwave", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').lzPrd)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').lzNonPrdUat]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').lzNonPrd)]"], "properties": {"displayName": "lz-non-prd-uat", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').lzNonPrd)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').lzNonPrdDev]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').lzNonPrd)]"], "properties": {"displayName": "lz-non-prd-dev", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').lzNonPrd)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').decommissioned]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').root)]"], "properties": {"displayName": "Decommissioned", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').root)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2023-04-01", "name": "[parameters('managementGroups').sandbox]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').root)]"], "properties": {"displayName": "Sandbox", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').root)]"}}}}]}