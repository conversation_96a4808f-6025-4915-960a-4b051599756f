{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "topLevelManagementGroupPrefix": {
            "type": "string",
            "maxLength": 10,
            "metadata": {
                "description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale."
            }
        },
        "addressPrefix": {
            "type": "string",
            "metadata": {
                "displayName": "addressPrefix",
                "description": "Address prefix of the HUB"
            }
        },
        "location": {
            "type": "string",
            "metadata": {
                "displayName": "location",
                "description": "Location of the HUB"
            },
            "defaultValue": "[deployment().location]"
        },
        "enableHub": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "enableAzFw": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "enableAzFwDnsProxy": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No",
            "metadata": {
                "description": "Select whether the Azure Firewall should be used as DNS Proxy or not."
            }
        },
        "enableVpnGw": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "enableErGw": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "enableDdoS": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "metadata": {
                "description": "Select whether the DDoS Network Protection should be enabled or not."
            }
        },
        "connectivitySubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "maxLength": 36
        },
        "vpnGwType": {
            "type": "string",
            "allowedValues": [
                "PolicyBased",
                "RouteBased"
            ],
            "defaultValue": "RouteBased"
        },
        "subnetMaskForAzFw": {
            "type": "string",
            "defaultValue": ""
        },
        "subnetMaskForGw": {
            "type": "string",
            "defaultValue": ""
        },
        "firewallZones": {
            "type": "array",
            "defaultValue": []
        },
        "gwRegionalOrAz": {
            "type": "string",
            "defaultValue": ""
        },
        "enableVpnActiveActive": {
            "type": "string",
            "defaultValue": ""
        },
        "gwAzSku": {
            "type": "string",
            "defaultValue": ""
        },
        "gwRegionalSku": {
            "type": "string",
            "defaultValue": ""
        },
        "erRegionalOrAz": {
            "type": "string",
            "defaultValue": ""
        },
        "erAzSku": {
            "type": "string",
            "defaultValue": ""
        },
        "erRegionalSku": {
            "type": "string",
            "defaultValue": ""
        }
    },
    "variables": {
        "policyDefinitions": {
            "dDoSPolicyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/94de2ad3-e0c1-4caf-ad78-5d47bbc83d3d"
        },
        "policyAssignmentNames": {
            "deployDdoS": "Enable-DDoS-VNET"
        },
        "roleAssignmentNames": {
            "deployDdoS": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').deployDdoS, 'platform'))]"
        },
        "rbacNetworkContributor": "4d97b98b-1d4f-4787-a291-c67834d212e7",
        "vpngwname": "[concat(parameters('topLevelManagementGroupPrefix'), '-vpngw-', parameters('location'))]",
        "erGwName": "[concat(parameters('topLevelManagementGroupPrefix'), '-ergw-', parameters('location'))]",
        "rgName": "[concat(parameters('topLevelManagementGroupPrefix'), '-connectivity')]",
        "dDoSRgName": "[concat(parameters('topLevelManagementGroupPrefix'), '-ddos')]",
        "dDoSName": "[concat(parameters('topLevelManagementGroupPrefix'), '-ddos-', parameters('location'))]",
        "azFwPolicyName": "[concat(parameters('topLevelManagementGroupPrefix'), '-azfwpolicy-', parameters('location'))]",
        "hubName": "[concat(parameters('topLevelManagementGroupPrefix'), '-hub-', parameters('location'))]",
        "azVpnGwIpName": "[concat(variables('vpngwname'), '-pip')]",
        "azVpnGwAAIpName": "[concat(variables('vpngwname'), '-pip-002')]",
        "azVpnGwSubnetId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'),'/providers/Microsoft.Network/virtualNetworks/', variables('hubname'), '/subnets/GatewaySubnet')]",
        "azFwName": "[concat(parameters('topLevelManagementGroupPrefix'), '-fw-', parameters('location'))]",
        "azErGwIpName": "[concat(variables('erGwName'), '-pip')]",
        "azVpnGwPipId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'), '/providers/Microsoft.Network/publicIPAddresses/', variables('azVpnGwIpName'))]",
        "azVpnGwAAPipId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'), '/providers/Microsoft.Network/publicIPAddresses/', variables('azVpnGwAAIpName'))]",
        "azFwIpName": "[concat(variables('azFwName'), '-pip')]",
        "azErGwSubnetId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'),'/providers/Microsoft.Network/virtualNetworks/', variables('hubname'), '/subnets/GatewaySubnet')]",
        "azErGwPipId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'), '/providers/Microsoft.Network/publicIPAddresses/', variables('azErGwIpName'))]",
        "azFwSubnetId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'),'/providers/Microsoft.Network/virtualNetworks/', variables('hubname'), '/subnets/AzureFirewallSubnet')]",
        "azFwPipId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'), '/providers/Microsoft.Network/publicIPAddresses/', variables('azFwIpName'))]",
        "resourceDeploymentName": "[take(concat(deployment().name, '-hubspoke'), 64)]",
        "dDosResourceDeploymentName": "[take(concat(deployment().name, 'ddos', deployment().location), 64)]",
        "gwSubnet": [
            {
                "name": "GatewaySubnet",
                "properties": {
                    "addressPrefix": "[parameters('subnetMaskForGw')]"
                }
            }
        ],
        "fwSubnet": [
            {
                "name": "AzureFirewallSubnet",
                "properties": {
                    "addressPrefix": "[parameters('subnetMaskForAzFw')]"
                }
            }
        ],
        "ddosProtectionPlanId": {
            "id": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('dDoSRgName'), '/providers/Microsoft.Network/ddosProtectionPlans/', variables('dDoSName'))]"
        },
        "azFirewallPolicyId": {
            "id": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'), '/providers/Microsoft.Network/firewallPolicies/', variables('azFwPolicyName'))]"
        },
        "azFirewallDnsSettings": {
                "enableProxy": true
        }
    },
    "resources": [
        {
            "condition": "[and(equals(parameters('enableDdoS'), 'Yes'), equals(parameters('enableHub'), 'Yes'))]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2019-09-01",
            "name": "[variables('policyAssignmentNames').deployDdoS]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[variables('policyAssignmentNames').deployDdoS]",
                "displayName": "[variables('policyAssignmentNames').deployDdoS]",
                "policyDefinitionId": "[variables('policyDefinitions').dDoSPolicyDefinitionId]",
                "parameters": {
                    "ddosPlan": {
                        "value": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('dDoSRgName'), '/providers/Microsoft.Network/ddosProtectionPlans/', variables('dDoSName'))]"
                    },
                    "effect": {
                        "value": "Modify"
                    }
                }
            }
        },
        {
            "condition": "[and(equals(parameters('enableDdoS'), 'Yes'), equals(parameters('enableHub'), 'Yes'))]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployDdoS]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/policyAssignments', variables('policyAssignmentNames').deployDdoS)]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacNetworkContributor'))]",
                "principalId": "[if(and(equals(parameters('enableDdoS'), 'Yes'), equals(parameters('enableHub'), 'Yes')), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').deployDdoS), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },
        {
            // Conditionally enable DDoS Protection plan
            "condition": "[and(equals(parameters('enableDdoS'), 'Yes'), equals(parameters('enableHub'), 'Yes'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "location": "[deployment().location]",
            "name": "[concat('EntScale', 'connectivityDdoS')]",
            "subscriptionId": "[parameters('connectivitySubscriptionId')]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {},
                    "resources": [
                        {
                            "type": "Microsoft.Resources/resourceGroups",
                            "apiVersion": "2019-10-01",
                            "location": "[deployment().location]",
                            "name": "[variables('dDoSRgName')]",
                            "properties": {}
                        },
                        {
                            "type": "Microsoft.Resources/deployments",
                            "apiVersion": "2019-10-01",
                            "name": "[variables('dDosResourceDeploymentName')]",
                            "resourceGroup": "[variables('dDoSRgName')]",
                            "dependsOn": [
                                "[concat('Microsoft.Resources/resourceGroups/', variables('dDoSRgName'))]"
                            ],
                            "properties": {
                                "mode": "Incremental",
                                "template": {
                                    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
                                    "contentVersion": "*******",
                                    "parameters": {},
                                    "resources": [
                                        {
                                            "type": "Microsoft.Network/ddosProtectionPlans",
                                            "apiVersion": "2019-02-01",
                                            "name": "[variables('dDoSName')]",
                                            "location": "[parameters('location')]",
                                            "properties": {}
                                        }
                                    ]
                                }
                            }
                        }
                    ]
                }
            }
        },
        {
            "condition": "[and(equals(parameters('enableHub'), 'Yes'), not(empty(parameters('connectivitySubscriptionId'))))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "location": "[deployment().location]",
            "name": "[concat('EntScale', '-connectivityHubSub')]",
            "subscriptionId": "[parameters('connectivitySubscriptionId')]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {},
                    "resources": [
                        {
                            "type": "Microsoft.Resources/resourceGroups",
                            "apiVersion": "2019-10-01",
                            "location": "[deployment().location]",
                            "name": "[variables('rgName')]",
                            "properties": {}
                        },
                        {
                            "type": "Microsoft.Resources/deployments",
                            "apiVersion": "2019-10-01",
                            "name": "[variables('resourceDeploymentName')]",
                            "resourceGroup": "[variables('rgName')]",
                            "dependsOn": [
                                "[concat('Microsoft.Resources/resourceGroups/', variables('rgName'))]"
                            ],
                            "properties": {
                                "mode": "Incremental",
                                "template": {
                                    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
                                    "contentVersion": "*******",
                                    "parameters": {},
                                    "resources": [
                                        {
                                            "name": "[variables('hubName')]",
                                            "type": "Microsoft.Network/virtualNetworks",
                                            "apiVersion": "2020-04-01",
                                            "location": "[parameters('location')]",
                                            "properties": {
                                                "addressSpace": {
                                                    "addressPrefixes": [
                                                        "[parameters('addressPrefix')]"
                                                    ]
                                                },
                                                "subnets": "[
                                                             union(
                                                                  if(
                                                                      not(
                                                                          empty(parameters('subnetMaskForGw'))), variables('gwSubnet'), json('[]')), 
                                                                          if(
                                                                              not(
                                                                                  empty(parameters('subnetMaskForAzFw'))), variables('fwSubnet'), json('[]')))]",
                                                "enableDdosProtection": "[if(equals(parameters('enableDdoS'), 'Yes'), 'true', 'false')]",
                                                "ddosProtectionPlan": "[if(equals(parameters('enableDdoS'), 'Yes'), variables('ddosProtectionPlanId'), json('null'))]"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableVpnGw'), 'Yes'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2020-05-01",
                                            "type": "Microsoft.Network/publicIpAddresses",
                                            "location": "[parameters('location')]",
                                            "name": "[variables('azVpnGwIpName')]",
                                            "sku": {
                                                "name": "[if(equals(parameters('gwRegionalOrAz'), 'Zone'), 'Standard', 'Basic')]"
                                            }, 
                                            "properties": {
                                                "publicIPAllocationMethod": "[if(equals(parameters('gwRegionalOrAz'), 'Zone'), 'Static', 'Dynamic')]"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableVpnGw'), 'Yes'), equals(parameters('enableVpnActiveActive'),'Yes'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2020-05-01",
                                            "type": "Microsoft.Network/publicIpAddresses",
                                            "location": "[parameters('location')]",
                                            "name": "[variables('azVpnGwAAIpName')]",
                                            "sku": {
                                                "name": "[if(equals(parameters('gwRegionalOrAz'), 'Zone'), 'Standard', 'Basic')]"
                                            }, 
                                            "properties": {
                                                "publicIPAllocationMethod": "[if(equals(parameters('gwRegionalOrAz'), 'Zone'), 'Static', 'Dynamic')]"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableVpnGw'), 'Yes'), equals(parameters('enableVpnActiveActive'),'No'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2020-05-01",
                                            "name": "[variables('vpngwname')]",
                                            "type": "Microsoft.Network/virtualNetworkGateways",
                                            "location": "[parameters('location')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/publicIPAddresses/', variables('azVpnGwIpName'))]",
                                                "[concat('Microsoft.Network/virtualNetworks/', variables('hubName'))]"
                                            ],
                                            "properties": {
                                                "activeActive": false,
                                                "gatewayType": "Vpn",
                                                "vpnGatewayGeneration": "Generation2",
                                                "vpnType": "[parameters('vpnGwType')]",
                                                "ipConfigurations": [
                                                    {
                                                        "name": "default",
                                                        "properties": {
                                                            "privateIPAllocationMethod": "Dynamic",
                                                            "subnet": {
                                                                "id": "[variables('azVpnGwSubnetId')]"
                                                            },
                                                            "publicIpAddress": {
                                                                "id": "[variables('azVpnGwPipId')]"
                                                            }
                                                        }
                                                    }
                                                ],
                                                "sku": {
                                                    "name": "[if(
                                                                 and(
                                                                     or(
                                                                         empty(parameters('gwRegionalSku')), 
                                                                         empty(parameters('gwAzSku'))), 
                                                                         not(
                                                                             empty(parameters('gwRegionalSku')))), 
                                                                                parameters('gwRegionalSku'), 
                                                                                parameters('gwAzSku'))]",
                                                    "tier": "[if(
                                                                and(
                                                                    or(
                                                                        empty(parameters('gwRegionalSku')), 
                                                                        empty(parameters('gwAzSku'))), 
                                                                        not(
                                                                            empty(parameters('gwRegionalSku')))), 
                                                                                parameters('gwRegionalSku'), 
                                                                                parameters('gwAzSku'))]"
                                                }
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableVpnGw'), 'Yes'), equals(parameters('enableVpnActiveActive'),'Yes'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2020-05-01",
                                            "name": "[variables('vpngwname')]",
                                            "type": "Microsoft.Network/virtualNetworkGateways",
                                            "location": "[parameters('location')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/publicIPAddresses/', variables('azVpnGwIpName'))]",
                                                "[concat('Microsoft.Network/publicIPAddresses/', variables('azVpnGwAAIpName'))]",
                                                "[concat('Microsoft.Network/virtualNetworks/', variables('hubName'))]"
                                            ],
                                            "properties": {
                                                "activeActive": true,
                                                "gatewayType": "Vpn",
                                                "vpnGatewayGeneration": "Generation2",
                                                "vpnType": "[parameters('vpnGwType')]",
                                                "ipConfigurations": [
                                                    {
                                                        "name": "default",
                                                        "properties": {
                                                            "privateIPAllocationMethod": "Dynamic",
                                                            "subnet": {
                                                                "id": "[variables('azVpnGwSubnetId')]"
                                                            },
                                                            "publicIpAddress": {
                                                                "id": "[variables('azVpnGwPipId')]"
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "name": "activeactive",
                                                        "properties": {
                                                            "privateIPAllocationMethod": "Dynamic",
                                                            "subnet": {
                                                                "id": "[variables('azVpnGwSubnetId')]"
                                                            },
                                                            "publicIpAddress": {
                                                                "id": "[variables('azVpnGwAAPipId')]"
                                                            }
                                                        }
                                                    }
                                                ],
                                                "sku": {
                                                    "name": "[if(
                                                                 and(
                                                                     or(
                                                                         empty(parameters('gwRegionalSku')), 
                                                                         empty(parameters('gwAzSku'))), 
                                                                         not(
                                                                             empty(parameters('gwRegionalSku')))), 
                                                                                parameters('gwRegionalSku'), 
                                                                                parameters('gwAzSku'))]",
                                                    "tier": "[if(
                                                                and(
                                                                    or(
                                                                        empty(parameters('gwRegionalSku')), 
                                                                        empty(parameters('gwAzSku'))), 
                                                                        not(
                                                                            empty(parameters('gwRegionalSku')))), 
                                                                                parameters('gwRegionalSku'), 
                                                                                parameters('gwAzSku'))]"
                                                }
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableErGw'), 'Yes'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2020-05-01",
                                            "type": "Microsoft.Network/publicIpAddresses",
                                            "location": "[parameters('location')]",
                                            "name": "[variables('azErGwIpName')]",
                                            "sku": {
                                                "name": "[if(equals(parameters('erRegionalOrAz'), 'Zone'), 'Standard', 'Basic')]"
                                            },
                                            "properties": {
                                                "publicIPAllocationMethod": "[if(equals(parameters('erRegionalOrAz'), 'Zone'), 'Static', 'Dynamic')]"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableErGw'), 'Yes'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2020-05-01",
                                            "name": "[variables('erGwName')]",
                                            "type": "Microsoft.Network/virtualNetworkGateways",
                                            "location": "[parameters('location')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/publicIPAddresses/', variables('azErGwIpName'))]",
                                                "[concat('Microsoft.Network/virtualNetworkGateways/', variables('vpngwname'))]",
                                                "[concat('Microsoft.Network/virtualNetworks/', variables('hubName'))]"
                                            ],
                                            "properties": {
                                                "gatewayType": "ExpressRoute",
                                                "ipConfigurations": [
                                                    {
                                                        "name": "default",
                                                        "properties": {
                                                            "privateIPAllocationMethod": "Dynamic",
                                                            "subnet": {
                                                                "id": "[variables('azErGwSubnetId')]"
                                                            },
                                                            "publicIpAddress": {
                                                                "id": "[variables('azErGwPipId')]"
                                                            }
                                                        }
                                                    }
                                                ],
                                                "sku": {
                                                    "name": "[if(
                                                                 and(
                                                                     or(
                                                                         empty(parameters('erRegionalSku')), 
                                                                         empty(parameters('erAzSku'))), 
                                                                         not(
                                                                             empty(parameters('erRegionalSku')))), 
                                                                                parameters('erRegionalSku'), 
                                                                                parameters('erAzSku'))]",
                                                    "tier": "[if(
                                                                and(
                                                                    or(
                                                                        empty(parameters('erRegionalSku')), 
                                                                        empty(parameters('erAzSku'))), 
                                                                        not(
                                                                            empty(parameters('erRegionalSku')))), 
                                                                                parameters('erRegionalSku'), 
                                                                                parameters('erAzSku'))]"
                                                }
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableAzFw'), 'Yes'), not(empty(parameters('subnetMaskForAzFw'))))]",
                                            "apiVersion": "2020-05-01",
                                            "type": "Microsoft.Network/publicIpAddresses",
                                            "name": "[variables('azFwIpName')]",
                                            "location": "[parameters('location')]",
                                            "sku": {
                                                "name": "Standard"
                                            },
                                            "properties": {
                                                "publicIPAllocationMethod": "Static"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableAzFw'), 'Yes'), not(empty(parameters('subnetMaskForAzFw'))))]",
                                            "type": "Microsoft.Network/firewallPolicies",
                                            "apiVersion": "2020-11-01",
                                            "name": "[variables('azFwPolicyName')]",
                                            "location": "[parameters('location')]",
                                            "properties": {
                                                "dnsSettings": "[if(equals(parameters('enableAzFwDnsProxy'), 'Yes'), variables('azFirewallDnsSettings'), json('null'))]"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableAzFw'), 'Yes'), not(empty(parameters('subnetMaskForAzFw'))))]",
                                            "apiVersion": "2020-05-01",
                                            "type": "Microsoft.Network/azureFirewalls",
                                            "name": "[variables('azfwname')]",
                                            "location": "[parameters('location')]",
                                            "zones": "[if(not(empty(parameters('firewallZones'))), parameters('firewallZones'), json('null'))]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/firewallPolicies/', variables('azFwPolicyName'))]",
                                                "[concat('Microsoft.Network/publicIpAddresses/', variables('azFwIpName'))]",
                                                "[concat('Microsoft.Network/virtualNetworks/', variables('hubName'))]"
                                            ],
                                            "properties": {
                                                /*"sku": {
                                                    "Name": "AZFW_VNet",
                                                    "Tier": "Standard"
                                                },*/
                                                "ipConfigurations": [
                                                    {
                                                        "name": "[variables('azFwIpName')]",
                                                        "properties": {
                                                            "subnet": {
                                                                "id": "[variables('azFwSubnetId')]"
                                                            },
                                                            "publicIPAddress": {
                                                                "id": "[variables('azFwPipId')]"
                                                            }
                                                        }
                                                    }
                                                ],
                                                "firewallPolicy": "[variables('azFirewallPolicyId')]"
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    ]
                }
            }
        }
    ]
}