{"name": "Deny-Service-Endpoints", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Deny or Audit service endpoints on subnets", "description": "This Policy will deny/audit Service Endpoints on subnets. Service Endpoints allows the network traffic to bypass Network appliances, such as the Azure Firewall.", "metadata": {"version": "1.0.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/virtualNetworks/subnets"}, {"count": {"field": "Microsoft.Network/virtualNetworks/subnets/serviceEndpoints[*]", "where": {"field": "Microsoft.Network/virtualNetworks/subnets/serviceEndpoints[*].service", "exists": true}}, "greater": 0}]}, "then": {"effect": "[[parameters('effect')]"}}}}