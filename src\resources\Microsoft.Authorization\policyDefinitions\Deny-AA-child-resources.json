{"name": "Deny-AA-child-resources", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "No child resources in Automation Account", "description": "This policy denies the creation of child resources on the Automation Account", "metadata": {"version": "1.0.0", "category": "Automation", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "in": ["Microsoft.Automation/automationAccounts/runbooks", "Microsoft.Automation/automationAccounts/variables", "Microsoft.Automation/automationAccounts/modules", "Microsoft.Automation/automationAccounts/credentials", "Microsoft.Automation/automationAccounts/connections", "Microsoft.Automation/automationAccounts/certificates"]}]}, "then": {"effect": "[[parameters('effect')]"}}}}