{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "topLevelManagementGroupPrefix": {
            "type": "string",
            "maxLength": 10,
            "metadata": {
                "description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale."
            }
        },
        "addressPrefix": {
            "type": "string",
            "metadata": {
                "displayName": "addressPrefix",
                "description": "Address prefix of the VHUB"
            },
            "defaultValue": "**********/23"
        },
        "location": {
            "type": "string",
            "metadata": {
                "displayName": "location",
                "description": "Location of the VHUB"
            },
            "defaultValue": "[deployment().location]"
        },
        "enableHub": {
            "type": "string",
            "allowedValues": [
                "vwan",
                "No"
            ],
            "defaultValue": "No"
        },
        "enableAzFw": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "firewallSku": {
            "type": "string",
            "allowedValues": [
                "Basic",                
                "Standard",
                "Premium"
            ],
            "defaultValue": "Standard"
        },
        "enableAzFwDnsProxy": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No",
            "metadata": {
                "description": "Select whether the Azure Firewall should be used as DNS Proxy or not."
            }
        },
        "enableVpnGw": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "enableErGw": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "connectivitySubscriptionId": {
            "type": "string"
        },
        "vpnGateWayScaleUnit": {
            "type": "string",
            "defaultValue": "1"
        },
        "expressRouteScaleUnit": {
            "type": "string",
            "defaultValue": "1"
        },
        "firewallZones": {
            "type": "array",
            "defaultValue": []
        },
        "internetTrafficRoutingPolicy": {
        "type": "bool",
        "defaultValue": true,
        "metadata": {
            "description": "Enable vWAN Routing Intent and Policy for Internet Traffic"
        }
        },
        "privateTrafficRoutingPolicy": {
        "type": "bool",
        "defaultValue": true,
        "metadata": {
            "description": "Enable vWAN Routing Intent and Policy for Private Traffic"
        }
        },
        "enablevWANRoutingIntent":{
            "type": "string",
            "allowedValues":[
                "Yes",
                "No"
            ],
            "metadata": {
                "description":
                    "Enable vWAN Routing Intent"
            }
        },
        "vWANHubRoutingPreference":{
            "type": "string",
            "defaultValue": "ExpressRoute",
            "allowedValues":[
                "ExpressRoute",
                "VpnGateway",
                "ASPath"
            ],
            "metadata": {
                "description":
                    "vWAN Hub Routing Preference"
            }
        },
        "vWANHubCapacity":{
            "type": "string",
            "metadata": {
                "description":
                    "vWAN Hub Capacity Units"
                                },
            "defaultValue": "2"
        },
        "addressPrefixSecondary": {
            "type": "string",
            "metadata": {
                "displayName": "addressPrefix",
                "description": "Address prefix of the VHUB"
            },
            "defaultValue": "**********/23"
        },
        "locationSecondary": {
            "type": "string",
            "metadata": {
                "displayName": "location",
                "description": "Location of the VHUB"
            },
            "defaultValue": "[deployment().location]"
        },
        "enableHubSecondary": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "enableAzFwSecondary": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "firewallSkuSecondary": {
            "type": "string",
            "allowedValues": [
                "Basic",                
                "Standard",
                "Premium"
            ],
            "defaultValue": "Standard"
        },
        "enableAzFwDnsProxySecondary": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No",
            "metadata": {
                "description": "Select whether the Azure Firewall should be used as DNS Proxy or not."
            }
        },
        "enableVpnGwSecondary": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "enableErGwSecondary": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "vpnGateWayScaleUnitSecondary": {
            "type": "string",
            "defaultValue": "1"
        },
        "expressRouteScaleUnitSecondary": {
            "type": "string",
            "defaultValue": "1"
        },
        "firewallZonesSecondary": {
            "type": "array",
            "defaultValue": []
        },
        "internetTrafficRoutingPolicySecondary": {
        "type": "bool",
        "defaultValue": true,
        "metadata": {
            "description": "Enable vWAN Routing Intent and Policy for Internet Traffic"
        }
        },
        "privateTrafficRoutingPolicySecondary": {
        "type": "bool",
        "defaultValue": true,
        "metadata": {
            "description": "Enable vWAN Routing Intent and Policy for Private Traffic"
        }
        },
        "enablevWANRoutingIntentSecondary":{
            "type": "string",
            "allowedValues":[
                "Yes",
                "No"
            ],
            "metadata": {
                "description":
                    "Enable vWAN Routing Intent"
            }
        },
        "vWANHubRoutingPreferenceSecondary":{
            "type": "string",
            "defaultValue": "ExpressRoute",
            "allowedValues":[
                "ExpressRoute",
                "VpnGateway",
                "ASPath"
            ],
            "metadata": {
                "description":
                    "vWAN Hub Routing Preference"
            }
        },
        "vWANHubCapacitySecondary":{
            "type": "string",
            "metadata": {
                "description":
                    "vWAN Hub Capacity Units"
                                },
            "defaultValue": "2"
        }
    },
    "variables": {
        "vWanName": "[concat(parameters('topLevelManagementGroupPrefix'), '-vwan-', parameters('location'))]",
        "vpngwname": "[concat(parameters('topLevelManagementGroupPrefix'), '-vpngw-', parameters('location'))]",
        "erGwName": "[concat(parameters('topLevelManagementGroupPrefix'), '-ergw-', parameters('location'))]",
        "rgName": "[concat(parameters('topLevelManagementGroupPrefix'), '-vnethub-', parameters('location'))]",
        "vHubName": "[concat(parameters('topLevelManagementGroupPrefix'), '-hub-', parameters('location'))]",
        "azFwName": "[concat(parameters('topLevelManagementGroupPrefix'), '-fw-', parameters('location'))]",
        "azFwPolicyName": "[concat(parameters('topLevelManagementGroupPrefix'), '-azfwpolicy-', parameters('location'))]",
        "vWanSku": "Standard",
        "vwanresourceid": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/' ,variables('rgName'),'/providers/Microsoft.Network/virtualWans/', variables('vwanname'))]",
        "vwanhub": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'),'/providers/Microsoft.Network/virtualHubs/', variables('vhubname'))]",
        "vhubsku": "Standard",
        "vpnbgpasn": 65515,
        "resourceDeploymentName": "[take(concat(deployment().name, '-vwan'), 64)]",
        "azFirewallPolicyId": {
            "id": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'), '/providers/Microsoft.Network/firewallPolicies/', variables('azFwPolicyName'))]"
        },
        "azFirewallDnsSettings": {
            "enableProxy": true
        },
        "routingIntentnexthop":"[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables ('rgName'), '/providers/Microsoft.Network/azureFirewalls/', variables ('azFwName'))]",
        "vpngwnameSecondary": "[concat(parameters('topLevelManagementGroupPrefix'), '-vpngw-', parameters('locationSecondary'))]",
        "ergwnameSecondary": "[concat(parameters('topLevelManagementGroupPrefix'), '-ergw-', parameters('locationSecondary'))]",
        "vHubNameSecondary": "[concat(parameters('topLevelManagementGroupPrefix'), '-hub-', parameters('locationSecondary'))]",
        "azFwNameSecondary": "[concat(parameters('topLevelManagementGroupPrefix'), '-fw-', parameters('locationSecondary'))]",
        "azFwPolicyNameSecondary": "[concat(parameters('topLevelManagementGroupPrefix'), '-azfwpolicy-', parameters('locationSecondary'))]",
        "vwanhubSecondary": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'),'/providers/Microsoft.Network/virtualHubs/', variables('vhubnameSecondary'))]",
        "vhubskuSecondary": "Standard",
        "azFirewallPolicyIdSecondary": {
            "id": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'), '/providers/Microsoft.Network/firewallPolicies/', variables('azFwPolicyNameSecondary'))]"
        },
        "azFirewallDnsSettingsSecondary": {
            "enableProxy": true
        },
        "routingIntentnexthopSecondary":"[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables ('rgName'), '/providers/Microsoft.Network/azureFirewalls/', variables ('azFwNameSecondary'))]"
    },
    "resources": [
        {
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "location": "[parameters('location')]",
            "name": "[concat('alz-connectivityVwanSub-', parameters('location'), '-', substring(uniqueString(parameters('connectivitySubscriptionId')),0,6))]",
            "subscriptionId": "[parameters('connectivitySubscriptionId')]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {},
                    "resources": [
                        {
                            "type": "Microsoft.Resources/resourceGroups",
                            "apiVersion": "2019-10-01",
                            "location": "[parameters('location')]",
                            "name": "[variables('rgName')]",
                            "properties": {}
                        },
                        {
                            "type": "Microsoft.Resources/deployments",
                            "apiVersion": "2019-10-01",
                            "name": "[variables('resourceDeploymentName')]",
                            "resourceGroup": "[variables('rgName')]",
                            "dependsOn": [
                                "[concat('Microsoft.Resources/resourceGroups/', variables('rgName'))]"
                            ],
                            "properties": {
                                "mode": "Incremental",
                                "template": {
                                    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
                                    "contentVersion": "*******",
                                    "parameters": {},
                                    "resources": [
                                        {
                                            "type": "Microsoft.Network/virtualWans",
                                            "apiVersion": "2020-05-01",
                                            "name": "[variables('vWanName')]",
                                            "location": "[parameters('location')]",
                                            "properties": {
                                                "virtualHubs": [],
                                                "vpnSites": [],
                                                "type": "[variables('vwansku')]"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableHub'), 'vwan'), not(empty(parameters('addressPrefix'))))]",
                                            "type": "Microsoft.Network/virtualHubs",
                                            "apiVersion": "2023-04-01",
                                            "location": "[parameters('location')]",
                                            "name": "[variables('vhubname')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/virtualWans/', variables('vWanName'))]"
                                            ],
                                            "properties": {
                                                "virtualWan": {
                                                    "id": "[variables('vwanresourceid')]"
                                                },
                                                "addressPrefix": "[parameters('addressPrefix')]",
                                                "sku": "[variables('vhubsku')]",
                                                "hubRoutingPreference": "[parameters('vWANHubRoutingPreference')]",
                                                "virtualRouterAutoScaleConfiguration": {
                                                        "minCapacity": "[int(parameters('vWANHubCapacity'))]"
                                                        }
                                                }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableHub'), 'vwan'), equals(parameters('enableVpnGw'), 'Yes'))]",
                                            "type": "Microsoft.Network/vpnGateways",
                                            "apiVersion": "2020-05-01",
                                            "location": "[parameters('location')]",
                                            "name": "[variables('vpngwname')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/virtualHubs/', variables('vhubname'))]"
                                            ],
                                            "properties": {
                                                "virtualHub": {
                                                    "id": "[variables('vwanhub')]"
                                                },
                                                "bgpSettings": {
                                                    "asn": "[variables('vpnbgpasn')]"
                                                },
                                                "vpnGatewayScaleUnit": "[int(parameters('vpnGateWayScaleUnit'))]"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableHub'), 'vwan'), equals(parameters('enableErGw'), 'Yes'))]",
                                            "type": "Microsoft.Network/expressRouteGateways",
                                            "apiVersion": "2020-05-01",
                                            "location": "[parameters('location')]",
                                            "name": "[variables('ergwname')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/virtualHubs/', variables('vhubname'))]"
                                            ],
                                            "properties": {
                                                "virtualHub": {
                                                    "id": "[variables('vwanhub')]"
                                                },
                                                "autoScaleConfiguration": {
                                                    "bounds": {
                                                        "min": "[int(parameters('expressRouteScaleUnit'))]"
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            "condition": "[equals(parameters('enableAzFw'), 'Yes')]",
                                            "type": "Microsoft.Network/firewallPolicies",
                                            "apiVersion": "2020-11-01",
                                            "name": "[variables('azFwPolicyName')]",
                                            "location": "[parameters('location')]",
                                            "properties": {
                                                "dnsSettings": "[if(equals(parameters('enableAzFwDnsProxy'), 'Yes'), variables('azFirewallDnsSettings'), json('null'))]",
                                                "sku": {
                                                    "tier": "[parameters('firewallSku')]"
                                                }
                                            }
                                        },
                                        {
                                            "condition": "[equals(parameters('enableAzFw'), 'Yes')]",
                                            "apiVersion": "2020-05-01",
                                            "type": "Microsoft.Network/azureFirewalls",
                                            "name": "[variables('azfwname')]",
                                            "location": "[parameters('location')]",
                                            "zones": "[if(not(empty(parameters('firewallZones'))), parameters('firewallZones'), json('null'))]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/firewallPolicies/', variables('azFwPolicyName'))]",
                                                "[concat('Microsoft.Network/virtualHubs/', variables('vhubname'))]"
                                            ],
                                            "properties": {
                                                "sku": {
                                                    "Name": "AZFW_Hub",
                                                    "Tier": "[parameters('firewallSku')]"
                                                },
                                                "hubIPAddresses": {
                                                    "publicIPs": {
                                                        "addresses": "[json('[]')]",
                                                        "count": 1
                                                    }
                                                },
                                                "virtualHub": {
                                                    "id": "[variables('vwanhub')]"
                                                },
                                                "firewallPolicy": {
                                                    "id": "[variables('azFirewallPolicyId').id]"
                                                }
                                            }
                                        },
                                         {
                                            "condition":"[and(equals(parameters('enablevWANRoutingIntent'), 'Yes'),equals(parameters('enableAzFw'), 'Yes'))]",
                                            "type": "Microsoft.Network/virtualHubs/routingIntent",
                                            "apiVersion": "2023-04-01",
                                            "name":"[concat(variables('vhubname'),'/','RoutingIntent')]",
                                             "dependsOn": [
                                                "[concat('Microsoft.Network/virtualHubs/', variables('vhubname'))]",
                                                "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables ('rgName'), '/providers/Microsoft.Network/azureFirewalls/', variables ('azFwName'))]"
                                                ],
                                                "properties":{
                                                "routingPolicies": "[                                                     
                                                                        if(and(equals(parameters('internetTrafficRoutingPolicy'), true()), 
                                                                                equals(parameters('privateTrafficRoutingPolicy'), true())), 
                                                                                createArray(
                                                                                    createObject('name', 'PublicTraffic', 'destinations', createArray('Internet'), 'nextHop', variables('routingIntentnexthop')), 
                                                                                    createObject('name', 'PrivateTraffic', 'destinations', createArray('PrivateTraffic'), 'nextHop', variables('routingIntentnexthop'))),                                                                                     
                                                                        if(and(equals(parameters('internetTrafficRoutingPolicy'), true()), 
                                                                                equals(parameters('privateTrafficRoutingPolicy'), false())), 
                                                                                createArray(
                                                                                    createObject('name', 'PublicTraffic', 'destinations', createArray('Internet'), 'nextHop', variables('routingIntentnexthop'))), 
                                                                                createArray(
                                                                                    createObject('name', 'PrivateTraffic', 'destinations', createArray('PrivateTraffic'), 'nextHop', variables('routingIntentnexthop')))))]"
                                                }
                                        },
                                        //Begin Secondary vhub Deployment
                                        {
                                            "condition": "[and(equals(parameters('enableHubSecondary'), 'Yes'), not(empty(parameters('addressPrefixSecondary'))))]",
                                            "type": "Microsoft.Network/virtualHubs",
                                            "apiVersion": "2023-04-01",
                                            "location": "[parameters('locationSecondary')]",
                                            "name": "[variables('vHubNameSecondary')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/virtualWans/', variables('vWanName'))]"
                                            ],
                                            "properties": {
                                                "virtualWan": {
                                                    "id": "[variables('vwanresourceid')]"
                                                },
                                                "addressPrefix": "[parameters('addressPrefixSecondary')]",
                                                "sku": "[variables('vhubskuSecondary')]",
                                                "hubRoutingPreference": "[parameters('vWANHubRoutingPreferenceSecondary')]",
                                                "virtualRouterAutoScaleConfiguration": {
                                                        "minCapacity": "[int(parameters('vWANHubCapacitySecondary'))]"
                                                        }
                                                }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableHubSecondary'), 'Yes'), equals(parameters('enableVpnGwSecondary'), 'Yes'))]",
                                            "type": "Microsoft.Network/vpnGateways",
                                            "apiVersion": "2020-05-01",
                                            "location": "[parameters('locationSecondary')]",
                                            "name": "[variables('vpngwnameSecondary')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/virtualHubs/', variables('vHubNameSecondary'))]"
                                            ],
                                            "properties": {
                                                "virtualHub": {
                                                    "id": "[variables('vwanhubSecondary')]"
                                                },
                                                "bgpSettings": {
                                                    "asn": "[variables('vpnbgpasn')]"
                                                },
                                                "vpnGatewayScaleUnit": "[int(parameters('vpnGateWayScaleUnit'))]"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableHubSecondary'), 'Yes'), equals(parameters('enableErGwSecondary'), 'Yes'))]",
                                            "type": "Microsoft.Network/expressRouteGateways",
                                            "apiVersion": "2020-05-01",
                                            "location": "[parameters('locationSecondary')]",
                                            "name": "[variables('ergwnameSecondary')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/virtualHubs/', variables('vHubNameSecondary'))]"
                                            ],
                                            "properties": {
                                                "virtualHub": {
                                                    "id": "[variables('vwanhubSecondary')]"
                                                },
                                                "autoScaleConfiguration": {
                                                    "bounds": {
                                                        "min": "[int(parameters('expressRouteScaleUnitSecondary'))]"
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            "condition": "[equals(parameters('enableAzFwSecondary'), 'Yes')]",
                                            "type": "Microsoft.Network/firewallPolicies",
                                            "apiVersion": "2020-11-01",
                                            "name": "[variables('azFwPolicyNameSecondary')]",
                                            "location": "[parameters('locationSecondary')]",
                                            "properties": {
                                                "dnsSettings": "[if(equals(parameters('enableAzFwDnsProxySecondary'), 'Yes'), variables('azFirewallDnsSettingsSecondary'), json('null'))]",
                                                "sku": {
                                                    "tier": "[parameters('firewallSkuSecondary')]"
                                                }
                                            }
                                        },
                                        {
                                            "condition": "[equals(parameters('enableAzFwSecondary'), 'Yes')]",
                                            "apiVersion": "2020-05-01",
                                            "type": "Microsoft.Network/azureFirewalls",
                                            "name": "[variables('azFwNameSecondary')]",
                                            "location": "[parameters('locationSecondary')]",
                                            "zones": "[if(not(empty(parameters('firewallZonesSecondary'))), parameters('firewallZonesSecondary'), json('null'))]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/firewallPolicies/', variables('azFwPolicyNameSecondary'))]",
                                                "[concat('Microsoft.Network/virtualHubs/', variables('vhubnamesecondary'))]"
                                            ],
                                            "properties": {
                                                "sku": {
                                                    "Name": "AZFW_Hub",
                                                    "Tier": "[parameters('firewallSkuSecondary')]"
                                                },
                                                "hubIPAddresses": {
                                                    "publicIPs": {
                                                        "addresses": "[json('[]')]",
                                                        "count": 1
                                                    }
                                                },
                                                "virtualHub": {
                                                    "id": "[variables('vwanhubSecondary')]"
                                                },
                                                "firewallPolicy": {
                                                    "id": "[variables('azFirewallPolicyIdSecondary').id]"
                                                }
                                            }
                                        },
                                        {
                                            "condition":"[and(equals(parameters('enablevWANRoutingIntentSecondary'), 'Yes'),equals(parameters('enableAzFwSecondary'), 'Yes'))]",
                                            "type": "Microsoft.Network/virtualHubs/routingIntent",
                                            "apiVersion": "2023-04-01",
                                            "name":"[concat(variables('vHubNameSecondary'),'/','RoutingIntent')]",
                                             "dependsOn": [
                                                "[concat('Microsoft.Network/virtualHubs/', variables('vHubNameSecondary'))]",
                                                "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables ('rgName'), '/providers/Microsoft.Network/azureFirewalls/', variables ('azFwNameSecondary'))]"
                                                ],
                                                "properties":{
                                                "routingPolicies": "[                                                     
                                                                        if(and(equals(parameters('internetTrafficRoutingPolicySecondary'), true()), 
                                                                                equals(parameters('privateTrafficRoutingPolicySecondary'), true())), 
                                                                                createArray(
                                                                                    createObject('name', 'PublicTraffic', 'destinations', createArray('Internet'), 'nextHop', variables('routingIntentnexthopSecondary')), 
                                                                                    createObject('name', 'PrivateTraffic', 'destinations', createArray('PrivateTraffic'), 'nextHop', variables('routingIntentnexthopSecondary'))),                                                                                     
                                                                        if(and(equals(parameters('internetTrafficRoutingPolicySecondary'), true()), 
                                                                                equals(parameters('privateTrafficRoutingPolicySecondary'), false())), 
                                                                                createArray(
                                                                                    createObject('name', 'PublicTraffic', 'destinations', createArray('Internet'), 'nextHop', variables('routingIntentnexthopSecondary'))), 
                                                                                createArray(
                                                                                    createObject('name', 'PrivateTraffic', 'destinations', createArray('PrivateTraffic'), 'nextHop', variables('routingIntentnexthopSecondary')))))]"
                                                }
                                        }

                                    ]
                                }
                            }
                        }
                    ]
                }
            }
        }
    ]
}