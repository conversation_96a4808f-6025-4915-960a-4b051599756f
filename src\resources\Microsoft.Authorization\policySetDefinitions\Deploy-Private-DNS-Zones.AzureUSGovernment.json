{"name": "Deploy-Private-DNS-Zones", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Configure Azure PaaS services to use private DNS zones", "description": "This policy initiative is a group of policies that ensures private endpoints to Azure PaaS services are integrated with Azure Private DNS zones", "metadata": {"version": "1.0.1", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureUSGovernment"]}, "parameters": {"azureFilePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureFilePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureBatchPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureBatchPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureAppPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureAppPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureAsrPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureAsrPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureIotPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureIotPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureKeyVaultPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureKeyVaultPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureSignalRPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureSignalRPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureAppServicesPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureAppServicesPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureEventGridTopicsPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureEventGridTopicsPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureDiskAccessPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureDiskAccessPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureCognitiveServicesPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureCognitiveServicesPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureIotHubsPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureIotHubsPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureEventGridDomainsPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureEventGridDomainsPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureRedisCachePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureRedisCachePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureAcrPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureAcrPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureEventHubNamespacePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureEventHubNamespacePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureMachineLearningWorkspacePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureMachineLearningWorkspacePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureServiceBusNamespacePrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureServiceBusNamespacePrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "azureCognitiveSearchPrivateDnsZoneId": {"type": "string", "defaultValue": "", "metadata": {"displayName": "azureCognitiveSearchPrivateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "effect": {"type": "string", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists"}, "effect1": {"type": "string", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["deployIfNotExists", "Disabled"], "defaultValue": "deployIfNotExists"}}, "policyDefinitions": [{"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-File-Sync", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06695360-db88-47f6-b976-7500d4297475", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureFilePrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Batch", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4ec38ebc-381f-45ee-81a4-acbc4be878f8", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureBatchPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-App", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/7a860e27-9ca2-4fc6-822d-c2d248c300df", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureAppPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-Site-Recovery", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/942bd215-1a66-44be-af65-6a1c0318dbe2", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureAsrPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-IoT", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/aaa64d2d-2fa3-45e5-b332-0b031b9b30e8", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureIotPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-KeyVault", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ac673a9a-f77d-4846-b2d8-a57f8e1c01d4", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureKeyVaultPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-SignalR", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b0e86710-7fb7-4a6c-a064-32e9b829509e", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureSignalRPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-AppServices", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b318f84a-b872-429b-ac6d-a01b96814452", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureAppServicesPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-EventGridTopics", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/baf19753-7502-405f-8745-370519b20483", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureEventGridTopicsPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect1')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-DiskAccess", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bc05b96c-0b36-4ca9-82f0-5c53f96ce05a", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureDiskAccessPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-CognitiveServices", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c4bc6f10-cb41-49eb-b000-d5ab82e2a091", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureCognitiveServicesPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-IoTHubs", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c99ce9c1-ced7-4c3e-aca0-10e69ce0cb02", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureIotHubsPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect1')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-EventGridDomains", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d389df0a-e0d7-4607-833c-75a6fdac2c2d", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureEventGridDomainsPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect1')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-RedisCache", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e016b22b-e0eb-436d-8fd7-160c4eaed6e2", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureRedisCachePrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-ACR", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9585a95-5b8c-4d03-b193-dc7eb5ac4c32", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureAcrPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-EventHubNamespace", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ed66d4f5-8220-45dc-ab4a-20d1749c74e6", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureEventHubNamespacePrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-MachineLearningWorkspace", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ee40564d-486e-4f68-a5ca-7a621edae0fb", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureMachineLearningWorkspacePrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-ServiceBusNamespace", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f0fcf93c-c063-4071-9668-c47474bd3564", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureServiceBusNamespacePrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DINE-Private-DNS-Azure-CognitiveSearch", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fbc14a67-53e4-4932-abcc-2049c6706009", "parameters": {"privateDnsZoneId": {"value": "[[parameters('azureCognitiveSearchPrivateDnsZoneId')]"}, "effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}