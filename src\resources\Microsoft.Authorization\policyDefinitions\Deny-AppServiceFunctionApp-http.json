{"name": "Deny-AppServiceFunctionApp-http", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Function App should only be accessible over HTTPS", "description": "Use of HTTPS ensures server/service authentication and protects data in transit from network layer eavesdropping attacks.", "metadata": {"version": "1.0.0", "category": "App Service", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "Disabled", "<PERSON><PERSON>"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Web/sites"}, {"field": "kind", "like": "functionapp*"}, {"field": "Microsoft.Web/sites/httpsOnly", "equals": "false"}]}, "then": {"effect": "[[parameters('effect')]"}}}}