{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}}, "variables": {"policyDefinitions": {"denySubnetWithoutNsg": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyDefinitions/Deny-Subnet-Without-Nsg')]"}, "policyAssignmentNames": {"denySubnetWithoutNsg": "Deny-Subnet-Without-Nsg", "description": "This policy denies the creation of a subnet without a Network Security Group to protect traffic across subnets.", "displayName": "Subnets should have a Network Security Group"}, "nonComplianceMessage": {"message": "Subnets {enforcementMode} have a Network Security Group.", "Default": "must", "DoNotEnforce": "should"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentNames').denySubnetWithoutNsg]", "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').denySubnetWithoutNsg]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}]}}], "outputs": {}}