---
name: Test Portal Experience

##########################################
# Start the job on push for all branches #
##########################################

# yamllint disable-line rule:truthy
on:
  pull_request_target:
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    paths:
      - "eslzArm/**.json"
      - "src/Alz.Tools/**"
      - "src/**.json"
      - "src/**.bicep"
  workflow_dispatch: {}

env:
  GITHUB_COMMIT_ID: ${{ github.event.pull_request.head.sha }}
  GITHUB_PR_ID: ${{ github.event.pull_request.id }}
  TEMP_SUBSCRIPTIONS_JSON_PATH: "./src/data/subscriptions.json"
  TEMP_DEPLOYMENT_OBJECT_PATH: "./src/data/eslzArm.test.deployment.json"
  POLICY_DIR: "src/resources/Microsoft.Authorization/policyDefinitions"
  POLICYSET_DIR: "src/resources/Microsoft.Authorization/policySetDefinitions"  

permissions:
  contents: read
  id-token: write

concurrency:
  group: test-${{ github.event.pull_request.head.repo.full_name }}/${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

###############
# Set the Job #
###############
jobs:
  test-portal:
    name: Test Portal Experience
    runs-on: ubuntu-latest
    environment: csu-rw
    if: |
      (
        github.event.pull_request.head.repo.full_name == 'Azure/Enterprise-Scale'
      )
      ||
      (
        github.event.pull_request.head.repo.full_name != 'Azure/Enterprise-Scale'
        &&
        contains(github.event.pull_request.labels.*.name, 'PR: Safe to test :test_tube:')
      )
      ||
      (
        github.event_name == 'workflow_dispatch'
      )
      ||
      (
        github.event_name == 'merge_group'
      )

    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha }}
          persist-credentials: false

      - name: Show env
        run: env | sort

      - name: List available pwsh modules
        uses: azure/powershell@v2
        with:
          inlineScript: Get-Module -ListAvailable
          azPSVersion: "latest"

      - name: Azure login (OIDC)
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
          enable-AzPSSession: true

      - name: Register subscriptions
        uses: azure/powershell@v2
        with:
          inlineScript: |
            ./src/scripts/Invoke-ActionRegisterSubscriptions.ps1
            Get-Content -Path $env:TEMP_SUBSCRIPTIONS_JSON_PATH | jq
          azPSVersion: "latest"
        env:
          BILLING_SCOPE: ${{ secrets.BILLING_SCOPE }}

      - name: Pre-process subscriptions
        uses: azure/powershell@v2
        with:
          inlineScript: ./src/scripts/Invoke-ActionRemoveOrphanedRBAC.ps1
          azPSVersion: "latest"

      - name: Check test label set
        if: |
          ${{ contains(github.event.pull_request.labels.*.name, 'Test: Standard') || contains(github.event.pull_request.labels.*.name, 'Test: Hub & Spoke')  || contains(github.event.pull_request.labels.*.name, 'Test: VWAN') }}
        run: echo "Test label has been set, test can proceed."

      - name: Generate eslzArm configuration (Standard)
        if: |
          ${{ contains(github.event.pull_request.labels.*.name, 'Test: Standard') }}
        uses: azure/powershell@v2
        with:
          inlineScript: |
            ./src/scripts/Invoke-ActionGenerateEslzArmConfig.ps1 -TemplateParameterPath "./eslzArm/eslzArm.test.param.std.json"
            Get-Content -Path $env:TEMP_DEPLOYMENT_OBJECT_PATH | jq
          azPSVersion: "latest"
        env:
          DEPLOYMENT_LOCATION: ${{ secrets.DEPLOYMENT_LOCATION }}

      - name: Generate eslzArm configuration (Hub & Spoke)
        if: |
          ${{ contains(github.event.pull_request.labels.*.name, 'Test: Hub & Spoke') }}
        uses: azure/powershell@v2
        with:
          inlineScript: |
            ./src/scripts/Invoke-ActionGenerateEslzArmConfig.ps1 -TemplateParameterPath "./eslzArm/eslzArm.test.param.hns.json"
            Get-Content -Path $env:TEMP_DEPLOYMENT_OBJECT_PATH | jq
          azPSVersion: "latest"
        env:
          DEPLOYMENT_LOCATION: ${{ secrets.DEPLOYMENT_LOCATION }}

      - name: Generate eslzArm configuration (VWAN)
        if: |
          ${{ contains(github.event.pull_request.labels.*.name, 'Test: VWAN') }}
        uses: azure/powershell@v2
        with:
          inlineScript: |
            ./src/scripts/Invoke-ActionGenerateEslzArmConfig.ps1 -TemplateParameterPath "./eslzArm/eslzArm.test.param.vwan.json"
            Get-Content -Path $env:TEMP_DEPLOYMENT_OBJECT_PATH | jq
          azPSVersion: "latest"
        env:
          DEPLOYMENT_LOCATION: ${{ secrets.DEPLOYMENT_LOCATION }}

      - name: Run eslzArm deployment (TEST)
        uses: azure/powershell@v2
        with:
          inlineScript: ./src/scripts/Invoke-ActionRunEslzArmDeployment.ps1 -Test
          azPSVersion: "latest"

      - name: Run eslzArm deployment (WHAT IF)
        uses: azure/powershell@v2
        with:
          inlineScript: ./src/scripts/Invoke-ActionRunEslzArmDeployment.ps1 -WhatIf
          azPSVersion: "latest"

      - name: Run eslzArm deployment (DEPLOY)
        uses: azure/powershell@v2
        with:
          inlineScript: ./src/scripts/Invoke-ActionRunEslzArmDeployment.ps1
          azPSVersion: "latest"

      - name: Install PowerShell modules
        shell: pwsh
        run: |
          Install-Module -Name "Az" -RequiredVersion "10.1.0" -Force -Scope CurrentUser -ErrorAction Stop
          Update-AzConfig -DisplayBreakingChangeWarning $false

      - name: Pester Test for Policies
        uses: azure/powershell@v2
        with:
          inlineScript: ./.github/actions-pester/PTF-TestPolicies.ps1
          azPSVersion: "latest"
        env:
          SUBSCRIPTION_ID: ${{ secrets.AZURE_POLICY_SUBSCRIPTION1_ID }}
          SUBSCRIPTION2_ID: ${{ secrets.AZURE_POLICY_SUBSCRIPTION2_ID }} #Used for policy tests that require a second subscription (e.g. cross subscription peering)
          TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}          

      - name: Run eslzArm deployment (DESTROY)
        uses: azure/powershell@v2
        with:
          inlineScript: ./src/scripts/Invoke-ActionRunEslzArmDeployment.ps1 -Destroy
          azPSVersion: "9.4.0"
