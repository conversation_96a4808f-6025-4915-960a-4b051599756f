{"name": "6fca939a-1b08-420b-affd-3d3061ecceb2", "type": "Microsoft.Authorization/roleDefinitions", "apiVersion": "2022-04-01", "properties": {"roleName": "OssDb-Owners", "description": "Platform-wide Open Source Database Owners: PostgreSQL, MySql", "type": "customRole", "permissions": [{"actions": ["Microsoft.Authorization/*/read", "Microsoft.Insights/alertRules/*", "Microsoft.Resources/deployments/*", "Microsoft.Resources/subscriptions/resourceGroups/read", "Microsoft.DBforMySQL/*", "Microsoft.DBforPostgreSQL/*", "Microsoft.DBforMariaDB/*"], "notActions": [], "dataActions": [], "notDataActions": []}], "assignableScopes": ["/providers/Microsoft.Management/managementGroups/contoso"]}}