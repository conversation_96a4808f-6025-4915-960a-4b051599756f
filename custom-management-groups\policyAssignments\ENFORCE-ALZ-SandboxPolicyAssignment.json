{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}, "listOfResourceTypesNotAllowed": {"type": "Array", "defaultValue": ["microsoft.network/expressroutecircuits", "microsoft.network/expressroutegateways", "microsoft.network/expressrouteports", "microsoft.network/virtualwans", "microsoft.network/virtualhubs", "microsoft.network/vpngateways", "microsoft.network/p2svpngateways", "microsoft.network/vpnsites", "microsoft.network/virtualnetworkgateways"]}}, "variables": {"policyDefinitions": {"enforceAlzSandbox": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Enforce-ALZ-Sandbox')]"}, "policyAssignmentNames": {"alzSandbox": "Enforce-ALZ-Sandbox", "description": "This initiative will help enforce and govern subscriptions that are placed within the Sandbox Management Group. See https://aka.ms/alz/policies for more information.", "displayName": "Enforce ALZ Sandbox Guardrails"}, "nonComplianceMessage": {"message": "ALZ Sandbox Guardrails {enforcementMode} be enforced.", "Default": "must", "DoNotEnforce": "should"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentNames').alzSandbox]", "location": "[deployment().location]", "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').enforceAlzSandbox]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {"listOfResourceTypesNotAllowed": {"value": "[parameters('listOfResourceTypesNotAllowed')]"}}}}], "outputs": {}}