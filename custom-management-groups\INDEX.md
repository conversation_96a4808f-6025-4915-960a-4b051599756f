# Custom Management Groups - File Index

## 📁 Main Files

| File | Description | Purpose |
|------|-------------|---------|
| `README.md` | Main documentation | Overview và hướng dẫn sử dụng |
| `STRUCTURE.md` | Management group structure | Chi tiết về cấu trúc và mục đích |
| `INDEX.md` | This file | Navigation guide |

## 🏗️ ARM Templates

| File | Description | Usage |
|------|-------------|-------|
| `main-template.json` | Master template | Entry point cho deployment |
| `management-groups.json` | Management groups creation | Tạo management group hierarchy |
| `policy-definitions.json` | Core policy definitions | Basic policies từ ALZ |
| `policy-assignments.json` | Core policy assignments | Basic policy assignments |
| `policy-assignments-extended.json` | Extended policy assignments | Comprehensive ALZ policies |

## ⚙️ Parameters

| File | Description | Usage |
|------|-------------|-------|
| `parameters/main.parameters.json` | Main parameters | Default deployment parameters |

## 🚀 Deployment Scripts

| File | Platform | Description |
|------|----------|-------------|
| `deploy.ps1` | PowerShell | Windows deployment script |
| `deploy.sh` | Bash | Linux/Mac deployment script |
| `validate.ps1` | PowerShell | Windows validation script |
| `validate.sh` | Bash | Linux/Mac validation script |

## 📚 Documentation

| File | Description | Content |
|------|-------------|---------|
| `policies/ALZ-PolicyAssignments-Reference.md` | Policy assignments reference | Tất cả policy assignments từ ALZ |
| `policies/ALZ-PolicyDefinitions-Reference.md` | Policy definitions reference | Tất cả policy definitions từ ALZ |

## 🔧 Utility Scripts

| File | Description | Usage |
|------|-------------|-------|
| `scripts/Generate-ALZ-Policies.ps1` | Policy generator | Generate policies từ ALZ source |

## 🎯 Quick Start Guide

### 1. Basic Deployment
```bash
# PowerShell
.\deploy.ps1

# Bash  
./deploy.sh
```

### 2. Custom Deployment
```bash
# PowerShell
.\deploy.ps1 -ManagementGroupPrefix "MYORG" -Location "West Europe"

# Bash
./deploy.sh --prefix "MYORG" --location "West Europe"
```

### 3. Validation
```bash
# PowerShell
.\validate.ps1

# Bash
./validate.sh
```

### 4. Generate Extended Policies
```powershell
.\scripts\Generate-ALZ-Policies.ps1 -ALZSourcePath "C:\path\to\Enterprise-Scale"
```

## 📋 Management Group Structure

```
EWH (Enterprise-Wide Hub)
├── Platform
│   ├── mg-Platform-Management
│   └── mg-Platform-Connectivity  
├── Landing Zones
│   ├── lz-prd
│   │   ├── lz-prd-legacy
│   │   └── lz-prd-microwave
│   └── lz-non-prd
│       ├── lz-non-prd-uat
│       └── lz-non-prd-dev
├── Decommissioned
└── Sandbox
```

## 🛡️ Policy Categories

- **🔍 AUDIT**: Compliance monitoring
- **🚫 DENY**: Security enforcement  
- **🔧 DINE**: Auto-deployment
- **🛡️ ENFORCE**: Service guardrails
- **🔄 MODIFY**: Configuration changes

## 📖 Key References

1. **Azure Landing Zones**: [Official Documentation](https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/ready/landing-zone/)
2. **Enterprise-Scale**: [GitHub Repository](https://github.com/Azure/Enterprise-Scale)
3. **Azure Policy**: [Documentation](https://docs.microsoft.com/en-us/azure/governance/policy/)
4. **Management Groups**: [Documentation](https://docs.microsoft.com/en-us/azure/governance/management-groups/)

## 🔄 Update Process

1. **Update Templates**: Modify ARM templates as needed
2. **Update Parameters**: Adjust parameter files
3. **Test Deployment**: Use validation scripts
4. **Deploy Changes**: Use deployment scripts
5. **Verify Results**: Run validation again

## 🆘 Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure proper RBAC permissions
2. **Policy Conflicts**: Check existing policy assignments
3. **Naming Conflicts**: Verify management group names
4. **Template Errors**: Validate JSON syntax

### Support Resources

- Check `validate.ps1` or `validate.sh` output
- Review Azure Activity Log
- Consult ALZ documentation
- Check GitHub issues

## 📝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

---

**Last Updated**: $(Get-Date)
**Version**: 1.0.0
