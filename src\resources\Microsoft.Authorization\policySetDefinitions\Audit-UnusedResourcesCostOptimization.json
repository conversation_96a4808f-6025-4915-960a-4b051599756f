{"name": "Audit-UnusedResourcesCostOptimization", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Unused resources driving cost should be avoided", "description": "Optimize cost by detecting unused but chargeable resources. Leverage this Azure Policy Initiative as a cost control tool to reveal orphaned resources that are contributing cost.", "metadata": {"version": "2.1.0", "category": "Cost Optimization", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effectDisks": {"type": "String", "metadata": {"displayName": "Disks Effect", "description": "Enable or disable the execution of the policy for Microsoft.Compute/disks"}, "allowedValues": ["Audit", "Disabled"], "defaultValue": "Audit"}, "effectPublicIpAddresses": {"type": "String", "metadata": {"displayName": "PublicIpAddresses Effect", "description": "Enable or disable the execution of the policy for Microsoft.Network/publicIpAddresses"}, "allowedValues": ["Audit", "Disabled"], "defaultValue": "Audit"}, "effectServerFarms": {"type": "String", "metadata": {"displayName": "ServerFarms Effect", "description": "Enable or disable the execution of the policy for Microsoft.Web/serverfarms"}, "allowedValues": ["Audit", "Disabled"], "defaultValue": "Audit"}}, "policyDefinitions": [{"policyDefinitionReferenceId": "AuditDisksUnusedResourcesCostOptimization", "policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Audit-Disks-UnusedResourcesCostOptimization", "definitionVersion": "1.*.*", "parameters": {"effect": {"value": "[[parameters('effectDisks')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AuditPublicIpAddressesUnusedResourcesCostOptimization", "policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Audit-PublicIpAddresses-UnusedResourcesCostOptimization", "definitionVersion": "1.*.*", "parameters": {"effect": {"value": "[[parameters('effectPublicIpAddresses')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AuditServerFarmsUnusedResourcesCostOptimization", "policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Audit-ServerFarms-UnusedResourcesCostOptimization", "definitionVersion": "1.*.*", "parameters": {"effect": {"value": "[[parameters('effectServerFarms')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AuditAzureHybridBenefitUnusedResourcesCostOptimization", "policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Audit-AzureHybridBenefit", "definitionVersion": "1.*.*", "parameters": {"effect": {"value": "Audit"}}, "groupNames": []}], "policyDefinitionGroups": null}}