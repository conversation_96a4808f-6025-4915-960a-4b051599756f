{"name": "Enforce-Backup", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce enhanced recovery and backup policies", "description": "Enforce enhanced recovery and backup policies on assigned scopes.", "metadata": {"version": "1.1.0", "category": "Backup", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "version": "1.0.0", "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy."}, "allowedValues": ["Audit", "Disabled"], "defaultValue": "Audit"}, "checkLockedImmutabilityOnly": {"type": "Boolean", "metadata": {"displayName": "checkLockedImmutabilityOnly", "description": "This parameter checks if Immutability is locked for Backup Vaults in scope. Selecting 'true' will mark only vaults with Immutability 'Locked' as compliant. Selecting 'false' will mark vaults that have Immutability either 'Enabled' or 'Locked' as compliant."}, "allowedValues": [true, false], "defaultValue": false}, "checkAlwaysOnSoftDeleteOnly": {"type": "Boolean", "metadata": {"displayName": "CheckAlwaysOnSoftDeleteOnly", "description": "This parameter checks if Soft Delete is 'Locked' for Backup Vaults in scope. Selecting 'true' will mark only vaults with Soft Delete 'AlwaysOn' as compliant. Selecting 'false' will mark vaults that have Soft Delete either 'On' or 'AlwaysOn' as compliant."}, "allowedValues": [true, false], "defaultValue": false}}, "policyDefinitions": [{"policyDefinitionReferenceId": "BackupBVault-Immutability", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2514263b-bc0d-4b06-ac3e-f262c0979018", "definitionVersion": "1.*.*-preview", "parameters": {"effect": {"value": "[[parameters('effect')]"}, "checkLockedImmutabiltyOnly": {"value": "[[parameters('checkLockedImmutabilityOnly')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "BackupRVault-Immutability", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d6f6f560-14b7-49a4-9fc8-d2c3a9807868", "definitionVersion": "1.*.*-preview", "parameters": {"effect": {"value": "[[parameters('effect')]"}, "checkLockedImmutabilityOnly": {"value": "[[parameters('checkLockedImmutabilityOnly')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "BackupBVault-SoftDelete", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/9798d31d-6028-4dee-8643-46102185c016", "definitionVersion": "1.*.*-preview", "parameters": {"effect": {"value": "[[parameters('effect')]"}, "checkAlwaysOnSoftDeleteOnly": {"value": "[[parameters('checkAlwaysOnSoftDeleteOnly')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "BackupRVault-SoftDelete", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/31b8092a-36b8-434b-9af7-5ec844364148", "definitionVersion": "1.*.*-preview", "parameters": {"effect": {"value": "[[parameters('effect')]"}, "checkAlwaysOnSoftDeleteOnly": {"value": "[[parameters('checkAlwaysOnSoftDeleteOnly')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "BackupBVault-MUA", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c58e083e-7982-4e24-afdc-be14d312389e", "definitionVersion": "1.*.*-preview", "parameters": {"effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "BackupRVault-MUA", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c7031eab-0fc0-4cd9-acd0-4497bd66d91a", "definitionVersion": "1.*.*-preview", "parameters": {"effect": {"value": "[[parameters('effect')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}