{"name": "Deny-MachineLearning-Compute-SubnetId", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Enforce subnet connectivity for Azure Machine Learning compute clusters and compute instances", "description": "Enforce subnet connectivity for Azure Machine Learning compute clusters and compute instances.", "metadata": {"version": "1.0.0", "category": "Machine Learning", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud"]}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "Disabled", "<PERSON><PERSON>"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.MachineLearningServices/workspaces/computes"}, {"field": "Microsoft.MachineLearningServices/workspaces/computes/computeType", "in": ["AmlCompute", "ComputeInstance"]}, {"anyOf": [{"field": "Microsoft.MachineLearningServices/workspaces/computes/subnet.id", "exists": false}, {"value": "[[empty(field('Microsoft.MachineLearningServices/workspaces/computes/subnet.id'))]", "equals": true}]}]}, "then": {"effect": "[[parameters('effect')]"}}}}