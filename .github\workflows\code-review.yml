---
name: Code Review

##########################################
# Start the job on push for all branches #
##########################################

# yamllint disable-line rule:truthy
on:
  pull_request:
    branches:
      - main
      - policy-refresh
  workflow_dispatch: {}

###############
# Set the Job #
###############
jobs:
  super-linter:
    name: GitHub Super Linter
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Lint eslzArm directory
        uses: docker://github/super-linter:v4.9.5
        env:
          # Only lint changed files
          VALIDATE_ALL_CODEBASE: false
          # Need to define main branch as default
          # is set to master in super-linter
          DEFAULT_BRANCH: main
          # Enable setting the status of each individual linter
          # run in the Checks section of a pull request
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # Only the following files will be linted:
          FILTER_REGEX_INCLUDE: .*eslzArm\/.*
          # The following linter types will be enabled:
          VALIDATE_ARM: true
          VALIDATE_JSON: true
          VALIDATE_MARKDOWN: true
          # Additional settings:
          # Disable errors to only generate a report
          DISABLE_ERRORS: true

      - name: Lint src directory
        uses: docker://github/super-linter:v4.9.5
        env:
          # Only lint changed files
          VALIDATE_ALL_CODEBASE: false
          # Need to define main branch as default
          # is set to master in super-linter
          DEFAULT_BRANCH: main
          # Enable setting the status of each individual linter
          # run in the Checks section of a pull request
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # Only the following files will be linted:
          FILTER_REGEX_INCLUDE: .*src\/.*
          FILTER_REGEX_EXCLUDE: .*src\/.*\/(Deploy-Diagnostics-Firewall\.json|Deploy-FirewallPolicy\.json)
          # The following linter types will be enabled:
          VALIDATE_ARM: true
          VALIDATE_BASH: true
          VALIDATE_BASH_EXEC: true
          VALIDATE_JSON: true
          VALIDATE_MARKDOWN: true
          VALIDATE_POWERSHELL: true
          VALIDATE_YAML: true
          # Additional settings:
          # If a shell script is not executable, the bash-exec
          # linter will report an error when set to true
          ERROR_ON_MISSING_EXEC_BIT: true
