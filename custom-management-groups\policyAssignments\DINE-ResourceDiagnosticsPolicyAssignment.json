{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ prefix to your intermediate root management group containing the policy definitions."}}, "logAnalyticsResourceId": {"type": "string", "metadata": {"description": "Provide the resourceId to the central Log Analytics workspace."}}, "laCategory": {"type": "string", "metadata": {"description": "Provide the category of logs to be forwarded to Log Analytics."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}}, "variables": {"policyDefinitions": {"deployResourceDiagnostics": "[if(equals(parameters('laCategory'), 'allLogs'),'/providers/Microsoft.Authorization/policySetDefinitions/0884adba-2312-4468-abeb-5422caed1038','/providers/Microsoft.Authorization/policySetDefinitions/f5b29bc4-feca-4cc6-a58a-772dd5e290a5')]", "policyVersion": "[if(equals(parameters('laCategory'), 'allLogs'),'1.*.*','1.*.*')]"}, "policyAssignmentNames": {"resourceDiagnostics": "Deploy-Diag-LogsCat", "description": "Resource logs should be enabled to track activities and events that take place on your resources and give you visibility and insights into any changes that occur. This initiative deploys diagnostic setting using the allLogs category group to route logs to an Event Hub for all supported resources.", "displayName": "Enable category group resource logging for supported resources to Log Analytics"}, "nonComplianceMessage": {"message": "Diagnostic settings {enforcementMode} be deployed to Azure services to forward logs to Log Analytics.", "Default": "must", "DoNotEnforce": "should"}, "rbacMonitoringContributor": "749f88d5-cbae-40b8-bcfc-e573ddc772fa", "rbacLogAnalyticsContributor": "92aaf0da-9dab-42b6-94a3-d43ce8d16293", "roleAssignmentNames": {"roleAssignmentNameLogAnalyticsContributor": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').resourceDiagnostics,'-1'))]", "roleAssignmentNameMonitoringContributor": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').resourceDiagnostics,'-2'))]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').resourceDiagnostics]", "location": "[deployment().location]", "identity": {"type": "SystemAssigned"}, "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').deployResourceDiagnostics]", "definitionVersion": "[variables('policyDefinitions').policyVersion]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {"logAnalytics": {"value": "[parameters('logAnalyticsResourceId')]"}}}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').roleAssignmentNameLogAnalyticsContributor]", "dependsOn": ["[variables('policyAssignmentNames').resourceDiagnostics]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacLogAnalyticsContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').resourceDiagnostics), '2019-09-01', 'Full' ).identity.principalId)]"}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').roleAssignmentNameMonitoringContributor]", "dependsOn": ["[variables('policyAssignmentNames').resourceDiagnostics]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacMonitoringContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').resourceDiagnostics), '2019-09-01', 'Full' ).identity.principalId)]"}}], "outputs": {}}