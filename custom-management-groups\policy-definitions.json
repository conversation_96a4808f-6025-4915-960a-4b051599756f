{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"description": "Creates key policy definitions from Azure Landing Zones"}, "parameters": {"topLevelManagementGroupId": {"type": "string", "metadata": {"description": "The management group ID where policies will be created"}}}, "variables": {"scope": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupId'))]"}, "resources": [{"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "Audit-Tags-Mandatory-Rg", "properties": {"policyType": "Custom", "displayName": "Audit for mandatory tags on resource groups", "mode": "All", "description": "Audits resource groups to ensure they have required tags based on tag array.", "metadata": {"version": "1.1.0", "category": "Tags", "source": "https://github.com/Azure/Enterprise-Scale/"}, "parameters": {"tagNames": {"type": "Array", "metadata": {"displayName": "Tag Names", "description": "List of tag names to check for"}}, "effect": {"type": "String", "defaultValue": "Audit", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Resources/resourceGroups"}, {"count": {"value": "[parameters('tagNames')]", "name": "tagName", "where": {"field": "[concat('tags[', current('tagName'), ']')]", "exists": "false"}}, "greater": 0}]}, "then": {"effect": "[parameters('effect')]"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "Audit-PrivateLinkDnsZones", "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Audit or Deny the creation of Private Link Private DNS Zones", "description": "This policy audits or denies the creation of Private Link Private DNS Zones in the current scope", "metadata": {"version": "1.0.2", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/privateDnsZones"}, {"anyOf": [{"field": "name", "like": "privatelink.*"}]}]}, "then": {"effect": "[parameters('effect')]"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "Deploy-Sql-SecurityAlertPolicies", "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Deploy SQL Database security Alert Policies configuration", "description": "Deploy the security Alert Policies configuration with email admin accounts when it not exist", "metadata": {"version": "1.1.1", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/"}, "parameters": {"effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "emailAddresses": {"type": "Array", "metadata": {"displayName": "Email addresses", "description": "Email addresses for security alerts"}}}, "policyRule": {"if": {"field": "type", "equals": "Microsoft.Sql/servers"}, "then": {"effect": "[parameters('effect')]", "details": {"type": "Microsoft.Sql/servers/securityAlertPolicies", "name": "default", "existenceCondition": {"field": "Microsoft.Sql/servers/securityAlertPolicies/state", "equals": "Enabled"}, "roleDefinitionIds": ["/providers/microsoft.authorization/roleDefinitions/056cd41c-7e88-42e1-933e-88ba6a50c9c3"], "deployment": {"properties": {"mode": "incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"serverName": {"type": "string"}, "emailAddresses": {"type": "array"}}, "resources": [{"type": "Microsoft.Sql/servers/securityAlertPolicies", "apiVersion": "2020-02-02-preview", "name": "[concat(parameters('serverName'), '/default')]", "properties": {"state": "Enabled", "emailAddresses": "[parameters('emailAddresses')]", "emailAccountAdmins": true}}]}, "parameters": {"serverName": {"value": "[field('name')]"}, "emailAddresses": {"value": "[parameters('emailAddresses')]"}}}}}}}}}, {"type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "name": "Enforce-ALZ-Sandbox", "dependsOn": ["[resourceId('Microsoft.Authorization/policyDefinitions', 'Audit-Tags-Mandatory-Rg')]"], "properties": {"policyType": "Custom", "displayName": "Enforce policies in the Sandbox Landing Zone", "description": "Enforce policies in the Sandbox Landing Zone.", "metadata": {"version": "1.1.0", "category": "Sandbox", "source": "https://github.com/Azure/Enterprise-Scale/"}, "parameters": {"listOfResourceTypesNotAllowed": {"type": "Array", "defaultValue": ["Microsoft.Compute/virtualMachines", "Microsoft.Network/expressRouteCircuits", "Microsoft.Network/virtualNetworkGateways"], "metadata": {"displayName": "Not allowed resource types in the Sandbox landing zone", "description": "Not allowed resource types in the Sandbox landing zone"}}, "effectNotAllowedResources": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "effectDenyVnetPeering": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyDefinitions": [{"policyDefinitionReferenceId": "SandboxNotAllowed", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6c112d4e-5bc7-47ae-a041-ea2d9dccd749", "definitionVersion": "2.*.*", "parameters": {"effect": {"value": "[[parameters('effectNotAllowedResources')]"}, "listOfResourceTypesNotAllowed": {"value": "[[parameters('listOfResourceTypesNotAllowed')]"}}}, {"policyDefinitionReferenceId": "DenyVnetPeering", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/35f9c03a-cc27-418e-9c0c-539ff999d010", "definitionVersion": "1.*.*", "parameters": {"effect": {"value": "[[parameters('effectDenyVnetPeering')]"}}}]}}, {"type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "name": "Enforce-Guardrails-Compute", "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Compute", "description": "This policy initiative ensures Compute resources are compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Compute", "source": "https://github.com/Azure/Enterprise-Scale/"}, "parameters": {"effectVmBackup": {"type": "String", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"], "metadata": {"displayName": "Effect for VM Backup policy", "description": "Enable or disable VM backup policy"}}}, "policyDefinitions": [{"policyDefinitionReferenceId": "VmBackup", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/013e242c-8828-4970-87b3-ab247555486d", "definitionVersion": "3.*.*", "parameters": {"effect": {"value": "[[parameters('effectVmBackup')]"}}}]}}]}