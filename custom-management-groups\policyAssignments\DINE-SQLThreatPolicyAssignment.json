{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}}, "variables": {"policyDefinitions": {"deploySqlThreat": "/providers/Microsoft.Authorization/policyDefinitions/36d49e87-48c4-4f2e-beed-ba4ed02b71f5", "policyVersion": "2.*.*"}, "policyAssignmentNames": {"deploySqlThreat": "Deploy-SQL-Threat", "description": "This policy ensures that Threat Detection is enabled on SQL Servers.", "displayName": "Deploy Threat Detection on SQL servers"}, "nonComplianceMessage": {"message": "Threat Detection {enforcementMode} be deployed on SQL servers.", "Default": "must", "DoNotEnforce": "should"}, "rbacSQLSecMan": "056cd41c-7e88-42e1-933e-88ba6a50c9c3", "roleAssignmentNames": {"deploySqlThreat": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').deploySqlThreat))]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').deploySqlThreat]", "location": "[deployment().location]", "identity": {"type": "SystemAssigned"}, "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').deploySqlThreat]", "definitionVersion": "[variables('policyDefinitions').policyVersion]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}]}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').deploySqlThreat]", "dependsOn": ["[variables('policyAssignmentNames').deploySqlThreat]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacSQLSecMan'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').deploySqlThreat), '2019-09-01', 'Full' ).identity.principalId)]"}}], "outputs": {}}