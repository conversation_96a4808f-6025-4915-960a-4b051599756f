{
    "$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
      "vNetRgName": {
        "type": "String"
      },
      "vNetName": {
        "type": "String"
      },
      "vNetLocation": {
        "type": "String"
      },
      "vNetCidrRange": {
        "type": "String"
      },
      "vWanhubResourceId": {
        "type": "String"
      },
      "dnsServers": {
        "type": "array",
        "defaultValue": [],
        "metadata": {
            "description": "Provide DNS servers."
        }        
      },
      "azureFirewallResourceId": {
        "type": "string",
        "defaultValue": "",
        "metadata": {
          "description": "Provide full resourceId of Azure Firewall if used as DNS proxy"
        }
      }
    },
    "variables": {},
    "resources": [
      {
        "type": "Microsoft.Resources/deployments",
        "apiVersion": "2020-06-01",
        "name": "[concat('alz-vnet-rg-', parameters('vNetLocation'), '-', substring(uniqueString(subscription().id),0,6))]",
        "location": "[parameters('vNetLocation')]",
        "dependsOn": [],
        "properties": {
          "mode": "Incremental",
          "template": {
            "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "parameters": {},
            "variables": {},
            "resources": [
              {
                "type": "Microsoft.Resources/resourceGroups",
                "apiVersion": "2020-06-01",
                "name": "[parameters('vNetRgName')]",
                "location": "[parameters('vNetLocation')]",
                "properties": {}
              },
              {
                "type": "Microsoft.Resources/resourceGroups",
                "apiVersion": "2020-06-01",
                "name": "[concat('NetworkWatcherRG-', parameters('vNetLocation'))]",
                "location": "[parameters('vNetLocation')]",
                "properties": {}
              }
            ],
            "outputs": {}
          }
        }
      },
      {
        "type": "Microsoft.Resources/deployments",
        "apiVersion": "2020-06-01",
        "name": "[concat('alz-vnet-', parameters('vNetLocation'), '-', substring(uniqueString(subscription().id),0,6))]",
        "dependsOn": [
          "[concat('alz-vnet-rg-', parameters('vNetLocation'), '-', substring(uniqueString(subscription().id),0,6))]"
        ],
        "properties": {
          "mode": "Incremental",
          "template": {
            "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#",
            "contentVersion": "*******",
            "parameters": {},
            "variables": {},
            "resources": [
                {
                "type": "Microsoft.Network/virtualNetworks",
                "apiVersion": "2020-06-01",
                "name": "[parameters('vNetName')]",
                "location": "[parameters('vNetLocation')]",
                "dependsOn": [],
                "properties": {
                    "addressSpace": {
                    "addressPrefixes": [
                        "[parameters('vNetCidrRange')]"
                    ]
                    },
                    "dhcpOptions": {
                    "dnsServers": "[if(
                                        not(
                                            empty(
                                                    parameters('azureFirewallResourceId'))), 
                                                    array(reference(parameters('azureFirewallResourceId'), '2020-05-01').hubIPAddresses.privateIPAddress), 
                                                    parameters('dnsServers'))]"
                    }
                }
                },
              {
                "type": "Microsoft.Resources/deployments",
                "apiVersion": "2020-06-01",
                "name": "[concat('alz-vwanhub-peering-', parameters('vNetLocation'), '-', substring(uniqueString(subscription().id),0,6))]",
                "subscriptionId": "[split(parameters('vWanhubResourceId'),'/')[2]]",
                "resourceGroup": "[split(parameters('vWanhubResourceId'),'/')[4]]",
                "dependsOn": [
                  "[parameters('vNetName')]"
                ],
                "properties": {
                  "mode": "Incremental",
                  "expressionEvaluationOptions": {
                    "scope": "inner"
                  },
                  "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                      "remoteVirtualNetwork": {
                        "Type": "string",
                        "defaultValue": false
                      },
                      "vWanvhubName": {
                        "Type": "string",
                        "defaultValue": false
                      }
                    },
                    "variables": {},
                    "resources": [
                      {
                        "type": "Microsoft.Network/virtualHubs/hubVirtualNetworkConnections",
                        "apiVersion": "2019-09-01",
                        "name": "[[concat(parameters('vWanVhubName'),'/',last(split(parameters('remoteVirtualNetwork'),'/')))]",
                        "properties": {
                          "remoteVirtualNetwork": {
                            "id": "[[parameters('remoteVirtualNetwork')]"
                          }
                        }
                      }
                    ],
                    "outputs": {}
                  },
                  "parameters": {
                    "remoteVirtualNetwork": {
                      "value": "[concat(subscription().id,'/resourceGroups/',parameters('vNetRgName'), '/providers/','Microsoft.Network/virtualNetworks/', parameters('vNetName'))]"
                    },
                    "vWanvhubName": {
                      "value": "[split(parameters('vWanhubResourceId'),'/')[8]]"
                    }
                  }
                }
              }
            ],
            "outputs": {}
          }
        },
        "resourceGroup": "[parameters('vNetRgName')]"
      }
    ],
    "outputs": {}
  }