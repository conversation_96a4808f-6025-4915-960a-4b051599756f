{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"addressPrefix": {"value": "**********/16"}, "addressPrefixSecondary": {"value": "**********/16"}, "ambaAgArmRole": {"value": ["8e3af657-a8ff-443c-a75c-2fe8c4bcb635"]}, "ambaAgEmailContact": {"value": ""}, "ambaAgServiceHook": {"value": ""}, "auditAppGwWaf": {"value": "Yes"}, "auditPeDnsZones": {"value": "Yes"}, "connectivityLocation": {"value": "uksouth"}, "connectivityLocationSecondary": {"value": "swedencentral"}, "connectivitySubscriptionId": {"value": "********-0000-0000-0000-********0000"}, "corpConnectedLzSubscriptionId": {"value": []}, "corpLzSubscriptionId": {"value": []}, "currentDateTimeUtcNow": {"value": "20250606T112045Z"}, "delayCount": {"value": 55}, "denyAksPrivileged": {"value": "Yes"}, "denyAksPrivilegedEscalation": {"value": "Yes"}, "denyClassicResources": {"value": "Yes"}, "denyHttpIngressForAks": {"value": "Yes"}, "denyHybridNetworking": {"value": "Yes"}, "denyIpForwarding": {"value": "Yes"}, "denyMgmtPorts": {"value": "Yes"}, "denyMgmtPortsForIdentity": {"value": "Yes"}, "denyPipForIdentity": {"value": "Yes"}, "denyPipOnNicForCorp": {"value": "Yes"}, "denyPublicEndpoints": {"value": "Yes"}, "denySubnetWithoutNsg": {"value": "Yes"}, "denySubnetWithoutNsgForIdentity": {"value": "Yes"}, "denyVMUnmanagedDisk": {"value": "Yes"}, "deployAVNM": {"value": false}, "emailContactAsc": {"value": "<EMAIL>"}, "enableAMBAHybridVM": {"value": "Yes"}, "enableAMBAKeyManagement": {"value": "Yes"}, "enableAMBALoadBalancing": {"value": "Yes"}, "enableAMBANetworkChanges": {"value": "Yes"}, "enableAMBARecoveryServices": {"value": "Yes"}, "enableAMBAStorage": {"value": "Yes"}, "enableAMBAVM": {"value": "Yes"}, "enableAMBAWeb": {"value": "Yes"}, "enableAsc": {"value": "Yes"}, "enableAscForApis": {"value": "DeployIfNotExists"}, "enableAscForAppServices": {"value": "DeployIfNotExists"}, "enableAscForArm": {"value": "DeployIfNotExists"}, "enableAscForContainers": {"value": "DeployIfNotExists"}, "enableAscForCosmosDbs": {"value": "DeployIfNotExists"}, "enableAscForCspm": {"value": "DeployIfNotExists"}, "enableAscForKeyVault": {"value": "DeployIfNotExists"}, "enableAscForOssDb": {"value": "DeployIfNotExists"}, "enableAscForServers": {"value": "DeployIfNotExists"}, "enableAscForServersVulnerabilityAssessments": {"value": "DeployIfNotExists"}, "enableAscForSql": {"value": "DeployIfNotExists"}, "enableAscForSqlOnVm": {"value": "DeployIfNotExists"}, "enableAscForStorage": {"value": "DeployIfNotExists"}, "enableAzFw": {"value": "Yes"}, "enableAzFwDnsProxy": {"value": "No"}, "enableAzFwDnsProxySecondary": {"value": "No"}, "enableAzFwSecondary": {"value": "Yes"}, "enableChangeTracking": {"value": "Yes"}, "enableDdoS": {"value": "Yes"}, "enableDecommissioned": {"value": "Yes"}, "enableEncryptionInTransit": {"value": "Yes"}, "enableErGw": {"value": "No"}, "enableErGwSecondary": {"value": "No"}, "enableGuestAttestationPlat": {"value": "Yes"}, "enableGuestAttestationLZ": {"value": "Yes"}, "enableHub": {"value": "vhub"}, "enableHubSecondary": {"value": "vhub"}, "enableLogAnalytics": {"value": "Yes"}, "enableLzDdoS": {"value": "Yes"}, "enableMDEndpoints": {"value": "DeployIfNotExists"}, "enableMonitorBaselines": {"value": "Yes"}, "enableMonitorConnectivity": {"value": "Yes"}, "enableMonitorIdentity": {"value": "Yes"}, "enableMonitorManagement": {"value": "Yes"}, "enablePrivateDnsZones": {"value": "Yes"}, "enablePrivateDnsZonesForLzs": {"value": "Yes"}, "enablePrivateDnsZonesSecondary": {"value": "No"}, "enablePrivateSubnet": {"value": "Audit"}, "enableSandbox": {"value": "Yes"}, "enableSecondaryRegion": {"value": "Yes"}, "enableSecuritySolution": {"value": "Yes"}, "enableSentinel": {"value": "No"}, "enableServiceHealth": {"value": "No"}, "enableServiceHealthBuiltIn": {"value": "Yes"}, "enableSqlAudit": {"value": "Yes"}, "enableSqlEncryption": {"value": "Yes"}, "enableSqlThreat": {"value": "Yes"}, "enableStorageHttps": {"value": "Yes"}, "enableUpdateMgmt": {"value": "Yes"}, "enableVmBackup": {"value": "Yes"}, "enableVmBackupForIdentity": {"value": "Yes"}, "enableVmHybridMonitoring": {"value": "Yes"}, "enableVmInsights": {"value": "Yes"}, "enableVmMonitoring": {"value": "Yes"}, "enableVmssMonitoring": {"value": "Yes"}, "enableVpnActiveActive": {"value": "No"}, "enableVpnActiveActiveSecondary": {"value": "No"}, "enableVpnGw": {"value": "No"}, "enableVpnGwSecondary": {"value": "No"}, "enablevWANRoutingIntent": {"value": "No"}, "enablevWANRoutingIntentSecondary": {"value": "No"}, "enableWsAPIMInitiatives": {"value": "Audit"}, "enableWsAppServicesInitiatives": {"value": "Audit"}, "enableWsAutomationInitiatives": {"value": "Audit"}, "enableWsBotServiceInitiatives": {"value": "Audit"}, "enableWsCMKInitiatives": {"value": "Audit"}, "enableWsCognitiveServicesInitiatives": {"value": "Audit"}, "enableWsComputeInitiatives": {"value": "Audit"}, "enableWsContainerAppsInitiatives": {"value": "Audit"}, "enableWsContainerInstanceInitiatives": {"value": "Audit"}, "enableWsContainerRegistryInitiatives": {"value": "Audit"}, "enableWsCosmosDbInitiatives": {"value": "Audit"}, "enableWsDataExplorerInitiatives": {"value": "Audit"}, "enableWsDataFactoryInitiatives": {"value": "Audit"}, "enableWsEventGridInitiatives": {"value": "Audit"}, "enableWsEventHubInitiatives": {"value": "Audit"}, "enableWsKeyVaultSupInitiatives": {"value": "Audit"}, "enableWsKubernetesInitiatives": {"value": "Audit"}, "enableWsMachineLearningInitiatives": {"value": "Audit"}, "enableWsMySQLInitiatives": {"value": "Audit"}, "enableWsNetworkInitiatives": {"value": "Audit"}, "enableWsOpenAIInitiatives": {"value": "Audit"}, "enableWsPostgreSQLInitiatives": {"value": "Audit"}, "enableWsServiceBusInitiatives": {"value": "Audit"}, "enableWsSQLInitiatives": {"value": "Audit"}, "enableWsStorageInitiatives": {"value": "Audit"}, "enableWsSynapseInitiatives": {"value": "Audit"}, "enableWsVirtualDesktopInitiatives": {"value": "Audit"}, "enforceAcsb": {"value": "Yes"}, "enforceBackup": {"value": "Yes"}, "enforceBackupPlat": {"value": "Yes"}, "enforceKvGuardrails": {"value": "Yes"}, "enforceKvGuardrailsPlat": {"value": "Yes"}, "enterpriseScaleCompanyPrefix": {"value": "defaults"}, "erAzSku": {"value": ""}, "erAzSkuSecondary": {"value": ""}, "erRegionalOrAz": {"value": ""}, "erRegionalOrAzSecondary": {"value": ""}, "erRegionalSku": {"value": ""}, "erRegionalSkuSecondary": {"value": ""}, "expressRouteScaleUnit": {"value": "1"}, "expressRouteScaleUnitSecondary": {"value": "1"}, "firewallSku": {"value": "Premium"}, "firewallSkuSecondary": {"value": "Premium"}, "firewallZones": {"value": ["1", "2", "3"]}, "firewallZonesSecondary": {"value": ["1", "2", "3"]}, "gwAzSku": {"value": ""}, "gwAzSkuSecondary": {"value": ""}, "gwRegionalOrAz": {"value": ""}, "gwRegionalOrAzSecondary": {"value": ""}, "gwRegionalSku": {"value": ""}, "gwRegionalSkuSecondary": {"value": ""}, "identityAddressPrefix": {"value": "**********/24"}, "identityAddressPrefixSecondary": {"value": "**********/24"}, "identitySubscriptionId": {"value": "********-0000-0000-0000-********0000"}, "internetTrafficRoutingPolicy": {"value": false}, "internetTrafficRoutingPolicySecondary": {"value": false}, "laCategory": {"value": "allLogs"}, "listOfResourceTypesDisallowedForDeletion": {"value": ["microsoft.managedidentity/userassignedidentities"]}, "managementSubscriptionId": {"value": "********-0000-0000-0000-********0000"}, "monitorAlertsResourceGroup": {"value": "rg-amba-monitoring-001"}, "onlineLzSubscriptionId": {"value": []}, "privateDnsZonesToDeploy": {"value": ["privatelink.regionGeoShortCode.backup.windowsazure.com", "privatelink.uksouth.azmk8s.io", "privatelink.uksouth.batch.azure.com", "privatelink.uksouth.kusto.windows.net", "privatelink.adf.azure.com", "privatelink.afs.azure.net", "privatelink.agentsvc.azure-automation.net", "privatelink.analysis.windows.net", "privatelink.api.azureml.ms", "privatelink.azconfig.io", "privatelink.azure-api.net", "privatelink.azure-automation.net", "privatelink.azurecr.io", "privatelink.azure-devices.net", "privatelink.azure-devices-provisioning.net", "privatelink.azuredatabricks.net", "privatelink.azurehdinsight.net", "privatelink.azurehealthcareapis.com", "privatelink.azureiotcentral.com", "privatelink.azurestaticapps.net", "privatelink.azuresynapse.net", "privatelink.azurewebsites.net", "privatelink.batch.azure.com", "privatelink.blob.core.windows.net", "privatelink.cassandra.cosmos.azure.com", "privatelink.cognitiveservices.azure.com", "privatelink.database.windows.net", "privatelink.datafactory.azure.net", "privatelink.dev.azuresynapse.net", "privatelink.dfs.core.windows.net", "privatelink.dicom.azurehealthcareapis.com", "privatelink.digitaltwins.azure.net", "privatelink.directline.botframework.com", "privatelink.documents.azure.com", "privatelink.dp.kubernetesconfiguration.azure.com", "privatelink.eventgrid.azure.net", "privatelink.file.core.windows.net", "privatelink.grafana.azure.com", "privatelink.gremlin.cosmos.azure.com", "privatelink.guestconfiguration.azure.com", "privatelink.his.arc.azure.com", "privatelink.kubernetesconfiguration.azure.com", "privatelink.managedhsm.azure.net", "privatelink.mariadb.database.azure.com", "privatelink.media.azure.net", "privatelink.mongo.cosmos.azure.com", "privatelink.monitor.azure.com", "privatelink.mysql.database.azure.com", "privatelink.notebooks.azure.net", "privatelink.ods.opinsights.azure.com", "privatelink.oms.opinsights.azure.com", "privatelink.pbidedicated.windows.net", "privatelink.postgres.database.azure.com", "privatelink.prod.migration.windowsazure.com", "privatelink.purview.azure.com", "privatelink.purviewstudio.azure.com", "privatelink.queue.core.windows.net", "privatelink.redis.cache.windows.net", "privatelink.redisenterprise.cache.azure.net", "privatelink.search.windows.net", "privatelink.service.signalr.net", "privatelink.servicebus.windows.net", "privatelink.siterecovery.windowsazure.com", "privatelink.sql.azuresynapse.net", "privatelink.table.core.windows.net", "privatelink.table.cosmos.azure.com", "privatelink.tip1.powerquery.microsoft.com", "privatelink.token.botframework.com", "privatelink.vaultcore.azure.net", "privatelink.web.core.windows.net", "privatelink.webpubsub.azure.com", "privatelink.wvd.microsoft.com", "privatelink-global.wvd.microsoft.com"]}, "privateDnsZonesToDeploySecondary": {"value": []}, "privateTrafficRoutingPolicy": {"value": false}, "privateTrafficRoutingPolicySecondary": {"value": false}, "regCompPolParAusGovIsmRestrictedResourceTypes": {"value": "all"}, "regCompPolParAusGovIsmRestrictedVmAdminsExclude": {"value": ""}, "regCompPolParCanadaFedPbmmVmAdminsExclude": {"value": ""}, "regCompPolParCanadaFedPbmmVmAdminsInclude": {"value": ""}, "regCompPolParCisV2KeyVaultKeysRotateDays": {"value": 90}, "regCompPolParCmmcL3VmAdminsExclude": {"value": ""}, "regCompPolParCmmcL3VmAdminsInclude": {"value": ""}, "regCompPolParHitrustHipaaApplicationName": {"value": ""}, "regCompPolParHitrustHipaaCertificateThumb": {"value": ""}, "regCompPolParHitrustHipaaResGroupPrefix": {"value": ""}, "regCompPolParHitrustHipaaStoragePrefix": {"value": ""}, "regCompPolParIrs1075Sep2016VmAdminsExclude": {"value": ""}, "regCompPolParIrs1075Sep2016VmAdminsInclude": {"value": ""}, "regCompPolParMPAAApplicationName": {"value": ""}, "regCompPolParMPAACertificateThumb": {"value": ""}, "regCompPolParMPAARBatchMetricName": {"value": ""}, "regCompPolParMPAAResGroupPrefix": {"value": ""}, "regCompPolParMPAAStoragePrefix": {"value": ""}, "regCompPolParNistSp800171R2VmAdminsExclude": {"value": ""}, "regCompPolParNistSp800171R2VmAdminsInclude": {"value": ""}, "regCompPolParNZIsmRestrictedVmAdminsExclude": {"value": ""}, "regCompPolParNZIsmRestrictedVmAdminsInclude": {"value": ""}, "regCompPolParSoc2Type2AllowedRegistries": {"value": ""}, "regCompPolParSoc2Type2MaxCpuUnits": {"value": ""}, "regCompPolParSoc2Type2MaxMemoryBytes": {"value": ""}, "regCompPolParSovBaseConfRegions": {"value": []}, "regCompPolParSovBaseGlobalRegions": {"value": []}, "regCompPolParSwift2020DomainFqdn": {"value": ""}, "regCompPolParSwift2020VmAdminsInclude": {"value": ""}, "regulatoryComplianceInitativesToAssign": {"value": []}, "retentionInDays": {"value": "30"}, "singlePlatformSubscriptionId": {"value": ""}, "subnetMaskForAzFw": {"value": "**********/24"}, "subnetMaskForAzFwMgmt": {"value": ""}, "subnetMaskForAzFwMgmtSecondary": {"value": ""}, "subnetMaskForAzFwSecondary": {"value": "**********/24"}, "subnetMaskForGw": {"value": ""}, "subnetMaskForGwSecondary": {"value": ""}, "telemetryOptOut": {"value": "No"}, "userAssignedManagedIdentityName": {"value": "id-amba-prod-001"}, "vpnGateWayScaleUnit": {"value": "1"}, "vpnGateWayScaleUnitSecondary": {"value": "1"}, "vWANHubCapacity": {"value": "2"}, "vWANHubCapacitySecondary": {"value": "2"}, "vWANHubRoutingPreference": {"value": "ExpressRoute"}, "vWANHubRoutingPreferenceSecondary": {"value": "ExpressRoute"}, "wsAPIMSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsAppServicesSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsAutomationSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsBotServiceSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsCMKSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsCognitiveServicesSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsComputeSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsContainerAppsSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsContainerInstanceSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsContainerRegistrySelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsCosmosDbSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsDataExplorerSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsDataFactorySelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsEventGridSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsEventHubSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsKeyVaultSupSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsKubernetesSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsMachineLearningSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsMySQLSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsNetworkSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsOpenAISelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsPostgreSQLSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsServiceBusSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsSQLSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsStorageSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsSynapseSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}, "wsVirtualDesktopSelectorMG": {"value": ["defaults-platform", "defaults-landingzones"]}}}