{"name": "Deny-AppGW-Without-WAF", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Application Gateway should be deployed with WAF enabled", "description": "This policy enables you to restrict that Application Gateways is always deployed with WAF enabled", "metadata": {"version": "1.0.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/applicationGateways"}, {"field": "Microsoft.Network/applicationGateways/sku.name", "notequals": "WAF_v2"}]}, "then": {"effect": "[[parameters('effect')]"}}}}