{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "logAnalyticsResourceId": {"type": "string", "metadata": {"description": "Provide the resourceId for the central Log Analytics workspace."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}}, "variables": {"policyDefinitions": {"deployAzureActivityLog": "/providers/Microsoft.Authorization/policyDefinitions/2465583e-4e78-4c15-b6be-a36cbc7c8b0f", "policyVersion": "1.*.*"}, "policyAssignmentNames": {"azureActivityLog": "Deploy-AzActivity-Log", "description": "Deploys the diagnostic settings for Azure Activity to stream subscriptions audit logs to a Log Analytics workspace to monitor subscription-level events", "displayName": "Configure Azure Activity logs to stream to specified Log Analytics workspace"}, "nonComplianceMessage": {"message": "Azure Activity logs {enforcementMode} be configured to stream to specified Log Analytics workspace.", "Default": "must", "DoNotEnforce": "should"}, "rbacMonitoringContributor": "749f88d5-cbae-40b8-bcfc-e573ddc772fa", "rbacLogAnalyticsContributor": "92aaf0da-9dab-42b6-94a3-d43ce8d16293", "roleAssignmentNames": {"roleAssignmentNameLogAnalyticsContributor": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureActivityLog,'-1'))]", "roleAssignmentNameMonitoringContributor": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureActivityLog,'-2'))]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').azureActivityLog]", "location": "[deployment().location]", "identity": {"type": "SystemAssigned"}, "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').deployAzureActivityLog]", "definitionVersion": "[variables('policyDefinitions').policyVersion]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {"logAnalytics": {"value": "[parameters('logAnalyticsResourceId')]"}, "logsEnabled": {"value": "True"}}}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').roleAssignmentNameLogAnalyticsContributor]", "dependsOn": ["[variables('policyAssignmentNames').azureActivityLog]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacLogAnalyticsContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureActivityLog), '2019-09-01', 'Full' ).identity.principalId)]"}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').roleAssignmentNameMonitoringContributor]", "dependsOn": ["[variables('policyAssignmentNames').azureActivityLog]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacMonitoringContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureActivityLog), '2019-09-01', 'Full' ).identity.principalId)]"}}], "outputs": {}}