{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "topLevelManagementGroupPrefix": {
            "type": "string",
            "maxLength": 10,
            "metadata": {
                "description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale."
            }
        },
        "managementSubscriptionId": {
            "type": "string",
            "maxLength": 36,
            "defaultValue": ""
        },
        "enableLogAnalytics": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "metadata": {
                "description": "If 'Yes' is selected when also adding a subscription for management, ARM will assign two policies to enable auditing in your environment, into the Log Analytics workspace for platform monitoring. If 'No', it will be ignored."
            }
        },
        "targetManagementGroup": {
            "type": "string",
            "defaultValue": "landingzones",
            "metadata": {
                "description": "Name of the Landing Zones' Management Group (landingzones, corp, online) where the Policies will be applied to."
            }
        },
        "enableSqlAudit": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ]
        },
        "enableAksPolicy": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ]
        },
        "denyAksPrivileged": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ]
        },
        "denyAksPrivilegedEscalation": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ]
        },
        "denyHttpIngressForAks": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ]
        },
        "enableSqlEncryption": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ]
        },
        "enableVmBackup": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ]
        },
        "denyRdp": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ]
        },
        "enableStorageHttps": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ]
        },
        "denyIpForwarding": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ]
        },
        "denySubnetWithoutNsg": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No"
        },
        "enableLzDdoS": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No",
            "metadata": {
                "description": "Select whether DDoS Protection standard should be enabled or not."
            }
        },
        "connectivitySubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Provide subscription Id for the connectivity subscription where DDoS is enabled."
            }
        },
        "enableArcMonitoring": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "metadata": {
                "description": "If 'Yes' is selected, policy will be assigned to enforce Arc monitoring."
            }
        },
        "denyPublicEndpoints": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No",
            "metadata": {
                "description": "Select if policy to deny public endpoint should be assigned or not."
            }
        },
        "enableEncryptionInTransit": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No",
            "metadata": {
                "description": "Select if encryption in transit policy should be assigned or not."
            }
        },
        "enableVmMonitoring": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "metadata": {
                "description": "If 'Yes' is selected, policy will be assigned to enforce VM monitoring."
            }
        },
        "enableVmssMonitoring": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "metadata": {
                "description": "If 'Yes' is selected, policy will be assigned to enforce VMSS monitoring."
            }
        },
        "location": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Provide the location for the connectivity resources in the connectivity subscription."
            }
        }          
    },
    "variables": {
        "scope": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '-', parameters('targetManagementGroup'))]",
        "policyDefinitions": {
            "deployEncryptionInTransit": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Enforce-EncryptTransit')]",
            "denyPublicEndpoint": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Deny-PublicEndpoints')]",
            "deployVmBackup": "/providers/Microsoft.Authorization/policyDefinitions/98d0b9f8-fd90-49c9-88e2-d3baf3b0dd86",
            "denySubnetWithoutNsg": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyDefinitions/Deny-Subnet-Without-Nsg')]",
            "denyRdp": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyDefinitions/Deny-RDP-From-Internet')]",
            "denyIpForwarding": "/providers/Microsoft.Authorization/policyDefinitions/88c0b9da-ce96-4b03-9635-f29a937e2900",
            "deploySqlEncryption": "/providers/Microsoft.Authorization/policyDefinitions/86a912f6-9a06-4e26-b447-11b16ba8659f",
            "deploySqlSecurity": "/providers/Microsoft.Authorization/policyDefinitions/f4c68484-132f-41f9-9b6d-3e4b1cb55036",
            "deploySqlAuditing": "/providers/Microsoft.Authorization/policyDefinitions/a6fb4358-5bf4-4ad7-ba82-2cd2f41ce5e9",
            "storageHttps": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9",
            "deployStorageAtp": "/providers/Microsoft.Authorization/policyDefinitions/361c2074-3595-4e5d-8cab-4f21dffc835c",
            "deployAks": "/providers/Microsoft.Authorization/policyDefinitions/a8eff44f-8c92-45c3-a3fb-9880802d67a7",
            "denyAksPriv": "/providers/Microsoft.Authorization/policyDefinitions/95edb821-ddaf-4404-9732-666045e056b4",
            "denyAksNoPrivEsc": "/providers/Microsoft.Authorization/policyDefinitions/1c6e92c9-99f0-4e55-9cf2-0c234dc48f99",
            "denyHttpIngressAks": "/providers/Microsoft.Authorization/policyDefinitions/1a5b4dca-0b6f-4cf5-907c-56316bc1bf3d",
            "windowsArcMonitoring": "/providers/Microsoft.Authorization/policyDefinitions/69af7d4a-7b18-4044-93a9-2651498ef203",
            "linuxArcMonitoring": "/providers/Microsoft.Authorization/policyDefinitions/9d2b61b4-1d14-4a63-be30-d4498e7ad2cf",
            "vmMonitoring": "/providers/Microsoft.Authorization/policySetDefinitions/55f3eceb-5573-4f18-9695-226972c6d74a",
            "vmssMonitoring": "/providers/Microsoft.Authorization/policySetDefinitions/75714362-cae7-409e-9b99-a8e5075b7fad",
            "deployDoS": "/providers/Microsoft.Authorization/policyDefinitions/94de2ad3-e0c1-4caf-ad78-5d47bbc83d3d"
        },
        "policyAssignmentNames": {
            "deployEncryptionInTransit": "Enforce-TLS-SSL",
            "denyPublicEndpoint": "Deny-Public-Endpoints",
            "deployVmBackup": "Deploy-VM-Backup",
            "denySubnetWithoutNsg": "Deny-Subnet-Without-Nsg",
            "denyRdp": "Deny-RDP-from-internet",
            "denyIpForwarding": "Deny-IP-forwarding",
            "deploySqlEncryption": "Enforce-SQL-Encryption",
            "deploysqlSecurity": "Deploy-SQL-Security",
            "deploySqlAuditing": "Deploy-SQL-DB-Auditing",
            "storageHttps": "Deny-Storage-http",
            "deployStorageAtp": "Deploy-Storage-ATP",
            "deployAks": "Deploy-AKS-Policy",
            "denyAksPriv": "Deny-Privileged-AKS",
            "denyAksNoPrivEsc": "Deny-Priv-Esc-AKS",
            "denyHttpIngressAks": "Enforce-AKS-HTTPS",
            "vmMonitoring": "Deploy-VM-Monitoring",
            "windowsArcMonitoring": "Deploy-WS-Arc-Monitoring",
            "linuxArcMonitoring": "Deploy-LX-Arc-Monitoring",
            "vmssMonitoring": "Deploy-VMSS-Monitoring",
            "deployDdoS": "Enable-DDoS-VNET"
        },
        "rbacOwner": "8e3af657-a8ff-443c-a75c-2fe8c4bcb635",
        "rbacNetworkContributor": "4d97b98b-1d4f-4787-a291-c67834d212e7",
        "roleAssignmentNames": {
            "deployEncryptionInTransit": "[guid(concat(parameters('topLevelManagementGroupPrefix'),variables('policyAssignmentNames').deployEncryptionInTransit))]",
            "deployVmBackup": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').deployVmBackup))]",
            "deploySqlSecurity": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').deploysqlSecurity))]",
            "deploySqlAuditing": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').deploySqlAuditing))]",
            "deployStorageAtp": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').deployStorageAtp))]",
            "deploySqlEncryption": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').deploySqlEncryption))]",
            "deployAks": "[guid(concat(parameters('topLevelManagementGroupPrefix'), variables('policyAssignmentNames').deployAks))]",
            "deployVmMonitoring": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').vmMonitoring))]",
            "deployWindowsArcMonitoring": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').windowsArcMonitoring))]",
            "deployLinuxArcMonitoring": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').linuxArcMonitoring))]",
            "deployVmssMonitoring": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').vmssMonitoring))]",
            "deployDdoS": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').deployDdoS))]",
            "deployDdOsConnectivityMg": "[guid(concat(parameters('topLevelManagementGroupPrefix'), '-connectivity'))]"
        },
        "dDoSRgName": "[concat(parameters('topLevelManagementGroupPrefix'), '-ddos')]",
        "dDoSName": "[concat(parameters('topLevelManagementGroupPrefix'), '-ddos-', parameters('location'))]"
        //"blankTemplateEscaped": "{\"$schema\":\"https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#\",\"contentVersion\":\"*******\",\"parameters\":{},\"variables\":{},\"resources\":[],\"outputs\":{}}"
    },
    "resources": [
        {
            "condition": "[and(equals(parameters('enableLzDdoS'), 'Yes'), not(empty(parameters('connectivitySubscriptionId'))))]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').deployDdoS]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[variables('policyAssignmentNames').deployDdoS]",
                "displayName": "[variables('policyAssignmentNames').deployDdoS]",
                "policyDefinitionId": "[variables('policyDefinitions').deployDoS]",
                "parameters": {
                    "ddosPlan": {
                        "value": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('dDoSRgName'), '/providers/Microsoft.Network/ddosProtectionPlans/', variables('dDoSName'))]"
                    },
                    "effect": {
                        "value": "Modify"
                    }
                }
            }
        },
        {
            "condition": "[and(equals(parameters('enableLzDdoS'), 'Yes'), not(empty(parameters('connectivitySubscriptionId'))))]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployDdoS]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/policyAssignments', variables('policyAssignmentNames').deployDdoS)]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacNetworkContributor'))]",
                "principalId": "[if(and(equals(parameters('enableLzDdoS'), 'Yes'), not(empty(parameters('connectivitySubscriptionId')))), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').deployDdoS), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },
        {
            "condition": "[equals(parameters('denyPublicEndpoints'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2019-09-01",
            "name": "[variables('policyAssignmentNames').denyPublicEndpoint]",
            "location": "[deployment().location]",
            "properties": {
                "description": "[variables('policyAssignmentNames').denyPublicEndpoint]",
                "displayName": "[variables('policyAssignmentNames').denyPublicEndpoint]",
                "policyDefinitionId": "[variables('policyDefinitions').denyPublicEndpoint]",
                "parameters": {}
            }
        },
        {
            "condition": "[equals(parameters('enableEncryptionInTransit'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2019-09-01",
            "name": "[variables('policyAssignmentNames').deployEncryptionInTransit]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[variables('policyAssignmentNames').deployEncryptionInTransit]",
                "displayName": "[variables('policyAssignmentNames').deployEncryptionInTransit]",
                "policyDefinitionId": "[variables('policyDefinitions').deployEncryptionInTransit]",
                "parameters": {}
            }
        },
        {
            "condition": "[equals(parameters('enableEncryptionInTransit'), 'Yes')]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployEncryptionInTransit]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/policyAssignments', variables('policyAssignmentNames').deployEncryptionInTransit)]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(equals(parameters('enableEncryptionInTransit'), 'Yes'), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').deployEncryptionInTransit), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },
        {
            "condition": "[equals(parameters('denyRdp'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').denyRdp]",
            "properties": {
                "description": "Deny-RDP-from-Internet",
                "displayName": "Deny-RDP-from-Internet",
                "policyDefinitionId": "[variables('policyDefinitions').denyRdp]",
                "scope": "[variables('scope')]"
            }
        },
        {
            "condition": "[equals(parameters('enableVmBackup'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').deployVmBackup]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-VM-Backup",
                "displayName": "Deploy-VM-Backup",
                "policyDefinitionId": "[variables('policyDefinitions').deployVmBackup]",
                "scope": "[variables('scope')]",
                "parameters": {}
            }
        },
        {
            "condition": "[equals(parameters('enableVmBackup'), 'Yes')]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployVmBackup]",
            "dependsOn": [
                "[variables('policyAssignmentNames').deployVmBackup]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(equals(parameters('enableVmBackup'), 'Yes'), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').deployVmBackup), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },
        {
            "condition": "[equals(parameters('enableSqlAudit'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').deploySqlAuditing]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-SQL-Audit",
                "displayName": "Deploy-SQL-Audit",
                "policyDefinitionId": "[variables('policyDefinitions').deploySqlAuditing]",
                "scope": "[variables('scope')]"
            }
        },
        {
            "condition": "[equals(parameters('enableSqlAudit'), 'Yes')]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deploySqlAuditing]",
            "dependsOn": [
                "[variables('policyAssignmentNames').deploySqlAuditing]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(equals(parameters('enableSqlAudit'), 'Yes'), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').deploySqlAuditing), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },
        /*
        {
            "condition": "[equals(parameters('enableSqlSecurity'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').deploySqlSecurity]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-SQL-Security",
                "displayName": "Deploy-SQL-Security",
                "policyDefinitionId": "[variables('policyDefinitions').deploySqlSecurity]",
                "scope": "[variables('scope')]"
            }
        },
        {
            "condition": "[equals(parameters('enableSqlSecurity'), 'Yes')]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deploySqlSecurity]",
            "dependsOn": [
                "[variables('policyAssignmentNames').deploySqlSecurity]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(equals(parameters('enableSqlSecurity'), 'Yes'), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').deploysqlSecurity), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },*/
        {
            "condition": "[equals(parameters('enableSqlEncryption'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').deploySqlEncryption]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-SQL-Security",
                "displayName": "Deploy-SQL-Security",
                "policyDefinitionId": "[variables('policyDefinitions').deploySqlEncryption]",
                "scope": "[variables('scope')]"
            }
        },
        {
            "condition": "[equals(parameters('enableSqlEncryption'), 'Yes')]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deploySqlEncryption]",
            "dependsOn": [
                "[variables('policyAssignmentNames').deploySqlEncryption]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(equals(parameters('enableSqlEncryption'), 'Yes'), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').deploySqlEncryption), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },
        {
            "condition": "[equals(parameters('enableAksPolicy'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').deployAks]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-AKS-Policy",
                "displayName": "Deploy-AKS-Policy",
                "policyDefinitionId": "[variables('policyDefinitions').deployAks]",
                "scope": "[variables('scope')]"
            }
        },
        {
            "condition": "[equals(parameters('enableAksPolicy'), 'Yes')]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployAks]",
            "dependsOn": [
                "[variables('policyAssignmentNames').deployAks]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(equals(parameters('enableAksPolicy'), 'Yes'), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').deployAks), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },
        {
            "condition": "[equals(parameters('denyAksPrivileged'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').denyAksPriv]",
            "location": "[deployment().location]",
            "properties": {
                "description": "Deny-Privileged-Containers-AKS",
                "displayName": "Deny-Privileged-Containers-AKS",
                "policyDefinitionId": "[variables('policyDefinitions').denyAksPriv]",
                "scope": "[variables('scope')]",
                "parameters": {
                    "effect": {
                        "value": "deny"
                    }
                }
            }
        },
        {
            "condition": "[equals(parameters('denyAksPrivilegedEscalation'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').denyAksNoPrivEsc]",
            "location": "[deployment().location]",
            "properties": {
                "description": "Deny-Privileged-Escalations-AKS",
                "displayName": "Deny-Privileged-Escalations-AKS",
                "policyDefinitionId": "[variables('policyDefinitions').denyAksNoPrivEsc]",
                "scope": "[variables('scope')]",
                "parameters": {
                    "effect": {
                        "value": "deny"
                    }
                }
            }
        },
        {
            "condition": "[equals(parameters('denyHttpIngressForAks'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').denyHttpIngressAks]",
            "location": "[deployment().location]",
            "properties": {
                "description": "Enforce-Https-Ingress-AKS",
                "displayName": "Enforce-Https-Ingress-AKS",
                "policyDefinitionId": "[variables('policyDefinitions').denyHttpIngressAks]",
                "scope": "[variables('scope')]",
                "parameters": {
                    "effect": {
                        "value": "deny"
                    }
                }
            }
        },                                    
        {
            "condition": "[equals(parameters('enableStorageHttps'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').storageHttps]",
            "location": "[deployment().location]",
            "properties": {
                "description": "Enforce-Secure-Storage",
                "displayName": "Enforce-Secure-Storage",
                "policyDefinitionId": "[variables('policyDefinitions').storageHttps]",
                "scope": "[variables('scope')]"             
            }
        },
        {
            "condition": "[equals(parameters('denyIpForwarding'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').denyIpForwarding]",
            "location": "[deployment().location]",
            "properties": {
                "description": "Deny-IP-Forwarding",
                "displayName": "Deny-IP-Forwarding",
                "policyDefinitionId": "[variables('policyDefinitions').denyIpForwarding]",
                "scope": "[variables('scope')]"
            }
        },
        {
            "condition": "[equals(parameters('denySubnetWithoutNsg'), 'Yes')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').denySubnetWithoutNsg]",
            "location": "[deployment().location]",
            "properties": {
                "description": "Deny-Subnet-Without-Nsg",
                "displayName": "Deny-Subnet-Without-Nsg",
                "policyDefinitionId": "[variables('policyDefinitions').denySubnetWithoutNsg]",
                "scope": "[variables('scope')]"
            }
        },     
        
        {
            // Conditional assignment of policy to enforce Log Analytics VM extension to Windows and Linux virtual machines
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableVmMonitoring'), 'Yes'))]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').vmMonitoring]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-VM-Monitoring",
                "displayName": "Deploy-VM-Monitoring",
                "policyDefinitionId": "[variables('policyDefinitions').vmMonitoring]",
                "scope": "[variables('scope')]",
                "parameters": {
                    "logAnalytics_1": {
                        "value": "[toLower(concat('/subscriptions/', parameters('managementSubscriptionId'), '/resourceGroups/', parameters('topLevelManagementGroupPrefix'), '-mgmt', '/providers/Microsoft.OperationalInsights/workspaces/', parameters('topLevelManagementGroupPrefix'), '-la-', parameters('managementSubscriptionId')))]"
                    }
                }
            }
        },
        {
            // Role assignment of the conditional VM monitoring policy assignment
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableVmMonitoring'), 'Yes'))]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployVmMonitoring]",
            "dependsOn": [
                "[variables('policyAssignmentNames').vmMonitoring]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableVmMonitoring'), 'Yes')), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').vmMonitoring), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },
        {
            // Conditional assignment of policy to enforce VMSS monitoring to Log Analytics
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableVmssMonitoring'), 'Yes'))]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').vmssMonitoring]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-VMSS-Monitoring",
                "displayName": "Deploy-VMSS-Monitoring",
                "policyDefinitionId": "[variables('policyDefinitions').vmssMonitoring]",
                "scope": "[variables('scope')]",
                "parameters": {
                    "logAnalytics_1": {
                        "value": "[toLower(concat('/subscriptions/', parameters('managementSubscriptionId'), '/resourceGroups/', parameters('topLevelManagementGroupPrefix'), '-mgmt', '/providers/Microsoft.OperationalInsights/workspaces/', parameters('topLevelManagementGroupPrefix'), '-la-', parameters('managementSubscriptionId')))]"
                    }
                }
            }
        },
        {
            // Role assignment of the conditional VMSS monitoring policy assignment
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableVmssMonitoring'), 'Yes'))]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployVmssMonitoring]",
            "dependsOn": [
                "[variables('policyAssignmentNames').vmssMonitoring]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableVmssMonitoring'), 'Yes')), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').vmssMonitoring), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },
        {
            // Conditional assignment of policy to enforce Windows Arc monitoring to Log Analytics
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableArcMonitoring'), 'Yes'))]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').windowsArcMonitoring]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-Windows-Arc-Monitoring",
                "displayName": "Deploy-Windows-Arc-Monitoring",
                "policyDefinitionId": "[variables('policyDefinitions').windowsArcMonitoring]",
                "scope": "[variables('scope')]",
                "parameters": {
                    "logAnalytics": {
                        "value": "[toLower(concat('/subscriptions/', parameters('managementSubscriptionId'), '/resourceGroups/', parameters('topLevelManagementGroupPrefix'), '-mgmt', '/providers/Microsoft.OperationalInsights/workspaces/', parameters('topLevelManagementGroupPrefix'), '-la-', parameters('managementSubscriptionId')))]"
                    }
                }
            }
        },
        {
            // Role assignment of the conditional Windows Arc monitoring policy assignment
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableArcMonitoring'), 'Yes'))]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployWindowsArcMonitoring]",
            "dependsOn": [
                "[variables('policyAssignmentNames').windowsArcMonitoring]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableArcMonitoring'), 'Yes')), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').windowsArcMonitoring), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },
        {
            // Conditional assignment of policy to enforce Linux Arc monitoring to Log Analytics
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableArcMonitoring'), 'Yes'))]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').linuxArcMonitoring]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-Linux-Arc-Monitoring",
                "displayName": "Deploy-Linux-Arc-Monitoring",
                "policyDefinitionId": "[variables('policyDefinitions').linuxArcMonitoring]",
                "scope": "[variables('scope')]",
                "parameters": {
                    "logAnalytics": {
                        "value": "[toLower(concat('/subscriptions/', parameters('managementSubscriptionId'), '/resourceGroups/', parameters('topLevelManagementGroupPrefix'), '-mgmt', '/providers/Microsoft.OperationalInsights/workspaces/', parameters('topLevelManagementGroupPrefix'), '-la-', parameters('managementSubscriptionId')))]"
                    }
                }
            }
        },
        {
            // Role assignment of the conditional Linux Arc monitoring policy assignment
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableArcMonitoring'), 'Yes'))]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployLinuxArcMonitoring]",
            "dependsOn": [
                "[variables('policyAssignmentNames').linuxArcMonitoring]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes'), equals(parameters('enableArcMonitoring'), 'Yes')), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').linuxArcMonitoring), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        }        
    ],
    "outputs": {}
}