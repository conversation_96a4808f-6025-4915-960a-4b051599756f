{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}}, "variables": {"policyDefinitions": {"denyHttpIngressAks": "/providers/Microsoft.Authorization/policyDefinitions/1a5b4dca-0b6f-4cf5-907c-56316bc1bf3d", "policyVersion": "8.*.*"}, "policyAssignmentNames": {"denyHttpIngressAks": "Enforce-AKS-HTTPS", "description": "Use of HTTPS ensures authentication and protects data in transit from network layer eavesdropping attacks. This capability is currently generally available for Kubernetes Service (AKS), and in preview for AKS Engine and Azure Arc enabled Kubernetes. For more info, visit https://aka.ms/kubepolicydoc", "displayName": "Kubernetes clusters should be accessible only over HTTPS"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').denyHttpIngressAks]", "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').denyHttpIngressAks]", "definitionVersion": "[variables('policyDefinitions').policyVersion]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {"effect": {"value": "deny"}}}}], "outputs": {}}