{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"policyEffect": {"type": "string", "allowedValues": ["<PERSON><PERSON>", "Audit", "Disabled"], "defaultValue": "Audit"}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}}, "variables": {"policyDefinitions": {"auditWAF": "/providers/Microsoft.Authorization/policyDefinitions/564feb30-bf6a-4854-b4bb-0d2d2d1e6c66", "policyVersion": "2.*.*"}, "policyAssignmentNames": {"auditWAF": "Audit-AppGW-WAF", "description": "Assign the WAF should be enabled for Application Gateway audit policy.", "displayName": "Web Application Firewall (WAF) should be enabled for Application Gateway"}, "nonComplianceMessage": {"message": "Web Application Firewall (WAF) {enforcementMode} be enabled for Application Gateway.", "Default": "must", "DoNotEnforce": "should"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').auditWAF]", "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').auditWAF]", "definitionVersion": "[variables('policyDefinitions').policyVersion]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {"effect": {"value": "[parameters('policyEffect')]"}}}}], "outputs": {}}