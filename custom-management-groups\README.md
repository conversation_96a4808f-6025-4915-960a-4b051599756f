# Custom Management Group Structure

Folder này chứa các template ARM để tạo cấu trúc management group tùy chỉnh theo yêu cầu.

## Cấu trúc Management Group

```
Tenant Root Management Group
└── EWH (Enterprise-Wide Hub)
    ├── Platform
    │   ├── mg-Platform-Management
    │   └── mg-Platform-Connectivity
    ├── Landing Zones
    │   ├── lz-prd
    │   ├── lz-non-prd
    │   │   ├── lz-prd-legacy
    │   │   ├── lz-prd-microwave
    │   │   ├── lz-non-prd-uat
    │   │   └── lz-non-prd-dev
    ├── Decommissioned
    └── Sandbox
```

## Files Structure

- `main-template.json` - Template chính để deploy toàn bộ cấu trúc
- `management-groups.json` - Template tạo management groups
- `policy-definitions.json` - Policy definitions từ Azure Landing Zones
- `policy-assignments.json` - Policy assignments cho từng management group
- `parameters/` - Folder chứa parameter files
- `policies/` - Folder chứa policy definitions riêng biệt

## Deployment

### Prerequisites
- Azure CLI hoặc Azure PowerShell
- Appropriate permissions để tạo management groups và policies
- Tenant Root Group access

### Option 1: PowerShell Deployment
```powershell
# Deploy với default settings
.\deploy.ps1

# Deploy với custom prefix
.\deploy.ps1 -ManagementGroupPrefix "MYORG" -Location "West Europe"

# Deploy chỉ management groups (không có policies)
.\deploy.ps1 -EnablePolicyDeployment $false
```

### Option 2: Bash Deployment
```bash
# Deploy với default settings
./deploy.sh

# Deploy với custom prefix
./deploy.sh --prefix "MYORG" --location "West Europe"

# Deploy chỉ management groups (không có policies)
./deploy.sh --no-policies
```

### Option 3: Manual Azure CLI
```bash
az deployment tenant create \
  --name "CustomMgmtGroups" \
  --template-file main-template.json \
  --parameters @parameters/main.parameters.json \
  --location "East US"
```

## Validation

Sau khi deploy, chạy script validation để kiểm tra:

```powershell
# PowerShell
.\validate.ps1 -ManagementGroupPrefix "EWH"
```

```bash
# Bash
./validate.sh --prefix "EWH"
```

## Policy Details

### Policy Definitions
1. **Audit-Tags-Mandatory-Rg**: Audit mandatory tags trên resource groups
2. **Audit-PrivateLinkDnsZones**: Audit việc tạo Private Link DNS Zones
3. **Deploy-Sql-SecurityAlertPolicies**: Deploy SQL security alert policies

### Policy Set Definitions (Initiatives)
1. **Enforce-ALZ-Sandbox**: Enforce policies cho Sandbox environment
2. **Enforce-Guardrails-Compute**: Enforce compute guardrails

### Policy Assignments
- **Root Level**: Audit mandatory tags
- **Landing Zones**: Audit Private DNS Zones
- **Production**: Deploy SQL Security, Enforce Compute Guardrails
- **Sandbox**: Enforce Sandbox restrictions

## Customization

### Adding New Policies
1. Thêm policy definition vào `policy-definitions.json`
2. Thêm policy assignment vào `policy-assignments.json`
3. Update parameters nếu cần

### Modifying Management Group Structure
1. Update `management-groups.json`
2. Update variables trong `main-template.json`
3. Update policy assignments scope

## Features

- ✅ Cấu trúc management group tùy chỉnh theo ảnh
- ✅ Policy definitions từ Azure Landing Zones
- ✅ Policy assignments phù hợp cho từng environment
- ✅ Support cho multiple environments (prd, uat, dev, legacy, microwave)
- ✅ Governance và compliance
- ✅ Automated deployment scripts
- ✅ Validation scripts
- ✅ Cross-platform support (PowerShell & Bash)
- ✅ Comprehensive policy library từ Azure Landing Zones
- ✅ Policy generation scripts

## Azure Landing Zones Policies Integration

### Policy Categories Available

Folder `policies/` chứa reference documentation cho tất cả policies từ Azure Landing Zones:

1. **ALZ-PolicyAssignments-Reference.md** - Danh sách tất cả policy assignments
2. **ALZ-PolicyDefinitions-Reference.md** - Danh sách tất cả policy definitions
3. **policy-assignments-extended.json** - Extended policy assignments template

### Policy Types

- **🔍 AUDIT Policies**: Compliance monitoring và reporting
- **🚫 DENY Policies**: Security enforcement và prevention
- **🔧 DINE Policies**: Automatic deployment và remediation
- **🛡️ ENFORCE Policies**: Guardrails cho specific services
- **🔄 MODIFY Policies**: Configuration changes
- **🚨 DENYACTION Policies**: Prevent specific actions

### Generate Custom Policies

Sử dụng script để generate policies từ Azure Landing Zones source:

```powershell
.\scripts\Generate-ALZ-Policies.ps1 -ALZSourcePath "C:\path\to\Enterprise-Scale" -ManagementGroupPrefix "EWH"
```

### Key Policy Sets Included

- **Enforce-Guardrails-Compute**: VM security và compliance
- **Enforce-Guardrails-Network**: Network security controls
- **Enforce-Guardrails-Storage**: Storage encryption và access
- **Enforce-Guardrails-SQL**: Database security và auditing
- **Enforce-Guardrails-KeyVault**: Key management security
- **Enforce-ALZ-Sandbox**: Sandbox environment restrictions
