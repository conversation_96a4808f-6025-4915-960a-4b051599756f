{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "effect": {"type": "string", "allowedValues": ["DenyAction", "Disabled"], "defaultValue": "DenyAction"}, "resourceName": {"type": "string", "metadata": {"description": "Provide the name of the resource that you want to protect from accidental deletion."}}, "resourceType": {"type": "string", "metadata": {"description": "Provide the resource type that you want to protect from accidental deletion."}}}, "variables": {"policyDefinitions": {"denyActionResourceDeletion": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyDefinitions/DenyAction-DeleteResources')]"}, "policyAssignmentNames": {"denyActionResourceDeletion": "DenyAction-DeleteUAMIAMA", "description": "This policy provides a safeguard against accidental removal of the User Assigned Managed Identity used by AMA by blocking delete calls using deny action effect.", "displayName": "Do not allow deletion of the User Assigned Managed Identity used by AMA"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentNames').denyActionResourceDeletion]", "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').denyActionResourceDeletion]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {"effect": {"value": "[parameters('effect')]"}, "resourceName": {"value": "[parameters('resourceName')]"}, "resourceType": {"value": "[parameters('resourceType')]"}}}}], "outputs": {}}