{"name": "Deny-PostgreSql-http", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "PostgreSQL database servers enforce SSL connection.", "description": "Azure Database for PostgreSQL supports connecting your Azure Database for PostgreSQL server to client applications using Secure Sockets Layer (SSL). Enforcing SSL connections between your database server and your client applications helps protect against 'man in the middle' attacks by encrypting the data stream between the server and your application. This configuration enforces that SSL is always enabled for accessing your database server.", "metadata": {"version": "1.0.1", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "Disabled", "<PERSON><PERSON>"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "minimalTlsVersion": {"type": "String", "defaultValue": "TLS1_2", "allowedValues": ["TLS1_2", "TLS1_0", "TLS1_1", "TLSEnforcementDisabled"], "metadata": {"displayName": "Select version minimum TLS for PostgreSQL server", "description": "Select version  minimum TLS version Azure Database for PostgreSQL server to enforce"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.DBforPostgreSQL/servers"}, {"anyOf": [{"field": "Microsoft.DBforPostgreSQL/servers/sslEnforcement", "exists": "false"}, {"field": "Microsoft.DBforPostgreSQL/servers/sslEnforcement", "notEquals": "Enabled"}, {"field": "Microsoft.DBforPostgreSQL/servers/minimalTlsVersion", "notequals": "[[parameters('minimalTlsVersion')]"}]}]}, "then": {"effect": "[[parameters('effect')]"}}}}