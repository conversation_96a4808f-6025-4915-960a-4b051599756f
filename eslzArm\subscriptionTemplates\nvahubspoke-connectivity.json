{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "topLevelManagementGroupPrefix": {
            "type": "string",
            "maxLength": 10,
            "metadata": {
                "description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale."
            }
        },
        "addressPrefix": {
            "type": "string",
            "metadata": {
                "displayName": "addressPrefix",
                "description": "Address prefix of the HUB"
            }
        },
        "location": {
            "type": "string",
            "metadata": {
                "displayName": "location",
                "description": "Location of the HUB"
            },
            "defaultValue": "[deployment().location]"
        },
        "enableHub": {
            "type": "string",
            "allowedValues": [
                "nva",
                "No"
            ],
            "defaultValue": "No",
            "metadata": {
                "description": "Select whether the virtual network hub should be deployed or not."
            }
        },
        "enableVpnGw": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No",
            "metadata": {
                "description": "Select whether the VPN Gateway should be deployed or not."
            }
        },
        "enableErGw": {
            "type": "string",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "defaultValue": "No",
            "metadata": {
                "description": "Select whether the ExpressRoute Gateway should be deployed or not."
            }
        },
        "enableDdoS": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "metadata": {
                "description": "Select whether the DDoS Network Protection should be enabled or not."
            }
        },
        "connectivitySubscriptionId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Provide the subscription id for the dedicated connectivity subscription."
            }
        },
        "subnetMaskForAzFw": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Provide subnet for Azure Firewall."
            }
        },
        "subnetMaskForGw": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "Provide subnet for VPN/ER."
            }
        },
        "gwRegionalOrAz": {
            "type": "string",
            "defaultValue": ""
        },
        "enableVpnActiveActive": {
            "type": "string",
            "defaultValue": ""
        },
        "gwAzSku": {
            "type": "string",
            "defaultValue": ""
        },
        "gwRegionalSku": {
            "type": "string",
            "defaultValue": ""
        },
        "erRegionalOrAz": {
            "type": "string",
            "defaultValue": ""
        },
        "erAzSku": {
            "type": "string",
            "defaultValue": ""
        },
        "erRegionalSku": {
            "type": "string",
            "defaultValue": ""
        },
        "ddosPlanResourceId": {
            "type": "string"
        }
    },
    "variables": {
        "vpngwname": "[concat(parameters('topLevelManagementGroupPrefix'), '-vpngw-', parameters('location'))]",
        "erGwName": "[concat(parameters('topLevelManagementGroupPrefix'), '-ergw-', parameters('location'))]",
        "rgName": "[concat(parameters('topLevelManagementGroupPrefix'), '-vnethub-', parameters('location'))]",
        "hubName": "[concat(parameters('topLevelManagementGroupPrefix'), '-hub-', parameters('location'))]",
        "azVpnGwIpName": "[concat(variables('vpngwname'), '-pip')]",
        "azVpnGwAAIpName": "[concat(variables('vpngwname'), '-pip-002')]",
        "azVpnGwSubnetId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'),'/providers/Microsoft.Network/virtualNetworks/', variables('hubname'), '/subnets/GatewaySubnet')]",
        "azErGwIpName": "[concat(variables('erGwName'), '-pip')]",
        "azVpnGwPipId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'), '/providers/Microsoft.Network/publicIPAddresses/', variables('azVpnGwIpName'))]",
        "azVpnGwAAPipId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'), '/providers/Microsoft.Network/publicIPAddresses/', variables('azVpnGwAAIpName'))]",
        "azVpnPIPZones": "[split('1,2,3', ',')]",
        "azErGwSubnetId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'),'/providers/Microsoft.Network/virtualNetworks/', variables('hubname'), '/subnets/GatewaySubnet')]",
        "azErGwPipId": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'), '/providers/Microsoft.Network/publicIPAddresses/', variables('azErGwIpName'))]",
        "resourceDeploymentName": "[take(concat(deployment().name, '-hubspoke', parameters('location')), 64)]",
        // Creating variable that later will be used in conjunction with the union() function to cater for conditional subnet creation while ensuring idempotency
        "gwSubnet": [
            {
                "name": "GatewaySubnet",
                "properties": {
                    "addressPrefix": "[parameters('subnetMaskForGw')]"
                }
            }
        ],
        "fwSubnet": [
            {
                "name": "AzureFirewallSubnet",
                "properties": {
                    "addressPrefix": "[parameters('subnetMaskForAzFw')]"
                }
            }
        ],
        "ddosProtectionPlanId": {
            "id": "[parameters('ddosPlanResourceId')]"
        }
    },
    "resources": [
        {
            // Conditionally deploy virtual network hub
            "condition": "[and(equals(parameters('enableHub'), 'nva'), not(empty(parameters('connectivitySubscriptionId'))))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "location": "[parameters('location')]",
            "name": "[concat('alz-connectivityNvaHubSub-', parameters('location'), '-', substring(uniqueString(parameters('connectivitySubscriptionId')),0,6))]",
            "subscriptionId": "[parameters('connectivitySubscriptionId')]",
            "properties": {
                "mode": "Incremental",
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {},
                    "resources": [
                        {
                            "type": "Microsoft.Resources/resourceGroups",
                            "apiVersion": "2019-10-01",
                            "location": "[parameters('location')]",
                            "name": "[variables('rgName')]",
                            "properties": {}
                        },
                        {
                            "type": "Microsoft.Resources/deployments",
                            "apiVersion": "2019-10-01",
                            "name": "[variables('resourceDeploymentName')]",
                            "resourceGroup": "[variables('rgName')]",
                            "dependsOn": [
                                "[concat('Microsoft.Resources/resourceGroups/', variables('rgName'))]"
                            ],
                            "properties": {
                                "mode": "Incremental",
                                "template": {
                                    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
                                    "contentVersion": "*******",
                                    "parameters": {},
                                    "resources": [
                                        {
                                            "name": "[variables('hubName')]",
                                            "type": "Microsoft.Network/virtualNetworks",
                                            "apiVersion": "2020-04-01",
                                            "location": "[parameters('location')]",
                                            "properties": {
                                                "addressSpace": {
                                                    "addressPrefixes": [
                                                        "[parameters('addressPrefix')]"
                                                    ]
                                                },
                                                "subnets": "[
                                                             union(
                                                                  if(
                                                                      not(
                                                                          empty(parameters('subnetMaskForGw'))), variables('gwSubnet'), json('[]')), 
                                                                          if(
                                                                              not(
                                                                                  empty(parameters('subnetMaskForAzFw'))), variables('fwSubnet'), json('[]')))]",
                                                "enableDdosProtection": "[if(equals(parameters('enableDdoS'), 'Yes'), 'true', 'false')]",
                                                "ddosProtectionPlan": "[if(equals(parameters('enableDdoS'), 'Yes'), variables('ddosProtectionPlanId'), json('null'))]"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableVpnGw'), 'Yes'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2023-11-01",
                                            "type": "Microsoft.Network/publicIpAddresses",
                                            "location": "[parameters('location')]",
                                            "name": "[variables('azVpnGwIpName')]",
                                            "zones": "[if(equals(parameters('gwRegionalOrAz'), 'Zone'), variables('azVpnPIPZones'), json('null'))]",
                                            "sku": {
                                                "name": "Standard"
                                            },
                                            "properties": {
                                                "publicIPAllocationMethod": "Static"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableVpnGw'), 'Yes'), equals(parameters('enableVpnActiveActive'),'Yes'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2023-11-01",
                                            "type": "Microsoft.Network/publicIpAddresses",
                                            "location": "[parameters('location')]",
                                            "name": "[variables('azVpnGwAAIpName')]",
                                            "zones": "[if(equals(parameters('gwRegionalOrAz'), 'Zone'), variables('azVpnPIPZones'), json('null'))]",
                                            "sku": {
                                                "name": "Standard"
                                            },
                                            "properties": {
                                                "publicIPAllocationMethod": "Static"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableVpnGw'), 'Yes'), equals(parameters('enableVpnActiveActive'),'No'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2023-11-01",
                                            "name": "[variables('vpngwname')]",
                                            "type": "Microsoft.Network/virtualNetworkGateways",
                                            "location": "[parameters('location')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/publicIPAddresses/', variables('azVpnGwIpName'))]",
                                                "[concat('Microsoft.Network/virtualNetworks/', variables('hubName'))]"
                                            ],
                                            "properties": {
                                                "activeActive": false,
                                                "gatewayType": "Vpn",
                                                "vpnGatewayGeneration": "Generation2",
                                                "vpnType": "RouteBased",
                                                "ipConfigurations": [
                                                    {
                                                        "name": "default",
                                                        "properties": {
                                                            "privateIPAllocationMethod": "Dynamic",
                                                            "subnet": {
                                                                "id": "[variables('azVpnGwSubnetId')]"
                                                            },
                                                            "publicIpAddress": {
                                                                "id": "[variables('azVpnGwPipId')]"
                                                            }
                                                        }
                                                    }
                                                ],
                                                "sku": {
                                                    "name": "[if(
                                                                 and(
                                                                     or(
                                                                         empty(parameters('gwRegionalSku')), 
                                                                         empty(parameters('gwAzSku'))), 
                                                                         not(
                                                                             empty(parameters('gwRegionalSku')))), 
                                                                                parameters('gwRegionalSku'), 
                                                                                parameters('gwAzSku'))]",
                                                    "tier": "[if(
                                                                and(
                                                                    or(
                                                                        empty(parameters('gwRegionalSku')), 
                                                                        empty(parameters('gwAzSku'))), 
                                                                        not(
                                                                            empty(parameters('gwRegionalSku')))), 
                                                                                parameters('gwRegionalSku'), 
                                                                                parameters('gwAzSku'))]"
                                                }
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableVpnGw'), 'Yes'), equals(parameters('enableVpnActiveActive'),'Yes'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2020-05-01",
                                            "name": "[variables('vpngwname')]",
                                            "type": "Microsoft.Network/virtualNetworkGateways",
                                            "location": "[parameters('location')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/publicIPAddresses/', variables('azVpnGwIpName'))]",
                                                "[concat('Microsoft.Network/publicIPAddresses/', variables('azVpnGwAAIpName'))]",
                                                "[concat('Microsoft.Network/virtualNetworks/', variables('hubName'))]"
                                            ],
                                            "properties": {
                                                "activeActive": true,
                                                "gatewayType": "Vpn",
                                                "vpnGatewayGeneration": "Generation2",
                                                "vpnType": "RouteBased",
                                                "ipConfigurations": [
                                                    {
                                                        "name": "default",
                                                        "properties": {
                                                            "privateIPAllocationMethod": "Dynamic",
                                                            "subnet": {
                                                                "id": "[variables('azVpnGwSubnetId')]"
                                                            },
                                                            "publicIpAddress": {
                                                                "id": "[variables('azVpnGwPipId')]"
                                                            }
                                                        }
                                                    },
                                                    {
                                                        "name": "activeactive",
                                                        "properties": {
                                                            "privateIPAllocationMethod": "Dynamic",
                                                            "subnet": {
                                                                "id": "[variables('azVpnGwSubnetId')]"
                                                            },
                                                            "publicIpAddress": {
                                                                "id": "[variables('azVpnGwAAPipId')]"
                                                            }
                                                        }
                                                    }
                                                ],
                                                "sku": {
                                                    "name": "[if(
                                                                 and(
                                                                     or(
                                                                         empty(parameters('gwRegionalSku')), 
                                                                         empty(parameters('gwAzSku'))), 
                                                                         not(
                                                                             empty(parameters('gwRegionalSku')))), 
                                                                                parameters('gwRegionalSku'), 
                                                                                parameters('gwAzSku'))]",
                                                    "tier": "[if(
                                                                and(
                                                                    or(
                                                                        empty(parameters('gwRegionalSku')), 
                                                                        empty(parameters('gwAzSku'))), 
                                                                        not(
                                                                            empty(parameters('gwRegionalSku')))), 
                                                                                parameters('gwRegionalSku'), 
                                                                                parameters('gwAzSku'))]"
                                                }
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableErGw'), 'Yes'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2020-05-01",
                                            "type": "Microsoft.Network/publicIpAddresses",
                                            "location": "[parameters('location')]",
                                            "name": "[variables('azErGwIpName')]",
                                            "sku": {
                                                "name": "Standard"
                                            },
                                            "properties": {
                                                "publicIPAllocationMethod": "Static"
                                            }
                                        },
                                        {
                                            "condition": "[and(equals(parameters('enableErGw'), 'Yes'), not(empty(parameters('subnetMaskForGw'))))]",
                                            "apiVersion": "2020-05-01",
                                            "name": "[variables('erGwName')]",
                                            "type": "Microsoft.Network/virtualNetworkGateways",
                                            "location": "[parameters('location')]",
                                            "dependsOn": [
                                                "[concat('Microsoft.Network/publicIPAddresses/', variables('azErGwIpName'))]",
                                                "[concat('Microsoft.Network/virtualNetworkGateways/', variables('vpngwname'))]",
                                                "[concat('Microsoft.Network/virtualNetworks/', variables('hubName'))]"
                                            ],
                                            "properties": {
                                                "gatewayType": "ExpressRoute",
                                                "ipConfigurations": [
                                                    {
                                                        "name": "default",
                                                        "properties": {
                                                            "privateIPAllocationMethod": "Dynamic",
                                                            "subnet": {
                                                                "id": "[variables('azErGwSubnetId')]"
                                                            },
                                                            "publicIpAddress": {
                                                                "id": "[variables('azErGwPipId')]"
                                                            }
                                                        }
                                                    }
                                                ],
                                                "sku": {
                                                    "name": "[if(
                                                                 and(
                                                                     or(
                                                                         empty(parameters('erRegionalSku')), 
                                                                         empty(parameters('erAzSku'))), 
                                                                         not(
                                                                             empty(parameters('erRegionalSku')))), 
                                                                                parameters('erRegionalSku'), 
                                                                                parameters('erAzSku'))]",
                                                    "tier": "[if(
                                                                and(
                                                                    or(
                                                                        empty(parameters('erRegionalSku')), 
                                                                        empty(parameters('erAzSku'))), 
                                                                        not(
                                                                            empty(parameters('erRegionalSku')))), 
                                                                                parameters('erRegionalSku'), 
                                                                                parameters('erAzSku'))]"
                                                }
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    ]
                }
            }
        }
    ]
}