{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string"}, "principalId": {"type": "string"}, "roleDefinitionId": {"type": "string"}, "principalType": {"type": "string", "defaultValue": "ServicePrincipal"}}, "variables": {"roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', parameters('roleDefinitionId'))]"}, "resources": [{"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[guid(concat(parameters('topLevelManagementGroupPrefix'), parameters('roleDefinitionId')))]", "properties": {"principalType": "[parameters('principalType')]", "roleDefinitionId": "[variables('roleDefinitionId')]", "principalId": "[parameters('principalId')]"}}]}