{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "location for the the resources to deploy."}}, "userAssignedIdentityName": {"type": "string", "defaultValue": "id-ama-prod", "metadata": {"description": "The name of the Managed Identity resource."}}, "userAssignedIdentityResourceGroup": {"type": "String", "metadata": {"description": "The name of the resource group where the Managed Identity resource will be created."}}}, "variables": {}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2021-04-01", "name": "[parameters('userAssignedIdentityName')]", "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "variables": {}, "resources": [{"type": "Microsoft.ManagedIdentity/userAssignedIdentities", "name": "[parameters('userAssignedIdentityName')]", "apiVersion": "2018-11-30", "location": "[parameters('location')]"}]}}, "resourceGroup": "[parameters('userAssignedIdentityResourceGroup')]"}], "outputs": {}}