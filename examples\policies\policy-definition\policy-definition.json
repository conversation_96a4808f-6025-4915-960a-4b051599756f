{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"policyName": {"type": "string", "metadata": {"description": "Provide name for the policyDefinition."}}, "policyDescription": {"type": "string", "metadata": {"description": "Provide a description for the policy."}}, "namePattern": {"type": "string", "metadata": {"description": "Provide naming pattern."}}}, "resources": [{"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2019-09-01", "name": "[parameters('policyName')]", "properties": {"description": "[parameters('policyDescription')]", "displayName": "[parameters('policyName')]", "policyRule": {"if": {"not": {"field": "name", "like": "[parameters('namePattern')]"}}, "then": {"effect": "deny"}}}}]}