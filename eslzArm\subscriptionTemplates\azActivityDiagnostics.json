{"$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"logAnalytics": {"type": "string"}, "logsEnabled": {"type": "string", "allowedValues": ["True", "False"], "defaultValue": "True"}}, "variables": {}, "resources": [{"name": "subscriptionToLa", "type": "Microsoft.Insights/diagnosticSettings", "apiVersion": "2017-05-01-preview", "location": "Global", "properties": {"workspaceId": "[parameters('logAnalytics')]", "logs": [{"category": "Administrative", "enabled": "[parameters('logsEnabled')]"}, {"category": "Security", "enabled": "[parameters('logsEnabled')]"}, {"category": "ServiceHealth", "enabled": "[parameters('logsEnabled')]"}, {"category": "<PERSON><PERSON>", "enabled": "[parameters('logsEnabled')]"}, {"category": "Recommendation", "enabled": "[parameters('logsEnabled')]"}, {"category": "Policy", "enabled": "[parameters('logsEnabled')]"}, {"category": "Autoscale", "enabled": "[parameters('logsEnabled')]"}, {"category": "ResourceHealth", "enabled": "[parameters('logsEnabled')]"}]}}], "outputs": {}}