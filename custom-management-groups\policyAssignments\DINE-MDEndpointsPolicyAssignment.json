{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}, "enableMDEndpoints": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists", "AuditIfNotExists"], "defaultValue": "DeployIfNotExists"}}, "variables": {"policyDefinitions": {"deployMDEndpoints": "/providers/Microsoft.Authorization/policySetDefinitions/e20d08c5-6d64-656d-6465-ce9e37fd0ebc", "policyVersion": "1.*.*-preview"}, "policyAssignmentNames": {"azureSecurityMDE": "Deploy-MDEndpoints", "description": "Deploy Microsoft Defender for Endpoint agent on applicable images.", "displayName": "[Preview]: Deploy Microsoft Defender for Endpoint agent"}, "nonComplianceMessage": {"message": "Microsoft Defender for Endpoint agent {enforcementMode} be deployed on applicable images.", "Default": "must", "DoNotEnforce": "should"}, "rbacContributor": "b24988ac-6180-42a0-ab88-20f7382dd24c", "roleAssignmentNames": {"deployMDEndpoints": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureSecurityMDE))]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').azureSecurityMDE]", "location": "[deployment().location]", "identity": {"type": "SystemAssigned"}, "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').deployMDEndpoints]", "definitionVersion": "[variables('policyDefinitions').policyVersion]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {"microsoftDefenderForEndpointWindowsVmAgentDeployEffect": {"value": "[parameters('enableMDEndpoints')]"}, "microsoftDefenderForEndpointLinuxVmAgentDeployEffect": {"value": "[parameters('enableMDEndpoints')]"}, "microsoftDefenderForEndpointWindowsArcAgentDeployEffect": {"value": "[parameters('enableMDEndpoints')]"}, "microsoftDefenderForEndpointLinuxArcAgentDeployEffect": {"value": "[parameters('enableMDEndpoints')]"}}}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').deployMDEndpoints]", "dependsOn": ["[variables('policyAssignmentNames').azureSecurityMDE]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureSecurityMDE), '2019-09-01', 'Full' ).identity.principalId)]"}}], "outputs": {}}