{
    "$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
      "rgName": {
        "type": "String"
      },
      "workspaceName": {
        "type": "String"
      },
      "workspaceRegion": {
        "type": "String"
      },
      "retentionInDays": {
        "type": "String"
      },
      "enableSentinel": {
        "type": "String"
      }
    },
    "variables": {
        "deploymentName": "eslz-loganalytics"
    },
    "resources": [
      {
        "type": "Microsoft.Resources/resourceGroups",
        "apiVersion": "2018-05-01",
        "name": "[parameters('rgName')]",
        "location": "[deployment().location]",
        "properties": {}
      },
      {
        "type": "Microsoft.Resources/deployments",
        "apiVersion": "2024-03-01",
        "name": "[variables('deploymentName')]",
        "resourceGroup": "[parameters('rgName')]",
        "dependsOn": [
          "[resourceId('Microsoft.Resources/resourceGroups/', parameters('rgName'))]"
        ],
        "properties": {
          "mode": "Incremental",
          "template": {
            "$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json",
            "contentVersion": "*******",
            "parameters": {},
            "variables": {},
            "resources": [
              {
                "apiVersion": "2020-08-01",
                "location": "[parameters('workspaceRegion')]",
                "name": "[parameters('workspaceName')]",
                "type": "Microsoft.OperationalInsights/workspaces",
                "properties": {
                  "sku": {
                    "name": "PerGB2018"
                  },
                  "enableLogAccessUsingOnlyResourcePermissions": true,
                  "retentionInDays": "[int(parameters('retentionInDays'))]"
                }
              },
              {
                  // Onboard Sentinel
                  "condition": "[equals(parameters('enableSentinel'), 'Yes')]",
                  "apiVersion": "2023-02-01-preview",
                  "type": "Microsoft.SecurityInsights/onboardingStates",
                  "name": "default",
                  "scope": "[concat(subscription().id, '/resourceGroups/', parameters('rgName'), '/providers/Microsoft.OperationalInsights/workspaces/', parameters('workspaceName'))]",
                  "dependsOn": [
                      "[concat(subscription().id, '/resourceGroups/', parameters('rgName'), '/providers/Microsoft.OperationalInsights/workspaces/', parameters('workspaceName'))]"
                  ],
                  "properties": {
                      "customerManagedKey": false
                  }
              }
            ],
            "outputs": {}
          }
        }
      }
    ],
    "outputs": {}
  }