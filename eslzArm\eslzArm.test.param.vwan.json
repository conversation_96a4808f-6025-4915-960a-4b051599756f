{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"enterpriseScaleCompanyPrefix": {"value": "testPortal"}, "telemetryOptOut": {"value": "Yes"}, "enableLogAnalytics": {"value": "Yes"}, "retentionInDays": {"value": "30"}, "enableSentinel": {"value": "Yes"}, "enableChangeTracking": {"value": "Yes"}, "enableUpdateMgmt": {"value": "Yes"}, "enableVmInsights": {"value": "Yes"}, "enableAsc": {"value": "Yes"}, "emailContactAsc": {"value": "<EMAIL>"}, "enableAscForServers": {"value": "DeployIfNotExists"}, "enableAscForServersVulnerabilityAssessments": {"value": "DeployIfNotExists"}, "enableAscForOssDb": {"value": "DeployIfNotExists"}, "enableAscForCosmosDbs": {"value": "DeployIfNotExists"}, "enableAscForAppServices": {"value": "DeployIfNotExists"}, "enableAscForStorage": {"value": "DeployIfNotExists"}, "enableAscForSql": {"value": "DeployIfNotExists"}, "enableAscForSqlOnVm": {"value": "DeployIfNotExists"}, "enableAscForKeyVault": {"value": "DeployIfNotExists"}, "enableAscForArm": {"value": "DeployIfNotExists"}, "enableAscForApis": {"value": "DeployIfNotExists"}, "enableAscForCspm": {"value": "DeployIfNotExists"}, "enableAscForContainers": {"value": "DeployIfNotExists"}, "enableMDEndpoints": {"value": "DeployIfNotExists"}, "enableServiceHealth": {"value": "No"}, "enableServiceHealthBuiltIn": {"value": "Yes"}, "enableMonitorBaselines": {"value": "Yes"}, "enableMonitorConnectivity": {"value": "Yes"}, "enableMonitorIdentity": {"value": "Yes"}, "enableMonitorManagement": {"value": "Yes"}, "monitorAlertsResourceGroup": {"value": "rg-amba-monitoring-001"}, "userAssignedManagedIdentityName": {"value": "id-amba-prod-001"}, "ambaAgEmailContact": {"value": "<EMAIL>"}, "ambaAgServiceHook": {"value": ""}, "ambaAgArmRole": {"value": []}, "enableAMBAHybridVM": {"value": "Yes"}, "enableAMBAKeyManagement": {"value": "Yes"}, "enableAMBALoadBalancing": {"value": "Yes"}, "enableAMBANetworkChanges": {"value": "Yes"}, "enableAMBARecoveryServices": {"value": "Yes"}, "enableAMBAStorage": {"value": "Yes"}, "enableAMBAVM": {"value": "Yes"}, "enableAMBAWeb": {"value": "Yes"}, "addressPrefix": {"value": "**********/23"}, "connectivityLocation": {"value": "uksouth"}, "enableDdoS": {"value": "Yes"}, "enablePrivateDnsZones": {"value": "No"}, "privateDnsZonesToDeploy": {"value": []}, "enableVpnGw": {"value": "Yes"}, "enableVpnActiveActive": {"value": "No"}, "gwRegionalOrAz": {"value": ""}, "gwRegionalSku": {"value": ""}, "gwAzSku": {"value": ""}, "vpnGateWayScaleUnit": {"value": "1"}, "subnetMaskForGw": {"value": ""}, "enableErGw": {"value": "Yes"}, "erAzSku": {"value": ""}, "erRegionalSku": {"value": ""}, "erRegionalOrAz": {"value": ""}, "expressRouteScaleUnit": {"value": "1"}, "enableHub": {"value": "vwan"}, "enableAzFw": {"value": "Yes"}, "enableAzFwDnsProxy": {"value": "Yes"}, "firewallSku": {"value": "Premium"}, "firewallZones": {"value": ["1", "2", "3"]}, "subnetMaskForAzFw": {"value": ""}, "subnetMaskForAzFwMgmt": {"value": ""}, "enablevWANRoutingIntent": {"value": "Yes"}, "internetTrafficRoutingPolicy": {"value": true}, "privateTrafficRoutingPolicy": {"value": true}, "vWANHubRoutingPreference": {"value": "ExpressRoute"}, "vWANHubCapacity": {"value": "2"}, "addressPrefixSecondary": {"value": "**********/23"}, "connectivityLocationSecondary": {"value": "swedencentral"}, "enablePrivateDnsZonesSecondary": {"value": "No"}, "privateDnsZonesToDeploySecondary": {"value": []}, "enableVpnGwSecondary": {"value": "Yes"}, "enableVpnActiveActiveSecondary": {"value": "No"}, "gwRegionalOrAzSecondary": {"value": ""}, "gwRegionalSkuSecondary": {"value": ""}, "gwAzSkuSecondary": {"value": ""}, "vpnGateWayScaleUnitSecondary": {"value": "1"}, "subnetMaskForGwSecondary": {"value": ""}, "enableErGwSecondary": {"value": "Yes"}, "erAzSkuSecondary": {"value": ""}, "erRegionalSkuSecondary": {"value": ""}, "erRegionalOrAzSecondary": {"value": ""}, "expressRouteScaleUnitSecondary": {"value": "1"}, "enableSecondaryRegion": {"value": "Yes"}, "enableHubSecondary": {"value": "vwan"}, "enableAzFwSecondary": {"value": "Yes"}, "enableAzFwDnsProxySecondary": {"value": "Yes"}, "firewallSkuSecondary": {"value": "Premium"}, "firewallZonesSecondary": {"value": ["1", "2", "3"]}, "subnetMaskForAzFwSecondary": {"value": ""}, "subnetMaskForAzFwMgmtSecondary": {"value": ""}, "enablevWANRoutingIntentSecondary": {"value": "Yes"}, "internetTrafficRoutingPolicySecondary": {"value": true}, "privateTrafficRoutingPolicySecondary": {"value": true}, "vWANHubRoutingPreferenceSecondary": {"value": "ExpressRoute"}, "vWANHubCapacitySecondary": {"value": "2"}, "denyMgmtPortsForIdentity": {"value": "Yes"}, "denySubnetWithoutNsgForIdentity": {"value": "Yes"}, "denyPipForIdentity": {"value": "Yes"}, "denyPipOnNicForCorp": {"value": "Yes"}, "enableVmBackupForIdentity": {"value": "Yes"}, "identityAddressPrefix": {"value": "**********/24"}, "identityAddressPrefixSecondary": {"value": "**********/24"}, "enableLzDdoS": {"value": "Yes"}, "denyPublicEndpoints": {"value": "Yes"}, "enablePrivateDnsZonesForLzs": {"value": "Yes"}, "enableEncryptionInTransit": {"value": "Yes"}, "enableVmMonitoring": {"value": "Yes"}, "enableVmssMonitoring": {"value": "Yes"}, "enableVmHybridMonitoring": {"value": "Yes"}, "denyAksPrivileged": {"value": "Yes"}, "denyAksPrivilegedEscalation": {"value": "Yes"}, "denyHttpIngressForAks": {"value": "Yes"}, "enableVmBackup": {"value": "Yes"}, "denyMgmtPorts": {"value": "Yes"}, "denySubnetWithoutNsg": {"value": "Yes"}, "denyIpForwarding": {"value": "Yes"}, "denyClassicResources": {"value": "Yes"}, "denyVMUnmanagedDisk": {"value": "Yes"}, "enableSqlEncryption": {"value": "Yes"}, "enableSqlThreat": {"value": "Yes"}, "enableSqlAudit": {"value": "Yes"}, "enableDecommissioned": {"value": "Yes"}, "enableSandbox": {"value": "Yes"}, "enableStorageHttps": {"value": "Yes"}, "enforceKvGuardrails": {"value": "Yes"}, "enforceKvGuardrailsPlat": {"value": "Yes"}, "enforceBackupPlat": {"value": "Yes"}, "enforceBackup": {"value": "Yes"}, "denyHybridNetworking": {"value": "Yes"}, "auditPeDnsZones": {"value": "Yes"}, "auditAppGwWaf": {"value": "Yes"}, "enforceAcsb": {"value": "Yes"}, "delayCount": {"value": 45}, "enableWsCMKInitiatives": {"value": "No"}, "wsCMKSelectorMG": {"value": []}, "enableWsAPIMInitiatives": {"value": "No"}, "wsAPIMSelectorMG": {"value": []}, "enableWsAppServicesInitiatives": {"value": "No"}, "wsAppServicesSelectorMG": {"value": []}, "enableWsAutomationInitiatives": {"value": "No"}, "wsAutomationSelectorMG": {"value": []}, "enableWsCognitiveServicesInitiatives": {"value": "No"}, "wsCognitiveServicesSelectorMG": {"value": []}, "enableWsComputeInitiatives": {"value": "No"}, "wsComputeSelectorMG": {"value": []}, "enableWsContainerAppsInitiatives": {"value": "No"}, "wsContainerAppsSelectorMG": {"value": []}, "enableWsContainerInstanceInitiatives": {"value": "No"}, "wsContainerInstanceSelectorMG": {"value": []}, "enableWsContainerRegistryInitiatives": {"value": "No"}, "wsContainerRegistrySelectorMG": {"value": []}, "enableWsCosmosDbInitiatives": {"value": "No"}, "wsCosmosDbSelectorMG": {"value": []}, "enableWsDataExplorerInitiatives": {"value": "No"}, "wsDataExplorerSelectorMG": {"value": []}, "enableWsDataFactoryInitiatives": {"value": "No"}, "wsDataFactorySelectorMG": {"value": []}, "enableWsEventGridInitiatives": {"value": "No"}, "wsEventGridSelectorMG": {"value": []}, "enableWsEventHubInitiatives": {"value": "No"}, "wsEventHubSelectorMG": {"value": []}, "enableWsKeyVaultSupInitiatives": {"value": "No"}, "wsKeyVaultSupSelectorMG": {"value": []}, "enableWsKubernetesInitiatives": {"value": "No"}, "wsKubernetesSelectorMG": {"value": []}, "enableWsMachineLearningInitiatives": {"value": "No"}, "wsMachineLearningSelectorMG": {"value": []}, "enableWsMySQLInitiatives": {"value": "No"}, "wsMySQLSelectorMG": {"value": []}, "enableWsNetworkInitiatives": {"value": "No"}, "wsNetworkSelectorMG": {"value": []}, "enableWsOpenAIInitiatives": {"value": "No"}, "wsOpenAISelectorMG": {"value": []}, "enableWsPostgreSQLInitiatives": {"value": "No"}, "wsPostgreSQLSelectorMG": {"value": []}, "enableWsServiceBusInitiatives": {"value": "No"}, "wsServiceBusSelectorMG": {"value": []}, "enableWsSQLInitiatives": {"value": "No"}, "wsSQLSelectorMG": {"value": []}, "enableWsStorageInitiatives": {"value": "No"}, "wsStorageSelectorMG": {"value": []}, "enableWsSynapseInitiatives": {"value": "No"}, "wsSynapseSelectorMG": {"value": []}, "enableWsVirtualDesktopInitiatives": {"value": "No"}, "wsVirtualDesktopSelectorMG": {"value": []}, "regulatoryComplianceInitativesToAssign": {"value": []}, "regCompPolParAusGovIsmRestrictedVmAdminsExclude": {"value": ""}, "regCompPolParAusGovIsmRestrictedResourceTypes": {"value": "all"}, "regCompPolParMPAACertificateThumb": {"value": ""}, "regCompPolParMPAAApplicationName": {"value": ""}, "regCompPolParMPAAStoragePrefix": {"value": ""}, "regCompPolParMPAAResGroupPrefix": {"value": ""}, "regCompPolParMPAARBatchMetricName": {"value": ""}, "regCompPolParSovBaseConfRegions": {"value": []}, "regCompPolParSovBaseGlobalRegions": {"value": []}, "regCompPolParSwift2020VmAdminsInclude": {"value": ""}, "regCompPolParSwift2020DomainFqdn": {"value": ""}, "regCompPolParCanadaFedPbmmVmAdminsInclude": {"value": ""}, "regCompPolParCanadaFedPbmmVmAdminsExclude": {"value": ""}, "regCompPolParCisV2KeyVaultKeysRotateDays": {"value": 90}, "regCompPolParCmmcL3VmAdminsInclude": {"value": ""}, "regCompPolParCmmcL3VmAdminsExclude": {"value": ""}, "regCompPolParHitrustHipaaApplicationName": {"value": ""}, "regCompPolParHitrustHipaaStoragePrefix": {"value": ""}, "regCompPolParHitrustHipaaResGroupPrefix": {"value": ""}, "regCompPolParHitrustHipaaCertificateThumb": {"value": ""}, "regCompPolParIrs1075Sep2016VmAdminsExclude": {"value": ""}, "regCompPolParIrs1075Sep2016VmAdminsInclude": {"value": ""}, "regCompPolParNZIsmRestrictedVmAdminsInclude": {"value": ""}, "regCompPolParNZIsmRestrictedVmAdminsExclude": {"value": ""}, "regCompPolParNistSp800171R2VmAdminsExclude": {"value": ""}, "regCompPolParNistSp800171R2VmAdminsInclude": {"value": ""}, "regCompPolParSoc2Type2AllowedRegistries": {"value": ""}, "regCompPolParSoc2Type2MaxCpuUnits": {"value": ""}, "regCompPolParSoc2Type2MaxMemoryBytes": {"value": ""}, "listOfResourceTypesDisallowedForDeletion": {"value": ["microsoft.managedidentity/userassignedidentities"]}}}