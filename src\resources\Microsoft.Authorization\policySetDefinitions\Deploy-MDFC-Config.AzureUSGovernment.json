{"name": "Deploy-MDFC-Config", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Deploy Microsoft Defender for Cloud configuration", "description": "Deploy Microsoft Defender for Cloud configuration", "metadata": {"version": "3.0.1", "category": "Security Center", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureUSGovernment"]}, "parameters": {"emailSecurityContact": {"type": "string", "metadata": {"displayName": "Security contacts email address", "description": "Provide email address for Microsoft Defender for Cloud contact details"}}, "minimalSeverity": {"type": "string", "allowedValues": ["High", "Medium", "Low"], "defaultValue": "High", "metadata": {"displayName": "Minimal severity", "description": "Defines the minimal alert severity which will be sent as email notifications"}}, "logAnalytics": {"type": "String", "metadata": {"displayName": "Primary Log Analytics workspace", "description": "Select Log Analytics workspace from dropdown list. If this workspace is outside of the scope of the assignment you must manually grant 'Log Analytics Contributor' permissions (or similar) to the policy assignment's principal ID.", "strongType": "omsWorkspace"}}, "ascExportResourceGroupName": {"type": "String", "metadata": {"displayName": "Resource Group name for the export to Log Analytics workspace configuration", "description": "The resource group name where the export to Log Analytics workspace configuration is created. If you enter a name for a resource group that doesn't exist, it'll be created in the subscription. Note that each resource group can only have one export to Log Analytics workspace configured."}}, "ascExportResourceGroupLocation": {"type": "String", "metadata": {"displayName": "Resource Group location for the export to Log Analytics workspace configuration", "description": "The location where the resource group and the export to Log Analytics workspace configuration are created."}}, "enableAscForSql": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForDns": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForArm": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForContainers": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForStorage": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForServers": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyDefinitions": [{"policyDefinitionReferenceId": "defenderForVM", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8e86a5b6-b9bd-49d1-8e21-4bb8a0862222", "parameters": {"effect": {"value": "[[parameters('enableAscForServers')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defenderForStorageAccounts", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/74c30959-af11-47b3-9ed2-a26e03f427a3", "parameters": {"effect": {"value": "[[parameters('enableAscForStorage')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c9ddb292-b203-4738-aead-18e2716e858f", "parameters": {"effect": {"value": "[[parameters('enableAscForContainers')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defender<PERSON><PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2370a3c1-4a25-4283-a91a-c9c1a145fb2f", "parameters": {"effect": {"value": "[[parameters('enableAscForDns')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defender<PERSON><PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b7021b2b-08fd-4dc0-9de7-3c6ece09faf9", "parameters": {"effect": {"value": "[[parameters('enableAscForArm')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "defender<PERSON>or<PERSON>qlP<PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/b99b73e7-074b-4089-9395-b7236f094491", "parameters": {"effect": {"value": "[[parameters('enableAscForSql')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "securityEmailContact", "policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deploy-ASC-SecurityContacts", "parameters": {"emailSecurityContact": {"value": "[[parameters('emailSecurityContact')]"}, "minimalSeverity": {"value": "[[parameters('minimalSeverity')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "ascExport", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/ffb6f416-7bd2-4488-8828-56585fef2be9", "parameters": {"resourceGroupName": {"value": "[[parameters('ascExportResourceGroupName')]"}, "resourceGroupLocation": {"value": "[[parameters('ascExportResourceGroupLocation')]"}, "workspaceResourceId": {"value": "[[parameters('logAnalytics')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}