{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "topLevelManagementGroupPrefix": {
            "type": "string",
            "metadata": {
                "description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."
            }
        },
        "policySetDefinitionId": {
            "type": "string",
            "metadata": {
                "description": "Resource ID of the Policy Initative (Set Definition)"
            }
        },
        "policySetDefinitionDisplayName": {
            "type": "string",
            "metadata": {
                "description": "The Display Name for the Policy Initative (Set Definition)"
            }
        },
        "policySetDefinitionDescription": {
            "type": "string",
            "metadata": {
                "description": "The Description for the Policy Initative (Set Definition)"
            }
        },
        "policyAssignmentName": {
            "type": "string",
            "metadata": {
                "description": "The name for the Policy Assignment"
            }
        },
        "enforcementMode": {
            "type": "string",
            "allowedValues": [
                "Default",
                "DoNotEnforce"
            ],
            "defaultValue": "Default"
        },
        "logAnalyticsWorkspaceId": {
            "type": "string",
            "defaultValue": "",
            "metadata": {
                "description": "The Resource ID of the Log Analytics Workspace"
            }
        },
        "regCompPolParAusGovIsmRestrictedVmAdminsExclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParAusGovIsmRestrictedResourceTypes": {
            "type": "string",
            "defaultValue": "all"
        },
        "regCompPolParMPAACertificateThumb": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParMPAAApplicationName": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParMPAAStoragePrefix": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParMPAAResGroupPrefix": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParMPAARBatchMetricName": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParSovBaseConfRegions": {
            "type": "array",
            "defaultValue": []
        },
        "regCompPolParSovBaseGlobalRegions": {
            "type": "array",
            "defaultValue": []
        },
        "regCompPolParSwift2020VmAdminsInclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParSwift2020DomainFqdn": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParCanadaFedPbmmVmAdminsInclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParCanadaFedPbmmVmAdminsExclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParCisV2KeyVaultKeysRotateDays": {
            "type": "int",
            "defaultValue": 90
        },
        "regCompPolParCmmcL3VmAdminsInclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParCmmcL3VmAdminsExclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParHitrustHipaaApplicationName": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParHitrustHipaaStoragePrefix": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParHitrustHipaaResGroupPrefix": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParHitrustHipaaCertificateThumb": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParIrs1075Sep2016VmAdminsExclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParIrs1075Sep2016VmAdminsInclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParNZIsmRestrictedVmAdminsInclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParNZIsmRestrictedVmAdminsExclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParNistSp800171R2VmAdminsExclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParNistSp800171R2VmAdminsInclude": {
            "type": "string",
            "defaultValue": ""
        },
        "regCompPolParSoc2Type2AllowedRegistries": {
            "type": "string",
            "defaultValue": "^[^\\/]+\\.azurecr\\.io\\/.+$"
        },
        "regCompPolParSoc2Type2MaxCpuUnits": {
            "type": "string",
            "defaultValue": "200m"
        },
        "regCompPolParSoc2Type2MaxMemoryBytes": {
            "type": "string",
            "defaultValue": "1Gi"
        }
    },
    "variables": {
        "rbacContributor": "b24988ac-6180-42a0-ab88-20f7382dd24c",
        "roleAssignmentNames": {
            "deployRoles": "[guid(concat(parameters('topLevelManagementGroupPrefix'), parameters('policyAssignmentName')))]"
        },
        "knownPolicyInitativeDefinitionIdsThatRequireParamaeters": [
            "/providers/Microsoft.Authorization/policySetDefinitions/27272c0b-c225-4cc3-b8b0-f2534b093077",
            "/providers/Microsoft.Authorization/policySetDefinitions/92646f03-e39d-47a9-9e24-58d60ef49af8",
            "/providers/Microsoft.Authorization/policySetDefinitions/03de05a4-c324-4ccd-882f-a814ea8ab9ea",
            "/providers/Microsoft.Authorization/policySetDefinitions/c1cbff38-87c0-4b9f-9f70-035c7a3b5523",
            "/providers/Microsoft.Authorization/policySetDefinitions/3e0c67fc-8c7c-406c-89bd-6b6bdc986a22",
            "/providers/Microsoft.Authorization/policySetDefinitions/4c4a5f27-de81-430b-b4e5-9cbd50595a87",
            "/providers/Microsoft.Authorization/policySetDefinitions/06f19060-9e68-4070-92ca-f15cc126059e",
            "/providers/Microsoft.Authorization/policySetDefinitions/b5629c75-5c77-4422-87b9-2509e680f8de",
            "/providers/Microsoft.Authorization/policySetDefinitions/a169a624-5599-4385-a696-c8d643089fab",
            "/providers/Microsoft.Authorization/policySetDefinitions/105e0327-6175-4eb2-9af4-1fba43bdb39d",
            "/providers/Microsoft.Authorization/policySetDefinitions/d1a462af-7e6d-4901-98ac-61570b4ed22a",
            "/providers/Microsoft.Authorization/policySetDefinitions/03055927-78bd-4236-86c0-f36125a10dc9",
            "/providers/Microsoft.Authorization/policySetDefinitions/4054785f-702b-4a98-9215-009cbd58b141"
        ],
        "allResourceTypes": [
            "Microsoft.Security/operations",
            "Microsoft.Security/securityStatuses",
            "Microsoft.Security/tasks",
            "Microsoft.Security/secureScores",
            "Microsoft.Security/secureScores/secureScoreControls",
            "Microsoft.Security/secureScoreControls",
            "Microsoft.Security/secureScoreControlDefinitions",
            "Microsoft.Security/connectors",
            "Microsoft.Security/regulatoryComplianceStandards",
            "Microsoft.Security/regulatoryComplianceStandards/regulatoryComplianceControls",
            "Microsoft.Security/regulatoryComplianceStandards/regulatoryComplianceControls/regulatoryComplianceAssessments",
            "Microsoft.Security/alerts",
            "Microsoft.Security/alertsSuppressionRules",
            "Microsoft.Security/autoDismissAlertsRules",
            "Microsoft.Security/dataCollectionAgents",
            "Microsoft.Security/pricings",
            "Microsoft.Security/pricings/securityOperators",
            "Microsoft.Security/AutoProvisioningSettings",
            "Microsoft.Security/MdeOnboardings",
            "Microsoft.Security/vmScanners",
            "Microsoft.Security/Compliances",
            "Microsoft.Security/securityContacts",
            "Microsoft.Security/workspaceSettings",
            "Microsoft.Security/complianceResults",
            "Microsoft.Security/policies",
            "Microsoft.Security/assessments",
            "Microsoft.Security/governanceRules",
            "Microsoft.Security/assessments/governanceAssignments",
            "Microsoft.Security/assessmentMetadata",
            "Microsoft.Security/subAssessments",
            "Microsoft.Security/securitySolutions",
            "Microsoft.Security/locations/securitySolutions",
            "Microsoft.Security/discoveredSecuritySolutions",
            "Microsoft.Security/locations/discoveredSecuritySolutions",
            "Microsoft.Security/allowedConnections",
            "Microsoft.Security/locations/allowedConnections",
            "Microsoft.Security/topologies",
            "Microsoft.Security/locations/topologies",
            "Microsoft.Security/securitySolutionsReferenceData",
            "Microsoft.Security/locations/securitySolutionsReferenceData",
            "Microsoft.Security/jitPolicies",
            "Microsoft.Security/jitNetworkAccessPolicies",
            "Microsoft.Security/locations/jitNetworkAccessPolicies",
            "Microsoft.Security/locations",
            "Microsoft.Security/securityStatusesSummaries",
            "Microsoft.Security/applicationWhitelistings",
            "Microsoft.Security/locations/applicationWhitelistings",
            "Microsoft.Security/locations/alerts",
            "Microsoft.Security/locations/tasks",
            "Microsoft.Security/externalSecuritySolutions",
            "Microsoft.Security/locations/externalSecuritySolutions",
            "Microsoft.Security/InformationProtectionPolicies",
            "Microsoft.Security/advancedThreatProtectionSettings",
            "Microsoft.Security/sqlVulnerabilityAssessments",
            "Microsoft.Security/deviceSecurityGroups",
            "Microsoft.Security/iotSecuritySolutions",
            "Microsoft.Security/iotSecuritySolutions/analyticsModels",
            "Microsoft.Security/iotSecuritySolutions/iotAlertTypes",
            "Microsoft.Security/iotSecuritySolutions/iotAlerts",
            "Microsoft.Security/iotSecuritySolutions/iotRecommendationTypes",
            "Microsoft.Security/iotSecuritySolutions/iotRecommendations",
            "Microsoft.Security/iotSecuritySolutions/analyticsModels/aggregatedAlerts",
            "Microsoft.Security/iotSecuritySolutions/analyticsModels/aggregatedRecommendations",
            "Microsoft.Security/settings",
            "Microsoft.Security/serverVulnerabilityAssessments",
            "Microsoft.Security/serverVulnerabilityAssessmentsSettings",
            "Microsoft.Security/adaptiveNetworkHardenings",
            "Microsoft.Security/automations",
            "Microsoft.Security/defenderForStorageSettings",
            "Microsoft.Security/dataScanners",
            "Microsoft.Security/securityConnectors",
            "Microsoft.Security/securityConnectors/devops",
            "Microsoft.Security/customRecommendations",
            "Microsoft.Security/customAssessmentAutomations",
            "Microsoft.Security/securityStandards",
            "Microsoft.Security/standards",
            "Microsoft.Security/standardAssignments",
            "Microsoft.Security/assignments",
            "Microsoft.Security/sensitivitySettings",
            "Microsoft.Security/query",
            "Microsoft.Security/applications",
            "Microsoft.Security/apiCollections",
            "Microsoft.Security/healthReports",
            "Microsoft.Security/aggregations",
            "Microsoft.Security/integrations",
            "Microsoft.PolicyInsights/policyEvents",
            "Microsoft.PolicyInsights/policyStates",
            "Microsoft.PolicyInsights/operations",
            "Microsoft.PolicyInsights/asyncOperationResults",
            "Microsoft.PolicyInsights/remediations",
            "Microsoft.PolicyInsights/eventGridFilters",
            "Microsoft.PolicyInsights/checkPolicyRestrictions",
            "Microsoft.PolicyInsights/policyTrackedResources",
            "Microsoft.PolicyInsights/policyMetadata",
            "Microsoft.Management/resources",
            "Microsoft.Management/managementGroups",
            "Microsoft.Management/getEntities",
            "Microsoft.Management/managementGroups/settings",
            "Microsoft.Management/checkNameAvailability",
            "Microsoft.Management/operationResults",
            "Microsoft.Management/operationResults/asyncOperation",
            "Microsoft.Management/operations",
            "Microsoft.Management/tenantBackfillStatus",
            "Microsoft.Management/startTenantBackfill",
            "Microsoft.Storage/storageAccounts/storageTaskAssignments",
            "Microsoft.Storage/storageAccounts/encryptionScopes",
            "Microsoft.Storage/deletedAccounts",
            "Microsoft.Storage/locations/deletedAccounts",
            "Microsoft.Storage/storageAccounts",
            "Microsoft.Storage/storageTasks",
            "Microsoft.Storage/operations",
            "Microsoft.Storage/locations/asyncoperations",
            "Microsoft.Storage/storageAccounts/listAccountSas",
            "Microsoft.Storage/storageAccounts/listServiceSas",
            "Microsoft.Storage/storageAccounts/blobServices",
            "Microsoft.Storage/storageAccounts/tableServices",
            "Microsoft.Storage/storageAccounts/queueServices",
            "Microsoft.Storage/storageAccounts/fileServices",
            "Microsoft.Storage/locations",
            "Microsoft.Storage/locations/usages",
            "Microsoft.Storage/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.Storage/usages",
            "Microsoft.Storage/checkNameAvailability",
            "Microsoft.Storage/locations/checkNameAvailability",
            "Microsoft.Storage/storageAccounts/services",
            "Microsoft.Storage/storageAccounts/services/metricDefinitions",
            "Microsoft.Storage/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.OperationalInsights/workspaces",
            "Microsoft.OperationalInsights/querypacks",
            "Microsoft.OperationalInsights/locations",
            "Microsoft.OperationalInsights/locations/operationStatuses",
            "Microsoft.OperationalInsights/workspaces/scopedPrivateLinkProxies",
            "Microsoft.OperationalInsights/workspaces/api",
            "Microsoft.OperationalInsights/workspaces/query",
            "Microsoft.OperationalInsights/workspaces/metadata",
            "Microsoft.OperationalInsights/workspaces/purge",
            "Microsoft.OperationalInsights/workspaces/operations",
            "Microsoft.OperationalInsights/workspaces/dataSources",
            "Microsoft.OperationalInsights/workspaces/linkedStorageAccounts",
            "Microsoft.OperationalInsights/workspaces/tables",
            "Microsoft.OperationalInsights/workspaces/storageInsightConfigs",
            "Microsoft.OperationalInsights/storageInsightConfigs",
            "Microsoft.OperationalInsights/workspaces/linkedServices",
            "Microsoft.OperationalInsights/linkTargets",
            "Microsoft.OperationalInsights/deletedWorkspaces",
            "Microsoft.OperationalInsights/operations",
            "Microsoft.OperationalInsights/clusters",
            "Microsoft.OperationalInsights/workspaces/dataExports",
            "Microsoft.OperationalInsights/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.Automation/automationAccounts",
            "Microsoft.Automation/deletedAutomationAccounts",
            "Microsoft.Automation/automationAccounts/runbooks",
            "Microsoft.Automation/automationAccounts/configurations",
            "Microsoft.Automation/automationAccounts/webhooks",
            "Microsoft.Automation/operations",
            "Microsoft.Automation/automationAccounts/softwareUpdateConfigurations",
            "Microsoft.Automation/automationAccounts/softwareUpdateConfigurationRuns",
            "Microsoft.Automation/automationAccounts/softwareUpdateConfigurationMachineRuns",
            "Microsoft.Automation/automationAccounts/jobs",
            "Microsoft.Automation/automationAccounts/privateLinkResources",
            "Microsoft.Automation/automationAccounts/privateEndpointConnections",
            "Microsoft.Automation/automationAccounts/privateEndpointConnectionProxies",
            "Microsoft.Automation/automationAccounts/hybridRunbookWorkerGroups",
            "Microsoft.Automation/automationAccounts/hybridRunbookWorkerGroups/hybridRunbookWorkers",
            "Microsoft.Automation/automationAccounts/agentRegistrationInformation",
            "Microsoft.Network/virtualNetworkGateways",
            "Microsoft.Network/localNetworkGateways",
            "Microsoft.Network/connections",
            "Microsoft.Network/applicationGateways",
            "Microsoft.Network/expressRouteCircuits",
            "Microsoft.Network/expressRouteServiceProviders",
            "Microsoft.Network/applicationGatewayAvailableWafRuleSets",
            "Microsoft.Network/applicationGatewayAvailableSslOptions",
            "Microsoft.Network/applicationGatewayAvailableServerVariables",
            "Microsoft.Network/applicationGatewayAvailableRequestHeaders",
            "Microsoft.Network/applicationGatewayAvailableResponseHeaders",
            "Microsoft.Network/routeFilters",
            "Microsoft.Network/bgpServiceCommunities",
            "Microsoft.Network/vpnSites",
            "Microsoft.Network/vpnServerConfigurations",
            "Microsoft.Network/virtualHubs",
            "Microsoft.Network/vpnGateways",
            "Microsoft.Network/p2sVpnGateways",
            "Microsoft.Network/expressRouteGateways",
            "Microsoft.Network/expressRoutePortsLocations",
            "Microsoft.Network/expressRoutePorts",
            "Microsoft.Network/securityPartnerProviders",
            "Microsoft.Network/azureFirewalls",
            "Microsoft.Network/azureFirewallFqdnTags",
            "Microsoft.Network/applicationGatewayWebApplicationFirewallPolicies",
            "Microsoft.Network/locations/ApplicationGatewayWafDynamicManifests",
            "Microsoft.Network/virtualWans",
            "Microsoft.Network/bastionHosts",
            "Microsoft.Network/queryExpressRoutePortsBandwidth",
            "Microsoft.Network/trafficmanagerprofiles",
            "Microsoft.Network/trafficmanagerprofiles/heatMaps",
            "Microsoft.Network/trafficmanagerprofiles/azureendpoints",
            "Microsoft.Network/trafficmanagerprofiles/externalendpoints",
            "Microsoft.Network/trafficmanagerprofiles/nestedendpoints",
            "Microsoft.Network/checkTrafficManagerNameAvailability",
            "Microsoft.Network/checkTrafficManagerNameAvailabilityV2",
            "Microsoft.Network/trafficManagerUserMetricsKeys",
            "Microsoft.Network/trafficManagerGeographicHierarchies",
            "Microsoft.Network/expressRouteProviderPorts",
            "Microsoft.Network/locations/hybridEdgeZone",
            "Microsoft.Network/firewallPolicies",
            "Microsoft.Network/ipGroups",
            "Microsoft.Network/azureWebCategories",
            "Microsoft.Network/locations/nfvOperations",
            "Microsoft.Network/locations/nfvOperationResults",
            "Microsoft.Network/virtualRouters",
            "Microsoft.Network/networkVirtualAppliances",
            "Microsoft.Network/networkVirtualApplianceSkus",
            "Microsoft.Network/frontdoorOperationResults",
            "Microsoft.Network/checkFrontdoorNameAvailability",
            "Microsoft.Network/frontdoors",
            "Microsoft.Network/frontdoors/frontendEndpoints",
            "Microsoft.Network/frontdoors/frontendEndpoints/customHttpsConfiguration",
            "Microsoft.Network/frontdoorWebApplicationFirewallPolicies",
            "Microsoft.Network/frontdoorWebApplicationFirewallManagedRuleSets",
            "Microsoft.Network/networkExperimentProfiles",
            "Microsoft.Network/networkManagers",
            "Microsoft.Network/networkManagerConnections",
            "Microsoft.Network/networkSecurityPerimeters",
            "Microsoft.Network/locations/perimeterAssociableResourceTypes",
            "Microsoft.Network/locations/queryNetworkSecurityPerimeter",
            "Microsoft.Network/virtualNetworks/listNetworkManagerEffectiveConnectivityConfigurations",
            "Microsoft.Network/virtualNetworks/listNetworkManagerEffectiveSecurityAdminRules",
            "Microsoft.Network/networkGroupMemberships",
            "Microsoft.Network/locations/commitInternalAzureNetworkManagerConfiguration",
            "Microsoft.Network/locations/internalAzureVirtualNetworkManagerOperation",
            "Microsoft.Network/privateDnsZones",
            "Microsoft.Network/privateDnsZones/virtualNetworkLinks",
            "Microsoft.Network/privateDnsOperationResults",
            "Microsoft.Network/privateDnsOperationStatuses",
            "Microsoft.Network/privateDnsZonesInternal",
            "Microsoft.Network/privateDnsZones/A",
            "Microsoft.Network/privateDnsZones/AAAA",
            "Microsoft.Network/privateDnsZones/CNAME",
            "Microsoft.Network/privateDnsZones/PTR",
            "Microsoft.Network/privateDnsZones/MX",
            "Microsoft.Network/privateDnsZones/TXT",
            "Microsoft.Network/privateDnsZones/SRV",
            "Microsoft.Network/privateDnsZones/SOA",
            "Microsoft.Network/privateDnsZones/all",
            "Microsoft.Network/virtualNetworks/privateDnsZoneLinks",
            "Microsoft.Network/dnsResolvers",
            "Microsoft.Network/dnsResolvers/inboundEndpoints",
            "Microsoft.Network/dnsResolvers/outboundEndpoints",
            "Microsoft.Network/dnsForwardingRulesets",
            "Microsoft.Network/dnsForwardingRulesets/forwardingRules",
            "Microsoft.Network/dnsForwardingRulesets/virtualNetworkLinks",
            "Microsoft.Network/virtualNetworks/listDnsResolvers",
            "Microsoft.Network/virtualNetworks/listDnsForwardingRulesets",
            "Microsoft.Network/locations/dnsResolverOperationResults",
            "Microsoft.Network/locations/dnsResolverOperationStatuses",
            "Microsoft.Network/locations/dnsResolverPolicyOperationResults",
            "Microsoft.Network/locations/dnsResolverPolicyOperationStatuses",
            "Microsoft.Network/dnszones",
            "Microsoft.Network/dnsOperationResults",
            "Microsoft.Network/dnsOperationStatuses",
            "Microsoft.Network/getDnsResourceReference",
            "Microsoft.Network/internalNotify",
            "Microsoft.Network/dnszones/A",
            "Microsoft.Network/dnszones/AAAA",
            "Microsoft.Network/dnszones/CNAME",
            "Microsoft.Network/dnszones/PTR",
            "Microsoft.Network/dnszones/MX",
            "Microsoft.Network/dnszones/TXT",
            "Microsoft.Network/dnszones/SRV",
            "Microsoft.Network/dnszones/SOA",
            "Microsoft.Network/dnszones/NS",
            "Microsoft.Network/dnszones/CAA",
            "Microsoft.Network/dnszones/DS",
            "Microsoft.Network/dnszones/TLSA",
            "Microsoft.Network/dnszones/NAPTR",
            "Microsoft.Network/dnszones/recordsets",
            "Microsoft.Network/dnszones/all",
            "Microsoft.Network/dnszones/dnssecConfigs",
            "Microsoft.Network/virtualNetworks",
            "Microsoft.Network/virtualNetworks/taggedTrafficConsumers",
            "Microsoft.Network/natGateways",
            "Microsoft.Network/publicIPAddresses",
            "Microsoft.Network/internalPublicIpAddresses",
            "Microsoft.Network/customIpPrefixes",
            "Microsoft.Network/networkInterfaces",
            "Microsoft.Network/dscpConfigurations",
            "Microsoft.Network/privateEndpoints",
            "Microsoft.Network/privateEndpoints/privateLinkServiceProxies",
            "Microsoft.Network/privateEndpointRedirectMaps",
            "Microsoft.Network/loadBalancers",
            "Microsoft.Network/networkSecurityGroups",
            "Microsoft.Network/applicationSecurityGroups",
            "Microsoft.Network/serviceEndpointPolicies",
            "Microsoft.Network/networkIntentPolicies",
            "Microsoft.Network/routeTables",
            "Microsoft.Network/publicIPPrefixes",
            "Microsoft.Network/networkWatchers",
            "Microsoft.Network/networkWatchers/connectionMonitors",
            "Microsoft.Network/networkWatchers/flowLogs",
            "Microsoft.Network/networkWatchers/pingMeshes",
            "Microsoft.Network/locations",
            "Microsoft.Network/locations/operations",
            "Microsoft.Network/locations/operationResults",
            "Microsoft.Network/locations/CheckDnsNameAvailability",
            "Microsoft.Network/locations/setLoadBalancerFrontendPublicIpAddresses",
            "Microsoft.Network/cloudServiceSlots",
            "Microsoft.Network/locations/usages",
            "Microsoft.Network/locations/virtualNetworkAvailableEndpointServices",
            "Microsoft.Network/locations/availableDelegations",
            "Microsoft.Network/locations/serviceTags",
            "Microsoft.Network/locations/availablePrivateEndpointTypes",
            "Microsoft.Network/locations/availableServiceAliases",
            "Microsoft.Network/locations/checkPrivateLinkServiceVisibility",
            "Microsoft.Network/locations/autoApprovedPrivateLinkServices",
            "Microsoft.Network/locations/batchValidatePrivateEndpointsForResourceMove",
            "Microsoft.Network/locations/batchNotifyPrivateEndpointsForResourceMove",
            "Microsoft.Network/locations/supportedVirtualMachineSizes",
            "Microsoft.Network/locations/setAzureNetworkManagerConfiguration",
            "Microsoft.Network/locations/publishResources",
            "Microsoft.Network/locations/getAzureNetworkManagerConfiguration",
            "Microsoft.Network/locations/checkAcceleratedNetworkingSupport",
            "Microsoft.Network/locations/validateResourceOwnership",
            "Microsoft.Network/locations/setResourceOwnership",
            "Microsoft.Network/locations/effectiveResourceOwnership",
            "Microsoft.Network/operations",
            "Microsoft.Network/virtualNetworkTaps",
            "Microsoft.Network/privateLinkServices",
            "Microsoft.Network/locations/privateLinkServices",
            "Microsoft.Network/ddosProtectionPlans",
            "Microsoft.Network/networkProfiles",
            "Microsoft.Network/locations/bareMetalTenants",
            "Microsoft.Network/ipAllocations",
            "Microsoft.Network/locations/serviceTagDetails",
            "Microsoft.Network/locations/dataTasks",
            "Microsoft.Network/locations/startPacketTagging",
            "Microsoft.Network/locations/deletePacketTagging",
            "Microsoft.Network/locations/getPacketTagging",
            "Microsoft.Network/locations/rnmEffectiveRouteTable",
            "Microsoft.Network/locations/rnmEffectiveNetworkSecurityGroups",
            "Microsoft.Compute/availabilitySets",
            "Microsoft.Compute/virtualMachines",
            "Microsoft.Compute/virtualMachines/extensions",
            "Microsoft.Compute/virtualMachineScaleSets",
            "Microsoft.Compute/virtualMachineScaleSets/extensions",
            "Microsoft.Compute/virtualMachineScaleSets/virtualMachines",
            "Microsoft.Compute/virtualMachineScaleSets/virtualMachines/extensions",
            "Microsoft.Compute/virtualMachineScaleSets/networkInterfaces",
            "Microsoft.Compute/virtualMachineScaleSets/virtualMachines/networkInterfaces",
            "Microsoft.Compute/virtualMachineScaleSets/publicIPAddresses",
            "Microsoft.Compute/locations",
            "Microsoft.Compute/locations/operations",
            "Microsoft.Compute/locations/vmSizes",
            "Microsoft.Compute/locations/runCommands",
            "Microsoft.Compute/locations/virtualMachines",
            "Microsoft.Compute/locations/virtualMachineScaleSets",
            "Microsoft.Compute/locations/publishers",
            "Microsoft.Compute/operations",
            "Microsoft.Compute/virtualMachines/runCommands",
            "Microsoft.Compute/virtualMachineScaleSets/applications",
            "Microsoft.Compute/virtualMachines/VMApplications",
            "Microsoft.Compute/locations/edgeZones",
            "Microsoft.Compute/locations/edgeZones/vmimages",
            "Microsoft.Compute/locations/edgeZones/publishers",
            "Microsoft.Compute/restorePointCollections",
            "Microsoft.Compute/restorePointCollections/restorePoints",
            "Microsoft.Compute/proximityPlacementGroups",
            "Microsoft.Compute/sshPublicKeys",
            "Microsoft.Compute/capacityReservationGroups",
            "Microsoft.Compute/capacityReservationGroups/capacityReservations",
            "Microsoft.Compute/virtualMachines/metricDefinitions",
            "Microsoft.Compute/locations/spotEvictionRates",
            "Microsoft.Compute/locations/spotPriceHistory",
            "Microsoft.Compute/locations/recommendations",
            "Microsoft.Compute/locations/sharedGalleries",
            "Microsoft.Compute/locations/communityGalleries",
            "Microsoft.Compute/sharedVMImages",
            "Microsoft.Compute/sharedVMImages/versions",
            "Microsoft.Compute/locations/artifactPublishers",
            "Microsoft.Compute/locations/capsoperations",
            "Microsoft.Compute/galleries",
            "Microsoft.Compute/galleries/images",
            "Microsoft.Compute/galleries/images/versions",
            "Microsoft.Compute/locations/galleries",
            "Microsoft.Compute/payloadGroups",
            "Microsoft.Compute/galleries/applications",
            "Microsoft.Compute/galleries/applications/versions",
            "Microsoft.Compute/disks",
            "Microsoft.Compute/snapshots",
            "Microsoft.Compute/locations/diskoperations",
            "Microsoft.Compute/diskEncryptionSets",
            "Microsoft.Compute/diskAccesses",
            "Microsoft.Compute/restorePointCollections/restorePoints/diskRestorePoints",
            "Microsoft.Compute/virtualMachineScaleSets/disks",
            "Microsoft.Compute/cloudServices",
            "Microsoft.Compute/cloudServices/roles",
            "Microsoft.Compute/cloudServices/roleInstances",
            "Microsoft.Compute/locations/csoperations",
            "Microsoft.Compute/locations/cloudServiceOsVersions",
            "Microsoft.Compute/locations/cloudServiceOsFamilies",
            "Microsoft.Compute/cloudServices/networkInterfaces",
            "Microsoft.Compute/cloudServices/roleInstances/networkInterfaces",
            "Microsoft.Compute/cloudServices/publicIPAddresses",
            "Microsoft.Compute/locations/usages",
            "Microsoft.Compute/images",
            "Microsoft.Compute/locations/diagnostics",
            "Microsoft.Compute/locations/diagnosticOperations",
            "Microsoft.Compute/locations/logAnalytics",
            "Microsoft.Compute/hostGroups",
            "Microsoft.Compute/hostGroups/hosts",
            "Microsoft.ResourceHealth/availabilityStatuses",
            "Microsoft.ResourceHealth/childAvailabilityStatuses",
            "Microsoft.ResourceHealth/childResources",
            "Microsoft.ResourceHealth/events",
            "Microsoft.ResourceHealth/metadata",
            "Microsoft.ResourceHealth/emergingissues",
            "Microsoft.ResourceHealth/operations",
            "microsoft.insights/components",
            "microsoft.insights/components/query",
            "microsoft.insights/components/metadata",
            "microsoft.insights/components/metrics",
            "microsoft.insights/components/events",
            "microsoft.insights/components/syntheticmonitorlocations",
            "microsoft.insights/components/analyticsItems",
            "microsoft.insights/components/webtests",
            "microsoft.insights/components/workItemConfigs",
            "microsoft.insights/components/myFavorites",
            "microsoft.insights/components/operations",
            "microsoft.insights/components/exportConfiguration",
            "microsoft.insights/components/purge",
            "microsoft.insights/components/api",
            "microsoft.insights/components/aggregate",
            "microsoft.insights/components/metricDefinitions",
            "microsoft.insights/components/extendQueries",
            "microsoft.insights/components/apiKeys",
            "microsoft.insights/components/myAnalyticsItems",
            "microsoft.insights/components/favorites",
            "microsoft.insights/components/defaultWorkItemConfig",
            "microsoft.insights/components/annotations",
            "microsoft.insights/components/proactiveDetectionConfigs",
            "microsoft.insights/components/move",
            "microsoft.insights/components/currentBillingFeatures",
            "microsoft.insights/components/quotaStatus",
            "microsoft.insights/components/featureCapabilities",
            "microsoft.insights/components/getAvailableBillingFeatures",
            "microsoft.insights/webtests",
            "microsoft.insights/webtests/getTestResultFile",
            "microsoft.insights/scheduledqueryrules",
            "microsoft.insights/components/pricingPlans",
            "microsoft.insights/migrateToNewPricingModel",
            "microsoft.insights/rollbackToLegacyPricingModel",
            "microsoft.insights/listMigrationdate",
            "microsoft.insights/logprofiles",
            "microsoft.insights/migratealertrules",
            "microsoft.insights/metricalerts",
            "microsoft.insights/alertrules",
            "microsoft.insights/autoscalesettings",
            "microsoft.insights/eventtypes",
            "microsoft.insights/locations",
            "microsoft.insights/locations/operationResults",
            "microsoft.insights/vmInsightsOnboardingStatuses",
            "microsoft.insights/operations",
            "microsoft.insights/diagnosticSettings",
            "microsoft.insights/diagnosticSettingsCategories",
            "microsoft.insights/extendedDiagnosticSettings",
            "microsoft.insights/metricDefinitions",
            "microsoft.insights/logDefinitions",
            "microsoft.insights/eventCategories",
            "microsoft.insights/metrics",
            "microsoft.insights/metricbatch",
            "microsoft.insights/metricNamespaces",
            "microsoft.insights/notificationstatus",
            "microsoft.insights/createnotifications",
            "microsoft.insights/tenantactiongroups",
            "microsoft.insights/actiongroups",
            "microsoft.insights/activityLogAlerts",
            "microsoft.insights/metricbaselines",
            "microsoft.insights/workbooks",
            "microsoft.insights/workbooktemplates",
            "microsoft.insights/myWorkbooks",
            "microsoft.insights/logs",
            "microsoft.insights/transactions",
            "microsoft.insights/topology",
            "microsoft.insights/generateLiveToken",
            "microsoft.insights/monitoredObjects",
            "microsoft.insights/dataCollectionRules",
            "microsoft.insights/dataCollectionRuleAssociations",
            "microsoft.insights/dataCollectionEndpoints",
            "microsoft.insights/dataCollectionEndpoints/scopedPrivateLinkProxies",
            "microsoft.insights/privateLinkScopes",
            "microsoft.insights/privateLinkScopes/privateEndpointConnections",
            "microsoft.insights/privateLinkScopes/privateEndpointConnectionProxies",
            "microsoft.insights/privateLinkScopes/scopedResources",
            "microsoft.insights/components/linkedstorageaccounts",
            "microsoft.insights/privateLinkScopeOperationStatuses",
            "microsoft.insights/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.ManagedServices/registrationDefinitions",
            "Microsoft.ManagedServices/registrationAssignments",
            "Microsoft.ManagedServices/operations",
            "Microsoft.ManagedServices/marketplaceRegistrationDefinitions",
            "Microsoft.ManagedServices/operationStatuses",
            "Microsoft.HDInsight/clusters",
            "Microsoft.HDInsight/clusters/applications",
            "Microsoft.HDInsight/clusters/operationresults",
            "Microsoft.HDInsight/locations",
            "Microsoft.HDInsight/locations/capabilities",
            "Microsoft.HDInsight/locations/usages",
            "Microsoft.HDInsight/locations/billingSpecs",
            "Microsoft.HDInsight/locations/operationresults",
            "Microsoft.HDInsight/locations/azureasyncoperations",
            "Microsoft.HDInsight/locations/validateCreateRequest",
            "Microsoft.HDInsight/operations",
            "Microsoft.HDInsight/locations/operationStatuses",
            "Microsoft.HDInsight/clusterPools",
            "Microsoft.HDInsight/clusterPools/clusters",
            "Microsoft.HDInsight/locations/clusterOfferingVersions",
            "Microsoft.HDInsight/locations/availableClusterPoolVersions",
            "Microsoft.HDInsight/locations/availableClusterVersions",
            "Microsoft.HDInsight/locations/checkNameAvailability",
            "Microsoft.HDInsight/clusterPools/clusters/serviceConfigs",
            "Microsoft.HDInsight/clusterPools/clusters/instanceViews",
            "Microsoft.HDInsight/clusterPools/clusters/jobs",
            "Microsoft.AlertsManagement/alerts",
            "Microsoft.AlertsManagement/alertsSummary",
            "Microsoft.AlertsManagement/smartGroups",
            "Microsoft.AlertsManagement/smartDetectorAlertRules",
            "Microsoft.AlertsManagement/migrateFromSmartDetection",
            "Microsoft.AlertsManagement/actionRules",
            "Microsoft.AlertsManagement/alertsMetaData",
            "Microsoft.AlertsManagement/prometheusRuleGroups",
            "Microsoft.AlertsManagement/operations",
            "Microsoft.AlertsManagement/alertRuleRecommendations",
            "Microsoft.AlertsManagement/tenantActivityLogAlerts",
            "Microsoft.AlertsManagement/investigations",
            "Microsoft.OperationsManagement/solutions",
            "Microsoft.OperationsManagement/managementassociations",
            "Microsoft.OperationsManagement/views",
            "Microsoft.OperationsManagement/operations",
            "Microsoft.KeyVault/vaults",
            "Microsoft.KeyVault/vaults/secrets",
            "Microsoft.KeyVault/vaults/accessPolicies",
            "Microsoft.KeyVault/operations",
            "Microsoft.KeyVault/checkNameAvailability",
            "Microsoft.KeyVault/deletedVaults",
            "Microsoft.KeyVault/locations",
            "Microsoft.KeyVault/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.KeyVault/locations/deletedVaults",
            "Microsoft.KeyVault/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.KeyVault/locations/operationResults",
            "Microsoft.KeyVault/vaults/eventGridFilters",
            "Microsoft.KeyVault/managedHSMs",
            "Microsoft.KeyVault/deletedManagedHSMs",
            "Microsoft.KeyVault/locations/deletedManagedHSMs",
            "Microsoft.KeyVault/locations/managedHsmOperationResults",
            "Microsoft.KeyVault/managedHSMs/keys",
            "Microsoft.KeyVault/managedHSMs/keys/versions",
            "Microsoft.KeyVault/checkMhsmNameAvailability",
            "Microsoft.KeyVault/vaults/keys",
            "Microsoft.KeyVault/vaults/keys/versions",
            "Microsoft.ContainerService/ManagedClusters/eventGridFilters",
            "Microsoft.ContainerService/fleetMemberships",
            "Microsoft.ContainerService/fleets",
            "Microsoft.ContainerService/fleets/members",
            "Microsoft.ContainerService/fleets/updateRuns",
            "Microsoft.ContainerService/fleets/updateStrategies",
            "Microsoft.ContainerService/locations",
            "Microsoft.ContainerService/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.ContainerService/locations/operationresults",
            "Microsoft.ContainerService/locations/operations",
            "Microsoft.ContainerService/locations/orchestrators",
            "Microsoft.ContainerService/locations/kubernetesVersions",
            "Microsoft.ContainerService/locations/usages",
            "Microsoft.ContainerService/locations/osOptions",
            "Microsoft.ContainerService/locations/guardrailsVersions",
            "Microsoft.ContainerService/locations/trustedAccessRoles",
            "Microsoft.ContainerService/managedClusters",
            "Microsoft.ContainerService/managedclustersnapshots",
            "Microsoft.ContainerService/operations",
            "Microsoft.ContainerService/snapshots",
            "Microsoft.DesktopVirtualization/workspaces",
            "Microsoft.DesktopVirtualization/applicationgroups",
            "Microsoft.DesktopVirtualization/applicationgroups/applications",
            "Microsoft.DesktopVirtualization/applicationgroups/desktops",
            "Microsoft.DesktopVirtualization/applicationgroups/startmenuitems",
            "Microsoft.DesktopVirtualization/hostpools",
            "Microsoft.DesktopVirtualization/hostpools/msixpackages",
            "Microsoft.DesktopVirtualization/hostpools/sessionhosts",
            "Microsoft.DesktopVirtualization/hostpools/sessionhosts/usersessions",
            "Microsoft.DesktopVirtualization/hostpools/usersessions",
            "Microsoft.DesktopVirtualization/scalingplans",
            "Microsoft.DesktopVirtualization/appattachpackages",
            "Microsoft.DesktopVirtualization/operations",
            "Microsoft.SecurityInsights/operations",
            "Microsoft.SecurityInsights/alertRules",
            "Microsoft.SecurityInsights/alertRuleTemplates",
            "Microsoft.SecurityInsights/triggeredAnalyticsRuleRuns",
            "Microsoft.SecurityInsights/cases",
            "Microsoft.SecurityInsights/bookmarks",
            "Microsoft.SecurityInsights/dataConnectors",
            "Microsoft.SecurityInsights/dataConnectorDefinitions",
            "Microsoft.SecurityInsights/dataConnectorsCheckRequirements",
            "Microsoft.SecurityInsights/enrichment",
            "Microsoft.SecurityInsights/fileImports",
            "Microsoft.SecurityInsights/entities",
            "Microsoft.SecurityInsights/incidents",
            "Microsoft.SecurityInsights/officeConsents",
            "Microsoft.SecurityInsights/settings",
            "Microsoft.SecurityInsights/aggregations",
            "Microsoft.SecurityInsights/entityQueries",
            "Microsoft.SecurityInsights/entityQueryTemplates",
            "Microsoft.SecurityInsights/threatIntelligence",
            "Microsoft.SecurityInsights/automationRules",
            "Microsoft.SecurityInsights/sourceControls",
            "Microsoft.SecurityInsights/exportConnections",
            "Microsoft.SecurityInsights/listrepositories",
            "Microsoft.SecurityInsights/watchlists",
            "Microsoft.SecurityInsights/confidentialWatchlists",
            "Microsoft.SecurityInsights/huntsessions",
            "Microsoft.SecurityInsights/dynamicSummaries",
            "Microsoft.SecurityInsights/hunts",
            "Microsoft.SecurityInsights/onboardingStates",
            "Microsoft.SecurityInsights/metadata",
            "Microsoft.SecurityInsights/contentPackages",
            "Microsoft.SecurityInsights/contentTemplates",
            "Microsoft.SecurityInsights/contentProductPackages",
            "Microsoft.SecurityInsights/contentProductTemplates",
            "Microsoft.SecurityInsights/MitreCoverageRecords",
            "Microsoft.SecurityInsights/overview",
            "Microsoft.SecurityInsights/recommendations",
            "Microsoft.SecurityInsights/billingStatistics",
            "Microsoft.SecurityInsights/workspaceManagerConfigurations",
            "Microsoft.SecurityInsights/workspaceManagerMembers",
            "Microsoft.SecurityInsights/workspaceManagerGroups",
            "Microsoft.SecurityInsights/workspaceManagerAssignments",
            "Microsoft.SecurityInsights/securityMLAnalyticsSettings",
            "Microsoft.SecurityInsights/contenttranslators",
            "Microsoft.ServiceFabric/clusters",
            "Microsoft.ServiceFabric/clusters/applications",
            "Microsoft.ServiceFabric/clusters/applicationTypes",
            "Microsoft.ServiceFabric/clusters/applicationTypes/versions",
            "Microsoft.ServiceFabric/clusters/applications/services",
            "Microsoft.ServiceFabric/locations",
            "Microsoft.ServiceFabric/locations/clusterVersions",
            "Microsoft.ServiceFabric/locations/environments",
            "Microsoft.ServiceFabric/locations/operations",
            "Microsoft.ServiceFabric/locations/operationResults",
            "Microsoft.ServiceFabric/locations/unsupportedVMSizes",
            "Microsoft.ServiceFabric/operations",
            "Microsoft.ServiceFabric/managedclusters",
            "Microsoft.ServiceFabric/managedclusters/nodetypes",
            "Microsoft.ServiceFabric/managedclusters/applicationTypes",
            "Microsoft.ServiceFabric/managedclusters/applicationTypes/versions",
            "Microsoft.ServiceFabric/managedclusters/applications",
            "Microsoft.ServiceFabric/managedclusters/applications/services",
            "Microsoft.ServiceFabric/locations/managedClusterOperations",
            "Microsoft.ServiceFabric/locations/managedClusterOperationResults",
            "Microsoft.ServiceFabric/locations/managedClusterVersions",
            "Microsoft.ServiceFabric/locations/environments/managedClusterVersions",
            "Microsoft.ServiceFabric/locations/managedUnsupportedVMSizes",
            "Microsoft.PowerBIDedicated/capacities",
            "Microsoft.PowerBIDedicated/autoScaleVCores",
            "Microsoft.PowerBIDedicated/locations",
            "Microsoft.PowerBIDedicated/locations/checkNameAvailability",
            "Microsoft.PowerBIDedicated/locations/operationresults",
            "Microsoft.PowerBIDedicated/locations/operationstatuses",
            "Microsoft.PowerBIDedicated/operations",
            "Microsoft.Logic/workflows",
            "Microsoft.Logic/locations/workflows",
            "Microsoft.Logic/locations/validateWorkflowExport",
            "Microsoft.Logic/locations/workflowExport",
            "Microsoft.Logic/locations",
            "Microsoft.Logic/operations",
            "Microsoft.Logic/integrationAccounts",
            "Microsoft.Logic/integrationServiceEnvironments",
            "Microsoft.Logic/integrationServiceEnvironments/managedApis",
            "Microsoft.Logic/locations/generateCopilotResponse",
            "Microsoft.MachineLearningServices/workspaces/batchEndpoints",
            "Microsoft.MachineLearningServices/workspaces/batchEndpoints/deployments",
            "Microsoft.MachineLearningServices/workspaces",
            "Microsoft.MachineLearningServices/registries",
            "Microsoft.MachineLearningServices/locations/registryOperationsStatus",
            "Microsoft.MachineLearningServices/workspaces/onlineEndpoints",
            "Microsoft.MachineLearningServices/workspaces/onlineEndpoints/deployments",
            "Microsoft.MachineLearningServices/workspaces/onlineEndpoints/deployments/skus",
            "Microsoft.MachineLearningServices/workspaces/computes",
            "Microsoft.MachineLearningServices/workspaces/jobs",
            "Microsoft.MachineLearningServices/workspaces/codes",
            "Microsoft.MachineLearningServices/workspaces/codes/versions",
            "Microsoft.MachineLearningServices/workspaces/components",
            "Microsoft.MachineLearningServices/workspaces/components/versions",
            "Microsoft.MachineLearningServices/workspaces/environments",
            "Microsoft.MachineLearningServices/workspaces/environments/versions",
            "Microsoft.MachineLearningServices/workspaces/data",
            "Microsoft.MachineLearningServices/workspaces/data/versions",
            "Microsoft.MachineLearningServices/workspaces/datasets",
            "Microsoft.MachineLearningServices/workspaces/services",
            "Microsoft.MachineLearningServices/workspaces/datastores",
            "Microsoft.MachineLearningServices/workspaces/eventGridFilters",
            "Microsoft.MachineLearningServices/workspaces/models",
            "Microsoft.MachineLearningServices/workspaces/models/versions",
            "Microsoft.MachineLearningServices/operations",
            "Microsoft.MachineLearningServices/locations",
            "Microsoft.MachineLearningServices/locations/computeOperationsStatus",
            "Microsoft.MachineLearningServices/locations/mfeOperationResults",
            "Microsoft.MachineLearningServices/locations/mfeOperationsStatus",
            "Microsoft.MachineLearningServices/locations/workspaceOperationsStatus",
            "Microsoft.MachineLearningServices/locations/usages",
            "Microsoft.MachineLearningServices/locations/vmsizes",
            "Microsoft.MachineLearningServices/locations/quotas",
            "Microsoft.MachineLearningServices/locations/updatequotas",
            "Microsoft.MachineLearningServices/workspaces/linkedServices",
            "Microsoft.MachineLearningServices/workspaces/labelingJobs",
            "Microsoft.MachineLearningServices/workspaces/schedules",
            "Microsoft.MachineLearningServices/workspaces/featuresets",
            "Microsoft.MachineLearningServices/workspaces/serverlessEndpoints",
            "Microsoft.MachineLearningServices/workspaces/marketplaceSubscriptions",
            "Microsoft.MachineLearningServices/workspaces/inferencePools",
            "Microsoft.MachineLearningServices/workspaces/inferencePools/groups",
            "Microsoft.MachineLearningServices/workspaces/inferencePools/endpoints",
            "Microsoft.MachineLearningServices/workspaces/featuresets/versions",
            "Microsoft.MachineLearningServices/workspaces/featurestoreEntities",
            "Microsoft.MachineLearningServices/workspaces/featurestoreEntities/versions",
            "Microsoft.MachineLearningServices/workspaces/endpoints",
            "Microsoft.MachineLearningServices/registries/codes",
            "Microsoft.MachineLearningServices/registries/codes/versions",
            "Microsoft.MachineLearningServices/registries/components",
            "Microsoft.MachineLearningServices/registries/components/versions",
            "Microsoft.MachineLearningServices/registries/data",
            "Microsoft.MachineLearningServices/registries/data/versions",
            "Microsoft.MachineLearningServices/registries/datareferences",
            "Microsoft.MachineLearningServices/registries/datareferences/versions",
            "Microsoft.MachineLearningServices/registries/environments",
            "Microsoft.MachineLearningServices/registries/environments/versions",
            "Microsoft.MachineLearningServices/registries/models",
            "Microsoft.MachineLearningServices/registries/models/versions",
            "Microsoft.MachineLearningServices/capacityReservationGroups",
            "Microsoft.ContainerInstance/containerGroups",
            "Microsoft.ContainerInstance/serviceAssociationLinks",
            "Microsoft.ContainerInstance/locations",
            "Microsoft.ContainerInstance/locations/capabilities",
            "Microsoft.ContainerInstance/locations/usages",
            "Microsoft.ContainerInstance/locations/operations",
            "Microsoft.ContainerInstance/locations/operationresults",
            "Microsoft.ContainerInstance/operations",
            "Microsoft.ContainerInstance/locations/cachedImages",
            "Microsoft.ContainerInstance/locations/validateDeleteVirtualNetworkOrSubnets",
            "Microsoft.ContainerInstance/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.ManagedIdentity/Identities",
            "Microsoft.ManagedIdentity/userAssignedIdentities",
            "Microsoft.ManagedIdentity/operations",
            "Microsoft.ManagedIdentity/userAssignedIdentities/federatedIdentityCredentials",
            "Microsoft.Cdn/profiles",
            "Microsoft.Cdn/profiles/endpoints",
            "Microsoft.Cdn/profiles/endpoints/origins",
            "Microsoft.Cdn/profiles/endpoints/origingroups",
            "Microsoft.Cdn/profiles/endpoints/customdomains",
            "Microsoft.Cdn/operationresults",
            "Microsoft.Cdn/operationresults/profileresults",
            "Microsoft.Cdn/operationresults/profileresults/endpointresults",
            "Microsoft.Cdn/operationresults/profileresults/endpointresults/originresults",
            "Microsoft.Cdn/operationresults/profileresults/endpointresults/origingroupresults",
            "Microsoft.Cdn/operationresults/profileresults/endpointresults/customdomainresults",
            "Microsoft.Cdn/checkNameAvailability",
            "Microsoft.Cdn/checkEndpointNameAvailability",
            "Microsoft.Cdn/checkResourceUsage",
            "Microsoft.Cdn/validateProbe",
            "Microsoft.Cdn/canMigrate",
            "Microsoft.Cdn/migrate",
            "Microsoft.Cdn/operations",
            "Microsoft.Cdn/edgenodes",
            "Microsoft.Cdn/CdnWebApplicationFirewallPolicies",
            "Microsoft.Cdn/operationresults/cdnwebapplicationfirewallpolicyresults",
            "Microsoft.Cdn/CdnWebApplicationFirewallManagedRuleSets",
            "Microsoft.Cdn/profiles/afdendpoints",
            "Microsoft.Cdn/profiles/afdendpoints/routes",
            "Microsoft.Cdn/profiles/customdomains",
            "Microsoft.Cdn/profiles/origingroups",
            "Microsoft.Cdn/profiles/origingroups/origins",
            "Microsoft.Cdn/profiles/rulesets",
            "Microsoft.Cdn/profiles/rulesets/rules",
            "Microsoft.Cdn/profiles/secrets",
            "Microsoft.Cdn/validateSecret",
            "Microsoft.Cdn/profiles/keygroups",
            "Microsoft.Cdn/profiles/securitypolicies",
            "Microsoft.Cdn/operationresults/profileresults/afdendpointresults",
            "Microsoft.Cdn/operationresults/profileresults/afdendpointresults/routeresults",
            "Microsoft.Cdn/operationresults/profileresults/customdomainresults",
            "Microsoft.Cdn/operationresults/profileresults/origingroupresults",
            "Microsoft.Cdn/operationresults/profileresults/origingroupresults/originresults",
            "Microsoft.Cdn/operationresults/profileresults/rulesetresults",
            "Microsoft.Cdn/operationresults/profileresults/rulesetresults/ruleresults",
            "Microsoft.Cdn/operationresults/profileresults/secretresults",
            "Microsoft.Cdn/operationresults/profileresults/securitypolicyresults",
            "Microsoft.Cdn/profiles/policies",
            "Microsoft.Cdn/profiles/networkpolicies",
            "Microsoft.Cdn/operationresults/profileresults/policyresults",
            "Microsoft.BotService/botServices",
            "Microsoft.BotService/botServices/channels",
            "Microsoft.BotService/botServices/connections",
            "Microsoft.BotService/listAuthServiceProviders",
            "Microsoft.BotService/listQnAMakerEndpointKeys",
            "Microsoft.BotService/hostSettings",
            "Microsoft.BotService/checkNameAvailability",
            "Microsoft.BotService/locations",
            "Microsoft.BotService/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.BotService/operations",
            "Microsoft.BotService/botServices/privateEndpointConnectionProxies",
            "Microsoft.BotService/botServices/privateEndpointConnections",
            "Microsoft.BotService/botServices/privateLinkResources",
            "Microsoft.BotService/operationResults",
            "Microsoft.Devices/checkNameAvailability",
            "Microsoft.Devices/checkProvisioningServiceNameAvailability",
            "Microsoft.Devices/usages",
            "Microsoft.Devices/operations",
            "Microsoft.Devices/operationResults",
            "Microsoft.Devices/provisioningServiceOperationResults",
            "Microsoft.Devices/locations/provisioningServiceOperationResults",
            "Microsoft.Devices/locations",
            "Microsoft.Devices/locations/operationResults",
            "Microsoft.Devices/IotHubs",
            "Microsoft.Devices/IotHubs/eventGridFilters",
            "Microsoft.Devices/IotHubs/failover",
            "Microsoft.Devices/ProvisioningServices",
            "Microsoft.Devices/IotHubs/securitySettings",
            "Microsoft.Databricks/workspaces",
            "Microsoft.Databricks/accessConnectors",
            "Microsoft.Databricks/workspaces/virtualNetworkPeerings",
            "Microsoft.Databricks/workspaces/dbWorkspaces",
            "Microsoft.Databricks/operations",
            "Microsoft.Databricks/locations",
            "Microsoft.Databricks/locations/operationstatuses",
            "Microsoft.Databricks/locations/getNetworkPolicies",
            "Microsoft.EventGrid/locations",
            "Microsoft.EventGrid/locations/eventSubscriptions",
            "Microsoft.EventGrid/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.EventGrid/eventSubscriptions",
            "Microsoft.EventGrid/topics",
            "Microsoft.EventGrid/domains",
            "Microsoft.EventGrid/domains/topics",
            "Microsoft.EventGrid/topicTypes",
            "Microsoft.EventGrid/operations",
            "Microsoft.EventGrid/locations/operationsStatus",
            "Microsoft.EventGrid/locations/operationResults",
            "Microsoft.EventGrid/locations/topicTypes",
            "Microsoft.EventGrid/extensionTopics",
            "Microsoft.EventGrid/operationResults",
            "Microsoft.EventGrid/operationsStatus",
            "Microsoft.EventGrid/systemTopics",
            "Microsoft.EventGrid/systemTopics/eventSubscriptions",
            "Microsoft.EventGrid/partnerRegistrations",
            "Microsoft.EventGrid/partnerConfigurations",
            "Microsoft.EventGrid/verifiedPartners",
            "Microsoft.EventGrid/namespaces",
            "Microsoft.EventGrid/partnerNamespaces",
            "Microsoft.EventGrid/partnerTopics",
            "Microsoft.EventGrid/partnerTopics/eventSubscriptions",
            "Microsoft.EventGrid/partnerNamespaces/eventChannels",
            "Microsoft.EventGrid/partnerNamespaces/channels",
            "Microsoft.EventGrid/partnerDestinations",
            "Microsoft.DBforPostgreSQL/operations",
            "Microsoft.DBforPostgreSQL/servers",
            "Microsoft.DBforPostgreSQL/serverGroupsv2",
            "Microsoft.DBforPostgreSQL/flexibleServers",
            "Microsoft.DBforPostgreSQL/locations/capabilities",
            "Microsoft.DBforPostgreSQL/locations/checkNameAvailability",
            "Microsoft.DBforPostgreSQL/servers/recoverableServers",
            "Microsoft.DBforPostgreSQL/servers/virtualNetworkRules",
            "Microsoft.DBforPostgreSQL/checkNameAvailability",
            "Microsoft.DBforPostgreSQL/availableEngineVersions",
            "Microsoft.DBforPostgreSQL/getPrivateDnsZoneSuffix",
            "Microsoft.DBforPostgreSQL/locations",
            "Microsoft.DBforPostgreSQL/locations/operationResults",
            "Microsoft.DBforPostgreSQL/locations/azureAsyncOperation",
            "Microsoft.DBforPostgreSQL/locations/administratorOperationResults",
            "Microsoft.DBforPostgreSQL/locations/administratorAzureAsyncOperation",
            "Microsoft.DBforPostgreSQL/locations/checkVirtualNetworkSubnetUsage",
            "Microsoft.DBforPostgreSQL/locations/privateEndpointConnectionProxyOperationResults",
            "Microsoft.DBforPostgreSQL/locations/privateEndpointConnectionProxyAzureAsyncOperation",
            "Microsoft.DBforPostgreSQL/locations/privateEndpointConnectionOperationResults",
            "Microsoft.DBforPostgreSQL/locations/privateEndpointConnectionAzureAsyncOperation",
            "Microsoft.DBforPostgreSQL/locations/performanceTiers",
            "Microsoft.DBforPostgreSQL/locations/securityAlertPoliciesAzureAsyncOperation",
            "Microsoft.DBforPostgreSQL/locations/securityAlertPoliciesOperationResults",
            "Microsoft.DBforPostgreSQL/locations/recommendedActionSessionsAzureAsyncOperation",
            "Microsoft.DBforPostgreSQL/locations/recommendedActionSessionsOperationResults",
            "Microsoft.DBforPostgreSQL/servers/topQueryStatistics",
            "Microsoft.DBforPostgreSQL/servers/queryTexts",
            "Microsoft.DBforPostgreSQL/servers/waitStatistics",
            "Microsoft.DBforPostgreSQL/servers/resetQueryPerformanceInsightData",
            "Microsoft.DBforPostgreSQL/servers/advisors",
            "Microsoft.DBforPostgreSQL/servers/privateLinkResources",
            "Microsoft.DBforPostgreSQL/servers/privateEndpointConnections",
            "Microsoft.DBforPostgreSQL/servers/privateEndpointConnectionProxies",
            "Microsoft.DBforPostgreSQL/servers/keys",
            "Microsoft.DBforPostgreSQL/locations/serverKeyAzureAsyncOperation",
            "Microsoft.DBforPostgreSQL/locations/serverKeyOperationResults",
            "Microsoft.DBforPostgreSQL/locations/getCachedServerName",
            "Microsoft.TimeSeriesInsights/environments",
            "Microsoft.TimeSeriesInsights/environments/eventsources",
            "Microsoft.TimeSeriesInsights/environments/referenceDataSets",
            "Microsoft.TimeSeriesInsights/environments/accessPolicies",
            "Microsoft.TimeSeriesInsights/environments/privateLinkResources",
            "Microsoft.TimeSeriesInsights/environments/privateEndpointConnectionProxies",
            "Microsoft.TimeSeriesInsights/environments/privateEndpointConnections",
            "Microsoft.TimeSeriesInsights/operations",
            "Microsoft.DBforMariaDB/operations",
            "Microsoft.DBforMariaDB/servers",
            "Microsoft.DBforMariaDB/servers/recoverableServers",
            "Microsoft.DBforMariaDB/servers/virtualNetworkRules",
            "Microsoft.DBforMariaDB/checkNameAvailability",
            "Microsoft.DBforMariaDB/locations",
            "Microsoft.DBforMariaDB/locations/operationResults",
            "Microsoft.DBforMariaDB/locations/azureAsyncOperation",
            "Microsoft.DBforMariaDB/locations/performanceTiers",
            "Microsoft.DBforMariaDB/locations/securityAlertPoliciesAzureAsyncOperation",
            "Microsoft.DBforMariaDB/locations/privateEndpointConnectionProxyOperationResults",
            "Microsoft.DBforMariaDB/locations/privateEndpointConnectionProxyAzureAsyncOperation",
            "Microsoft.DBforMariaDB/locations/privateEndpointConnectionOperationResults",
            "Microsoft.DBforMariaDB/locations/privateEndpointConnectionAzureAsyncOperation",
            "Microsoft.DBforMariaDB/locations/securityAlertPoliciesOperationResults",
            "Microsoft.DBforMariaDB/locations/recommendedActionSessionsAzureAsyncOperation",
            "Microsoft.DBforMariaDB/locations/recommendedActionSessionsOperationResults",
            "Microsoft.DBforMariaDB/servers/topQueryStatistics",
            "Microsoft.DBforMariaDB/servers/queryTexts",
            "Microsoft.DBforMariaDB/servers/waitStatistics",
            "Microsoft.DBforMariaDB/servers/resetQueryPerformanceInsightData",
            "Microsoft.DBforMariaDB/servers/advisors",
            "Microsoft.DBforMariaDB/servers/privateLinkResources",
            "Microsoft.DBforMariaDB/servers/privateEndpointConnections",
            "Microsoft.DBforMariaDB/servers/privateEndpointConnectionProxies",
            "Microsoft.DBforMariaDB/servers/keys",
            "Microsoft.DBforMariaDB/locations/serverKeyAzureAsyncOperation",
            "Microsoft.DBforMariaDB/locations/serverKeyOperationResults",
            "Microsoft.DBforMariaDB/servers/start",
            "Microsoft.DBforMariaDB/servers/stop",
            "Microsoft.Cache/Redis",
            "Microsoft.Cache/Redis/privateEndpointConnectionProxies",
            "Microsoft.Cache/Redis/privateEndpointConnectionProxies/validate",
            "Microsoft.Cache/Redis/privateEndpointConnections",
            "Microsoft.Cache/Redis/privateLinkResources",
            "Microsoft.Cache/locations/asyncOperations",
            "Microsoft.Cache/locations",
            "Microsoft.Cache/locations/operationResults",
            "Microsoft.Cache/locations/operationsStatus",
            "Microsoft.Cache/checkNameAvailability",
            "Microsoft.Cache/operations",
            "Microsoft.Cache/redisEnterprise",
            "Microsoft.Cache/RedisEnterprise/privateEndpointConnectionProxies",
            "Microsoft.Cache/RedisEnterprise/privateEndpointConnectionProxies/validate",
            "Microsoft.Cache/RedisEnterprise/privateEndpointConnectionProxies/operationresults",
            "Microsoft.Cache/RedisEnterprise/privateEndpointConnections",
            "Microsoft.Cache/RedisEnterprise/privateEndpointConnections/operationresults",
            "Microsoft.Cache/RedisEnterprise/privateLinkResources",
            "Microsoft.Cache/redisEnterprise/databases",
            "Microsoft.Cache/locations/checkNameAvailability",
            "Microsoft.Cache/Redis/EventGridFilters",
            "Microsoft.RecoveryServices/vaults",
            "Microsoft.RecoveryServices/operations",
            "Microsoft.RecoveryServices/locations",
            "Microsoft.RecoveryServices/locations/backupStatus",
            "Microsoft.RecoveryServices/locations/checkNameAvailability",
            "Microsoft.RecoveryServices/locations/allocatedStamp",
            "Microsoft.RecoveryServices/locations/allocateStamp",
            "Microsoft.RecoveryServices/locations/backupValidateFeatures",
            "Microsoft.RecoveryServices/locations/backupPreValidateProtection",
            "Microsoft.RecoveryServices/locations/backupCrrJobs",
            "Microsoft.RecoveryServices/locations/backupCrrJob",
            "Microsoft.RecoveryServices/locations/backupAadProperties",
            "Microsoft.RecoveryServices/locations/backupCrossRegionRestore",
            "Microsoft.RecoveryServices/locations/backupCrrOperationResults",
            "Microsoft.RecoveryServices/locations/backupCrrOperationsStatus",
            "Microsoft.RecoveryServices/backupProtectedItems",
            "Microsoft.RecoveryServices/replicationEligibilityResults",
            "Microsoft.RecoveryServices/locations/capabilities",
            "Microsoft.ServiceBus/namespaces",
            "Microsoft.ServiceBus/namespaces/authorizationrules",
            "Microsoft.ServiceBus/namespaces/networkrulesets",
            "Microsoft.ServiceBus/namespaces/privateEndpointConnections",
            "Microsoft.ServiceBus/namespaces/privateEndpointConnectionProxies",
            "Microsoft.ServiceBus/namespaces/queues",
            "Microsoft.ServiceBus/namespaces/queues/authorizationrules",
            "Microsoft.ServiceBus/namespaces/topics",
            "Microsoft.ServiceBus/namespaces/topics/authorizationrules",
            "Microsoft.ServiceBus/namespaces/topics/subscriptions",
            "Microsoft.ServiceBus/namespaces/topics/subscriptions/rules",
            "Microsoft.ServiceBus/checkNamespaceAvailability",
            "Microsoft.ServiceBus/checkNameAvailability",
            "Microsoft.ServiceBus/sku",
            "Microsoft.ServiceBus/premiumMessagingRegions",
            "Microsoft.ServiceBus/operations",
            "Microsoft.ServiceBus/namespaces/eventgridfilters",
            "Microsoft.ServiceBus/namespaces/disasterrecoveryconfigs",
            "Microsoft.ServiceBus/namespaces/migrationConfigurations",
            "Microsoft.ServiceBus/namespaces/disasterrecoveryconfigs/checkNameAvailability",
            "Microsoft.ServiceBus/locations",
            "Microsoft.ServiceBus/locations/operationStatus",
            "Microsoft.ServiceBus/locations/namespaceOperationResults",
            "Microsoft.ServiceBus/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.ServiceFabricMesh/applications",
            "Microsoft.ServiceFabricMesh/networks",
            "Microsoft.ServiceFabricMesh/volumes",
            "Microsoft.ServiceFabricMesh/secrets",
            "Microsoft.ServiceFabricMesh/gateways",
            "Microsoft.ServiceFabricMesh/locations",
            "Microsoft.ServiceFabricMesh/locations/applicationOperations",
            "Microsoft.ServiceFabricMesh/locations/networkOperations",
            "Microsoft.ServiceFabricMesh/locations/volumeOperations",
            "Microsoft.ServiceFabricMesh/locations/gatewayOperations",
            "Microsoft.ServiceFabricMesh/locations/secretOperations",
            "Microsoft.ServiceFabricMesh/operations",
            "Microsoft.NotificationHubs/namespaces",
            "Microsoft.NotificationHubs/namespaces/notificationHubs",
            "Microsoft.NotificationHubs/checkNamespaceAvailability",
            "Microsoft.NotificationHubs/checkNameAvailability",
            "Microsoft.NotificationHubs/operations",
            "Microsoft.ContainerRegistry/registries",
            "Microsoft.ContainerRegistry/registries/cacheRules",
            "Microsoft.ContainerRegistry/registries/credentialSets",
            "Microsoft.ContainerRegistry/registries/connectedRegistries",
            "Microsoft.ContainerRegistry/registries/connectedRegistries/deactivate",
            "Microsoft.ContainerRegistry/registries/scopeMaps",
            "Microsoft.ContainerRegistry/registries/tokens",
            "Microsoft.ContainerRegistry/registries/generateCredentials",
            "Microsoft.ContainerRegistry/registries/privateEndpointConnections",
            "Microsoft.ContainerRegistry/registries/privateEndpointConnectionProxies",
            "Microsoft.ContainerRegistry/registries/privateEndpointConnectionProxies/validate",
            "Microsoft.ContainerRegistry/registries/privateLinkResources",
            "Microsoft.ContainerRegistry/registries/importImage",
            "Microsoft.ContainerRegistry/registries/exportPipelines",
            "Microsoft.ContainerRegistry/registries/importPipelines",
            "Microsoft.ContainerRegistry/registries/pipelineRuns",
            "Microsoft.ContainerRegistry/registries/listBuildSourceUploadUrl",
            "Microsoft.ContainerRegistry/registries/scheduleRun",
            "Microsoft.ContainerRegistry/registries/runs",
            "Microsoft.ContainerRegistry/registries/taskRuns",
            "Microsoft.ContainerRegistry/registries/taskRuns/listDetails",
            "Microsoft.ContainerRegistry/registries/agentPools",
            "Microsoft.ContainerRegistry/registries/agentPoolsOperationResults",
            "Microsoft.ContainerRegistry/registries/agentPools/listQueueStatus",
            "Microsoft.ContainerRegistry/registries/runs/listLogSasUrl",
            "Microsoft.ContainerRegistry/registries/runs/cancel",
            "Microsoft.ContainerRegistry/registries/tasks",
            "Microsoft.ContainerRegistry/registries/tasks/listDetails",
            "Microsoft.ContainerRegistry/registries/replications",
            "Microsoft.ContainerRegistry/registries/webhooks",
            "Microsoft.ContainerRegistry/registries/webhooks/ping",
            "Microsoft.ContainerRegistry/registries/webhooks/getCallbackConfig",
            "Microsoft.ContainerRegistry/registries/webhooks/listEvents",
            "Microsoft.ContainerRegistry/locations/operationResults",
            "Microsoft.ContainerRegistry/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.ContainerRegistry/registries/listCredentials",
            "Microsoft.ContainerRegistry/registries/regenerateCredential",
            "Microsoft.ContainerRegistry/registries/listUsages",
            "Microsoft.ContainerRegistry/registries/listPolicies",
            "Microsoft.ContainerRegistry/registries/updatePolicies",
            "Microsoft.ContainerRegistry/registries/eventGridFilters",
            "Microsoft.ContainerRegistry/checkNameAvailability",
            "Microsoft.ContainerRegistry/operations",
            "Microsoft.ContainerRegistry/locations",
            "Microsoft.StreamAnalytics/streamingjobs",
            "Microsoft.StreamAnalytics/clusters",
            "Microsoft.StreamAnalytics/clusters/privateEndpoints",
            "Microsoft.StreamAnalytics/locations",
            "Microsoft.StreamAnalytics/locations/quotas",
            "Microsoft.StreamAnalytics/locations/testQuery",
            "Microsoft.StreamAnalytics/locations/compileQuery",
            "Microsoft.StreamAnalytics/locations/sampleInput",
            "Microsoft.StreamAnalytics/locations/testInput",
            "Microsoft.StreamAnalytics/locations/testOutput",
            "Microsoft.StreamAnalytics/locations/operationResults",
            "Microsoft.StreamAnalytics/operations",
            "Microsoft.DataLakeAnalytics/accounts",
            "Microsoft.DataLakeAnalytics/accounts/dataLakeStoreAccounts",
            "Microsoft.DataLakeAnalytics/accounts/storageAccounts",
            "Microsoft.DataLakeAnalytics/accounts/storageAccounts/containers",
            "Microsoft.DataLakeAnalytics/accounts/storageAccounts/containers/listSasTokens",
            "Microsoft.DataLakeAnalytics/locations",
            "Microsoft.DataLakeAnalytics/locations/operationresults",
            "Microsoft.DataLakeAnalytics/locations/checkNameAvailability",
            "Microsoft.DataLakeAnalytics/locations/capability",
            "Microsoft.DataLakeAnalytics/locations/usages",
            "Microsoft.DataLakeAnalytics/operations",
            "Microsoft.Relay/namespaces",
            "Microsoft.Relay/namespaces/authorizationrules",
            "Microsoft.Relay/namespaces/privateEndpointConnections",
            "Microsoft.Relay/namespaces/privateEndpointConnectionProxies",
            "Microsoft.Relay/namespaces/hybridconnections",
            "Microsoft.Relay/namespaces/hybridconnections/authorizationrules",
            "Microsoft.Relay/namespaces/wcfrelays",
            "Microsoft.Relay/namespaces/wcfrelays/authorizationrules",
            "Microsoft.Relay/checkNameAvailability",
            "Microsoft.Relay/operations",
            "Microsoft.Relay/locations",
            "Microsoft.Relay/locations/namespaceOperationResults",
            "Microsoft.DevTestLab/labs/environments",
            "Microsoft.DevTestLab/labs",
            "Microsoft.DevTestLab/schedules",
            "Microsoft.DevTestLab/labs/virtualMachines",
            "Microsoft.DevTestLab/labs/serviceRunners",
            "Microsoft.DevTestLab/operations",
            "Microsoft.DevTestLab/locations",
            "Microsoft.DevTestLab/locations/operations",
            "Microsoft.EventHub/namespaces",
            "Microsoft.EventHub/clusters",
            "Microsoft.EventHub/namespaces/authorizationrules",
            "Microsoft.EventHub/namespaces/networkrulesets",
            "Microsoft.EventHub/namespaces/privateEndpointConnections",
            "Microsoft.EventHub/namespaces/privateEndpointConnectionProxies",
            "Microsoft.EventHub/namespaces/networkSecurityPerimeterConfigurations",
            "Microsoft.EventHub/namespaces/networkSecurityPerimeterAssociationProxies",
            "Microsoft.EventHub/namespaces/eventhubs",
            "Microsoft.EventHub/namespaces/eventhubs/authorizationrules",
            "Microsoft.EventHub/namespaces/eventhubs/consumergroups",
            "Microsoft.EventHub/namespaces/applicationGroups",
            "Microsoft.EventHub/checkNamespaceAvailability",
            "Microsoft.EventHub/checkNameAvailability",
            "Microsoft.EventHub/sku",
            "Microsoft.EventHub/operations",
            "Microsoft.EventHub/namespaces/disasterrecoveryconfigs",
            "Microsoft.EventHub/namespaces/disasterrecoveryconfigs/checkNameAvailability",
            "Microsoft.EventHub/locations",
            "Microsoft.EventHub/locations/operationStatus",
            "Microsoft.EventHub/locations/clusterOperationResults",
            "Microsoft.EventHub/locations/namespaceOperationResults",
            "Microsoft.EventHub/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.EventHub/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.EventHub/availableClusterRegions",
            "Microsoft.AppPlatform/Spring",
            "Microsoft.AppPlatform/Spring/operationResults",
            "Microsoft.AppPlatform/Spring/operationStatuses",
            "Microsoft.AppPlatform/Spring/apps",
            "Microsoft.AppPlatform/Spring/apps/operationResults",
            "Microsoft.AppPlatform/Spring/apps/operationStatuses",
            "Microsoft.AppPlatform/Spring/apps/deployments",
            "Microsoft.AppPlatform/Spring/apps/deployments/operationResults",
            "Microsoft.AppPlatform/Spring/apps/deployments/operationStatuses",
            "Microsoft.AppPlatform/Spring/configServers",
            "Microsoft.AppPlatform/Spring/configServers/operationResults",
            "Microsoft.AppPlatform/Spring/configServers/operationStatuses",
            "Microsoft.AppPlatform/Spring/eurekaServers",
            "Microsoft.AppPlatform/Spring/eurekaServers/operationResults",
            "Microsoft.AppPlatform/Spring/eurekaServers/operationStatuses",
            "Microsoft.AppPlatform/Spring/apps/domains",
            "Microsoft.AppPlatform/Spring/apps/domains/operationResults",
            "Microsoft.AppPlatform/Spring/apps/domains/operationStatuses",
            "Microsoft.AppPlatform/locations/checkNameAvailability",
            "Microsoft.AppPlatform/operations",
            "Microsoft.AppPlatform/locations",
            "Microsoft.AppPlatform/runtimeVersions",
            "Microsoft.AppPlatform/locations/operationResults",
            "Microsoft.AppPlatform/locations/operationStatus",
            "Microsoft.CustomProviders/resourceProviders",
            "Microsoft.CustomProviders/resourceProviders/operationResults",
            "Microsoft.CustomProviders/resourceProviders/operationStatuses",
            "Microsoft.CustomProviders/associations",
            "Microsoft.CustomProviders/operations",
            "Microsoft.CustomProviders/locations",
            "Microsoft.CustomProviders/locations/operationStatuses",
            "Microsoft.CustomProviders/locations/operationResults",
            "Microsoft.DocumentDB/databaseAccounts",
            "Microsoft.DocumentDB/databaseAccountNames",
            "Microsoft.DocumentDB/operations",
            "Microsoft.DocumentDB/operationResults",
            "Microsoft.DocumentDB/operationsStatus",
            "Microsoft.DocumentDB/locations/operationsStatus",
            "Microsoft.DocumentDB/locations/operationResults",
            "Microsoft.DocumentDB/locations",
            "Microsoft.DocumentDB/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.DocumentDB/locations/restorableDatabaseAccounts",
            "Microsoft.DocumentDB/restorableDatabaseAccounts",
            "Microsoft.DocumentDB/cassandraClusters",
            "Microsoft.DocumentDB/databaseAccounts/encryptionScopes",
            "Microsoft.DocumentDB/mongoClusters",
            "Microsoft.DocumentDB/locations/mongoClusterOperationResults",
            "Microsoft.DocumentDB/locations/mongoClusterAzureAsyncOperation",
            "Microsoft.DocumentDB/locations/checkMongoClusterNameAvailability",
            "Microsoft.DocumentDB/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.DocumentDB/throughputPools",
            "Microsoft.DocumentDB/throughputPools/throughputPoolAccounts",
            "Microsoft.Maintenance/maintenanceConfigurations",
            "Microsoft.Maintenance/updates",
            "Microsoft.Maintenance/configurationAssignments",
            "Microsoft.Maintenance/applyUpdates",
            "Microsoft.Maintenance/publicMaintenanceConfigurations",
            "Microsoft.Maintenance/operations",
            "Microsoft.Sql/operations",
            "Microsoft.Sql/locations",
            "Microsoft.Sql/locations/capabilities",
            "Microsoft.Sql/locations/databaseAzureAsyncOperation",
            "Microsoft.Sql/locations/databaseOperationResults",
            "Microsoft.Sql/locations/databaseEncryptionProtectorRevalidateAzureAsyncOperation",
            "Microsoft.Sql/locations/databaseEncryptionProtectorRevalidateOperationResults",
            "Microsoft.Sql/locations/databaseEncryptionProtectorRevertAzureAsyncOperation",
            "Microsoft.Sql/locations/databaseEncryptionProtectorRevertOperationResults",
            "Microsoft.Sql/locations/serverKeyAzureAsyncOperation",
            "Microsoft.Sql/locations/serverKeyOperationResults",
            "Microsoft.Sql/servers/keys",
            "Microsoft.Sql/servers/encryptionProtector",
            "Microsoft.Sql/locations/encryptionProtectorOperationResults",
            "Microsoft.Sql/locations/encryptionProtectorAzureAsyncOperation",
            "Microsoft.Sql/locations/externalPolicyBasedAuthorizationsAzureAsycOperation",
            "Microsoft.Sql/locations/externalPolicyBasedAuthorizationsOperationResults",
            "Microsoft.Sql/locations/refreshExternalGovernanceStatusOperationResults",
            "Microsoft.Sql/locations/refreshExternalGovernanceStatusAzureAsyncOperation",
            "Microsoft.Sql/locations/refreshExternalGovernanceStatusMIOperationResults",
            "Microsoft.Sql/locations/refreshExternalGovernanceStatusMIAzureAsyncOperation",
            "Microsoft.Sql/locations/managedInstanceKeyAzureAsyncOperation",
            "Microsoft.Sql/locations/managedInstanceKeyOperationResults",
            "Microsoft.Sql/locations/managedInstanceEncryptionProtectorOperationResults",
            "Microsoft.Sql/locations/managedInstanceEncryptionProtectorAzureAsyncOperation",
            "Microsoft.Sql/locations/transparentDataEncryptionAzureAsyncOperation",
            "Microsoft.Sql/locations/transparentDataEncryptionOperationResults",
            "Microsoft.Sql/locations/managedtransparentDataEncryptionAzureAsyncOperation",
            "Microsoft.Sql/locations/managedtransparentDataEncryptionOperationResults",
            "Microsoft.Sql/servers/tdeCertificates",
            "Microsoft.Sql/locations/tdeCertAzureAsyncOperation",
            "Microsoft.Sql/locations/tdeCertOperationResults",
            "Microsoft.Sql/locations/serverAzureAsyncOperation",
            "Microsoft.Sql/locations/serverOperationResults",
            "Microsoft.Sql/locations/usages",
            "Microsoft.Sql/checkNameAvailability",
            "Microsoft.Sql/servers",
            "Microsoft.Sql/servers/databases",
            "Microsoft.Sql/servers/serviceObjectives",
            "Microsoft.Sql/servers/communicationLinks",
            "Microsoft.Sql/servers/administrators",
            "Microsoft.Sql/servers/administratorOperationResults",
            "Microsoft.Sql/locations/serverAdministratorAzureAsyncOperation",
            "Microsoft.Sql/locations/serverAdministratorOperationResults",
            "Microsoft.Sql/servers/restorableDroppedDatabases",
            "Microsoft.Sql/servers/recoverableDatabases",
            "Microsoft.Sql/servers/databases/geoBackupPolicies",
            "Microsoft.Sql/servers/import",
            "Microsoft.Sql/servers/importExportOperationResults",
            "Microsoft.Sql/servers/operationResults",
            "Microsoft.Sql/servers/databases/backupLongTermRetentionPolicies",
            "Microsoft.Sql/servers/databases/backupShortTermRetentionPolicies",
            "Microsoft.Sql/servers/databaseSecurityPolicies",
            "Microsoft.Sql/servers/automaticTuning",
            "Microsoft.Sql/servers/databases/automaticTuning",
            "Microsoft.Sql/servers/databases/transparentDataEncryption",
            "Microsoft.Sql/servers/databases/ledgerDigestUploads",
            "Microsoft.Sql/locations/ledgerDigestUploadsAzureAsyncOperation",
            "Microsoft.Sql/locations/ledgerDigestUploadsOperationResults",
            "Microsoft.Sql/servers/recommendedElasticPools",
            "Microsoft.Sql/servers/databases/dataMaskingPolicies",
            "Microsoft.Sql/servers/databases/dataMaskingPolicies/rules",
            "Microsoft.Sql/servers/databases/securityAlertPolicies",
            "Microsoft.Sql/servers/securityAlertPolicies",
            "Microsoft.Sql/servers/databases/advancedThreatProtectionSettings",
            "Microsoft.Sql/servers/advancedThreatProtectionSettings",
            "Microsoft.Sql/managedInstances/databases/advancedThreatProtectionSettings",
            "Microsoft.Sql/managedInstances/advancedThreatProtectionSettings",
            "Microsoft.Sql/servers/databases/auditingSettings",
            "Microsoft.Sql/servers/auditingSettings",
            "Microsoft.Sql/servers/extendedAuditingSettings",
            "Microsoft.Sql/servers/devOpsAuditingSettings",
            "Microsoft.Sql/locations/auditingSettingsAzureAsyncOperation",
            "Microsoft.Sql/locations/auditingSettingsOperationResults",
            "Microsoft.Sql/locations/extendedAuditingSettingsAzureAsyncOperation",
            "Microsoft.Sql/locations/extendedAuditingSettingsOperationResults",
            "Microsoft.Sql/locations/devOpsAuditingSettingsOperationResults",
            "Microsoft.Sql/locations/devOpsAuditingSettingsAzureAsyncOperation",
            "Microsoft.Sql/locations/elasticPoolAzureAsyncOperation",
            "Microsoft.Sql/locations/elasticPoolOperationResults",
            "Microsoft.Sql/servers/elasticpools",
            "Microsoft.Sql/servers/jobAccounts",
            "Microsoft.Sql/servers/jobAgents",
            "Microsoft.Sql/locations/jobAgentOperationResults",
            "Microsoft.Sql/locations/jobAgentAzureAsyncOperation",
            "Microsoft.Sql/servers/jobAgents/privateEndpoints",
            "Microsoft.Sql/locations/jobAgentPrivateEndpointOperationResults",
            "Microsoft.Sql/locations/jobAgentPrivateEndpointAzureAsyncOperation",
            "Microsoft.Sql/servers/jobAgents/jobs",
            "Microsoft.Sql/servers/jobAgents/jobs/steps",
            "Microsoft.Sql/servers/jobAgents/jobs/executions",
            "Microsoft.Sql/servers/disasterRecoveryConfiguration",
            "Microsoft.Sql/servers/dnsAliases",
            "Microsoft.Sql/locations/dnsAliasAsyncOperation",
            "Microsoft.Sql/locations/dnsAliasOperationResults",
            "Microsoft.Sql/servers/failoverGroups",
            "Microsoft.Sql/locations/failoverGroupAzureAsyncOperation",
            "Microsoft.Sql/locations/failoverGroupOperationResults",
            "Microsoft.Sql/locations/firewallRulesOperationResults",
            "Microsoft.Sql/locations/firewallRulesAzureAsyncOperation",
            "Microsoft.Sql/locations/ipv6FirewallRulesOperationResults",
            "Microsoft.Sql/locations/ipv6FirewallRulesAzureAsyncOperation",
            "Microsoft.Sql/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.Sql/servers/virtualNetworkRules",
            "Microsoft.Sql/locations/virtualNetworkRulesOperationResults",
            "Microsoft.Sql/locations/virtualNetworkRulesAzureAsyncOperation",
            "Microsoft.Sql/locations/deleteVirtualNetworkOrSubnetsOperationResults",
            "Microsoft.Sql/locations/deleteVirtualNetworkOrSubnetsAzureAsyncOperation",
            "Microsoft.Sql/locations/databaseRestoreAzureAsyncOperation",
            "Microsoft.Sql/servers/usages",
            "Microsoft.Sql/servers/databases/metricDefinitions",
            "Microsoft.Sql/servers/databases/metrics",
            "Microsoft.Sql/servers/aggregatedDatabaseMetrics",
            "Microsoft.Sql/servers/elasticpools/metrics",
            "Microsoft.Sql/servers/elasticpools/metricdefinitions",
            "Microsoft.Sql/servers/databases/topQueries",
            "Microsoft.Sql/servers/databases/topQueries/queryText",
            "Microsoft.Sql/servers/advisors",
            "Microsoft.Sql/servers/elasticPools/advisors",
            "Microsoft.Sql/servers/databases/advisors",
            "Microsoft.Sql/servers/databases/extensions",
            "Microsoft.Sql/servers/elasticPoolEstimates",
            "Microsoft.Sql/servers/databases/auditRecords",
            "Microsoft.Sql/servers/databases/VulnerabilityAssessmentScans",
            "Microsoft.Sql/servers/databases/workloadGroups",
            "Microsoft.Sql/servers/databases/vulnerabilityAssessments",
            "Microsoft.Sql/servers/vulnerabilityAssessments",
            "Microsoft.Sql/managedInstances/databases/vulnerabilityAssessments",
            "Microsoft.Sql/managedInstances/vulnerabilityAssessments",
            "Microsoft.Sql/servers/databases/VulnerabilityAssessmentSettings",
            "Microsoft.Sql/servers/databases/VulnerabilityAssessment",
            "Microsoft.Sql/locations/vulnerabilityAssessmentScanAzureAsyncOperation",
            "Microsoft.Sql/locations/vulnerabilityAssessmentScanOperationResults",
            "Microsoft.Sql/servers/databases/sqlvulnerabilityassessments",
            "Microsoft.Sql/servers/sqlvulnerabilityassessments",
            "Microsoft.Sql/locations/sqlVulnerabilityAssessmentAzureAsyncOperation",
            "Microsoft.Sql/locations/sqlVulnerabilityAssessmentOperationResults",
            "Microsoft.Sql/servers/databases/recommendedSensitivityLabels",
            "Microsoft.Sql/servers/databases/syncGroups",
            "Microsoft.Sql/servers/databases/syncGroups/syncMembers",
            "Microsoft.Sql/servers/syncAgents",
            "Microsoft.Sql/instancePools",
            "Microsoft.Sql/locations/importExportOperationResults",
            "Microsoft.Sql/locations/importExportAzureAsyncOperation",
            "Microsoft.Sql/locations/instancePoolOperationResults",
            "Microsoft.Sql/locations/instancePoolAzureAsyncOperation",
            "Microsoft.Sql/managedInstances",
            "Microsoft.Sql/managedInstances/administrators",
            "Microsoft.Sql/managedInstances/databases",
            "Microsoft.Sql/managedInstances/recoverableDatabases",
            "Microsoft.Sql/managedInstances/metrics",
            "Microsoft.Sql/managedInstances/metricDefinitions",
            "Microsoft.Sql/managedInstances/databases/backupLongTermRetentionPolicies",
            "Microsoft.Sql/managedInstances/sqlAgent",
            "Microsoft.Sql/managedInstances/startStopSchedules",
            "Microsoft.Sql/locations/managedInstancePrivateEndpointConnectionProxyOperationResults",
            "Microsoft.Sql/locations/managedInstancePrivateEndpointConnectionProxyAzureAsyncOperation",
            "Microsoft.Sql/locations/managedInstancePrivateEndpointConnectionOperationResults",
            "Microsoft.Sql/locations/managedInstancePrivateEndpointConnectionAzureAsyncOperation",
            "Microsoft.Sql/locations/longTermRetentionManagedInstances",
            "Microsoft.Sql/locations/longTermRetentionManagedInstanceBackups",
            "Microsoft.Sql/locations/managedInstanceLongTermRetentionPolicyOperationResults",
            "Microsoft.Sql/locations/managedInstanceLongTermRetentionPolicyAzureAsyncOperation",
            "Microsoft.Sql/locations/longTermRetentionManagedInstanceBackupOperationResults",
            "Microsoft.Sql/locations/longTermRetentionManagedInstanceBackupAzureAsyncOperation",
            "Microsoft.Sql/locations/managedDatabaseAzureAsyncOperation",
            "Microsoft.Sql/locations/managedDatabaseOperationResults",
            "Microsoft.Sql/locations/managedDatabaseRestoreAzureAsyncOperation",
            "Microsoft.Sql/locations/managedDatabaseRestoreOperationResults",
            "Microsoft.Sql/locations/managedDatabaseCompleteRestoreAzureAsyncOperation",
            "Microsoft.Sql/locations/managedDatabaseCompleteRestoreOperationResults",
            "Microsoft.Sql/locations/managedServerSecurityAlertPoliciesAzureAsyncOperation",
            "Microsoft.Sql/locations/stopManagedInstanceAzureAsyncOperation",
            "Microsoft.Sql/locations/stopManagedInstanceOperationResults",
            "Microsoft.Sql/locations/startManagedInstanceAzureAsyncOperation",
            "Microsoft.Sql/locations/startManagedInstanceOperationResults",
            "Microsoft.Sql/managedInstances/tdeCertificates",
            "Microsoft.Sql/locations/managedInstanceTdeCertAzureAsyncOperation",
            "Microsoft.Sql/locations/managedInstanceTdeCertOperationResults",
            "Microsoft.Sql/locations/managedServerSecurityAlertPoliciesOperationResults",
            "Microsoft.Sql/locations/securityAlertPoliciesAzureAsyncOperation",
            "Microsoft.Sql/locations/securityAlertPoliciesOperationResults",
            "Microsoft.Sql/locations/advancedThreatProtectionAzureAsyncOperation",
            "Microsoft.Sql/locations/advancedThreatProtectionOperationResults",
            "Microsoft.Sql/locations/managedInstanceAdvancedThreatProtectionAzureAsyncOperation",
            "Microsoft.Sql/locations/managedInstanceAdvancedThreatProtectionOperationResults",
            "Microsoft.Sql/managedInstances/dnsAliases",
            "Microsoft.Sql/locations/managedDnsAliasAsyncOperation",
            "Microsoft.Sql/locations/managedDnsAliasOperationResults",
            "Microsoft.Sql/virtualClusters",
            "Microsoft.Sql/locations/virtualClusterAzureAsyncOperation",
            "Microsoft.Sql/locations/virtualClusterOperationResults",
            "Microsoft.Sql/locations/updateManagedInstanceDnsServersAzureAsyncOperation",
            "Microsoft.Sql/locations/updateManagedInstanceDnsServersOperationResults",
            "Microsoft.Sql/locations/managedInstanceAzureAsyncOperation",
            "Microsoft.Sql/locations/managedInstanceOperationResults",
            "Microsoft.Sql/locations/distributedAvailabilityGroupsOperationResults",
            "Microsoft.Sql/locations/distributedAvailabilityGroupsAzureAsyncOperation",
            "Microsoft.Sql/locations/serverTrustCertificatesOperationResults",
            "Microsoft.Sql/locations/serverTrustCertificatesAzureAsyncOperation",
            "Microsoft.Sql/locations/administratorAzureAsyncOperation",
            "Microsoft.Sql/locations/administratorOperationResults",
            "Microsoft.Sql/locations/syncGroupOperationResults",
            "Microsoft.Sql/locations/syncGroupAzureAsyncOperation",
            "Microsoft.Sql/locations/syncMemberOperationResults",
            "Microsoft.Sql/locations/syncAgentOperationResults",
            "Microsoft.Sql/locations/syncDatabaseIds",
            "Microsoft.Sql/locations/longTermRetentionServers",
            "Microsoft.Sql/locations/longTermRetentionBackups",
            "Microsoft.Sql/locations/longTermRetentionPolicyOperationResults",
            "Microsoft.Sql/locations/longTermRetentionPolicyAzureAsyncOperation",
            "Microsoft.Sql/locations/longTermRetentionBackupOperationResults",
            "Microsoft.Sql/locations/longTermRetentionBackupAzureAsyncOperation",
            "Microsoft.Sql/locations/changeLongTermRetentionBackupAccessTierOperationResults",
            "Microsoft.Sql/locations/changeLongTermRetentionBackupAccessTierAzureAsyncOperation",
            "Microsoft.Sql/locations/shortTermRetentionPolicyOperationResults",
            "Microsoft.Sql/locations/shortTermRetentionPolicyAzureAsyncOperation",
            "Microsoft.Sql/locations/managedShortTermRetentionPolicyOperationResults",
            "Microsoft.Sql/locations/managedShortTermRetentionPolicyAzureAsyncOperation",
            "Microsoft.Sql/locations/instanceFailoverGroups",
            "Microsoft.Sql/locations/instanceFailoverGroupAzureAsyncOperation",
            "Microsoft.Sql/locations/instanceFailoverGroupOperationResults",
            "Microsoft.Sql/locations/privateEndpointConnectionProxyOperationResults",
            "Microsoft.Sql/locations/privateEndpointConnectionProxyAzureAsyncOperation",
            "Microsoft.Sql/locations/privateEndpointConnectionOperationResults",
            "Microsoft.Sql/locations/outboundFirewallRulesAzureAsyncOperation",
            "Microsoft.Sql/locations/outboundFirewallRulesOperationResults",
            "Microsoft.Sql/locations/privateEndpointConnectionAzureAsyncOperation",
            "Microsoft.Sql/locations/notifyAzureAsyncOperation",
            "Microsoft.Sql/locations/serverTrustGroups",
            "Microsoft.Sql/locations/serverTrustGroupOperationResults",
            "Microsoft.Sql/locations/serverTrustGroupAzureAsyncOperation",
            "Microsoft.Sql/locations/managedDatabaseMoveOperationResults",
            "Microsoft.Sql/locations/managedDatabaseMoveAzureAsyncOperation",
            "Microsoft.Sql/servers/connectionPolicies",
            "Microsoft.Sql/locations/connectionPoliciesAzureAsyncOperation",
            "Microsoft.Sql/locations/connectionPoliciesOperationResults",
            "Microsoft.Sql/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.Sql/locations/replicationLinksAzureAsyncOperation",
            "Microsoft.Sql/locations/replicationLinksOperationResults",
            "Microsoft.Sql/locations/managedInstanceDtcAzureAsyncOperation",
            "Microsoft.Sql/managedInstances/databases/ledgerDigestUploads",
            "Microsoft.Sql/locations/managedLedgerDigestUploadsOperationResults",
            "Microsoft.Sql/locations/managedLedgerDigestUploadsAzureAsyncOperation",
            "Microsoft.Sql/locations/serverConfigurationOptionAzureAsyncOperation",
            "Microsoft.Sql/servers/failoverGroups/tryPlannedBeforeForcedFailover",
            "Microsoft.DBforMySQL/operations",
            "Microsoft.DBforMySQL/servers",
            "Microsoft.DBforMySQL/flexibleServers",
            "Microsoft.DBforMySQL/servers/recoverableServers",
            "Microsoft.DBforMySQL/servers/virtualNetworkRules",
            "Microsoft.DBforMySQL/locations/capabilities",
            "Microsoft.DBforMySQL/locations/capabilitySets",
            "Microsoft.DBforMySQL/locations/checkNameAvailability",
            "Microsoft.DBforMySQL/checkNameAvailability",
            "Microsoft.DBforMySQL/assessForMigration",
            "Microsoft.DBforMySQL/getPrivateDnsZoneSuffix",
            "Microsoft.DBforMySQL/locations/checkVirtualNetworkSubnetUsage",
            "Microsoft.DBforMySQL/locations/listMigrations",
            "Microsoft.DBforMySQL/locations/updateMigration",
            "Microsoft.DBforMySQL/locations",
            "Microsoft.DBforMySQL/locations/operationResults",
            "Microsoft.DBforMySQL/locations/operationProgress",
            "Microsoft.DBforMySQL/locations/azureAsyncOperation",
            "Microsoft.DBforMySQL/locations/administratorOperationResults",
            "Microsoft.DBforMySQL/locations/administratorAzureAsyncOperation",
            "Microsoft.DBforMySQL/locations/privateEndpointConnectionProxyOperationResults",
            "Microsoft.DBforMySQL/locations/privateEndpointConnectionProxyAzureAsyncOperation",
            "Microsoft.DBforMySQL/locations/privateEndpointConnectionOperationResults",
            "Microsoft.DBforMySQL/locations/privateEndpointConnectionAzureAsyncOperation",
            "Microsoft.DBforMySQL/locations/performanceTiers",
            "Microsoft.DBforMySQL/locations/securityAlertPoliciesAzureAsyncOperation",
            "Microsoft.DBforMySQL/locations/securityAlertPoliciesOperationResults",
            "Microsoft.DBforMySQL/locations/recommendedActionSessionsAzureAsyncOperation",
            "Microsoft.DBforMySQL/locations/recommendedActionSessionsOperationResults",
            "Microsoft.DBforMySQL/servers/topQueryStatistics",
            "Microsoft.DBforMySQL/servers/queryTexts",
            "Microsoft.DBforMySQL/servers/waitStatistics",
            "Microsoft.DBforMySQL/servers/resetQueryPerformanceInsightData",
            "Microsoft.DBforMySQL/servers/advisors",
            "Microsoft.DBforMySQL/servers/privateLinkResources",
            "Microsoft.DBforMySQL/servers/privateEndpointConnections",
            "Microsoft.DBforMySQL/servers/privateEndpointConnectionProxies",
            "Microsoft.DBforMySQL/servers/keys",
            "Microsoft.DBforMySQL/locations/serverKeyAzureAsyncOperation",
            "Microsoft.DBforMySQL/locations/serverKeyOperationResults",
            "Microsoft.DBforMySQL/servers/upgrade",
            "Microsoft.CognitiveServices/accounts",
            "Microsoft.CognitiveServices/operations",
            "Microsoft.CognitiveServices/locations/operationResults",
            "Microsoft.CognitiveServices/locations",
            "Microsoft.CognitiveServices/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.CognitiveServices/locations/checkSkuAvailability",
            "Microsoft.CognitiveServices/checkDomainAvailability",
            "Microsoft.CognitiveServices/accounts/privateLinkResources",
            "Microsoft.CognitiveServices/accounts/privateEndpointConnections",
            "Microsoft.CognitiveServices/accounts/privateEndpointConnectionProxies",
            "Microsoft.CognitiveServices/deletedAccounts",
            "Microsoft.CognitiveServices/locations/resourceGroups",
            "Microsoft.CognitiveServices/locations/resourceGroups/deletedAccounts",
            "Microsoft.CognitiveServices/locations/commitmentTiers",
            "Microsoft.CognitiveServices/locations/models",
            "Microsoft.CognitiveServices/locations/usages",
            "Microsoft.CognitiveServices/locations/raiContentFilters",
            "Microsoft.CognitiveServices/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.CognitiveServices/accounts/networkSecurityPerimeterAssociationProxies",
            "Microsoft.CognitiveServices/accounts/encryptionScopes",
            "Microsoft.CognitiveServices/commitmentPlans",
            "Microsoft.CognitiveServices/attestations",
            "Microsoft.CognitiveServices/attestationDefinitions",
            "Microsoft.Media/mediaservices",
            "Microsoft.Media/mediaservices/assets",
            "Microsoft.Media/mediaservices/assets/tracks",
            "Microsoft.Media/mediaservices/assets/tracks/operationstatuses",
            "Microsoft.Media/mediaservices/assets/tracks/operationResults",
            "Microsoft.Media/mediaservices/contentKeyPolicies",
            "Microsoft.Media/mediaservices/streamingLocators",
            "Microsoft.Media/mediaservices/streamingPolicies",
            "Microsoft.Media/mediaservices/eventGridFilters",
            "Microsoft.Media/mediaservices/transforms",
            "Microsoft.Media/mediaservices/transforms/jobs",
            "Microsoft.Media/mediaservices/streamingEndpoints",
            "Microsoft.Media/mediaservices/liveEvents",
            "Microsoft.Media/mediaservices/liveEvents/liveOutputs",
            "Microsoft.Media/mediaservices/streamingEndpointOperations",
            "Microsoft.Media/mediaservices/liveEventOperations",
            "Microsoft.Media/mediaservices/liveOutputOperations",
            "Microsoft.Media/mediaservices/streamingendpoints/operationlocations",
            "Microsoft.Media/mediaservices/liveevents/operationlocations",
            "Microsoft.Media/mediaservices/liveevents/liveoutputs/operationlocations",
            "Microsoft.Media/mediaservices/privateEndpointConnectionProxies",
            "Microsoft.Media/mediaservices/privateEndpointConnections",
            "Microsoft.Media/mediaservices/privateEndpointConnectionOperations",
            "Microsoft.Media/locations/mediaServicesOperationStatuses",
            "Microsoft.Media/locations/mediaServicesOperationResults",
            "Microsoft.Media/mediaservices/assets/assetFilters",
            "Microsoft.Media/mediaservices/accountFilters",
            "Microsoft.Media/operations",
            "Microsoft.Media/checknameavailability",
            "Microsoft.Media/locations",
            "Microsoft.Media/locations/checkNameAvailability",
            "Microsoft.Web/publishingUsers",
            "Microsoft.Web/ishostnameavailable",
            "Microsoft.Web/validate",
            "Microsoft.Web/isusernameavailable",
            "Microsoft.Web/generateGithubAccessTokenForAppserviceCLI",
            "Microsoft.Web/sourceControls",
            "Microsoft.Web/availableStacks",
            "Microsoft.Web/webAppStacks",
            "Microsoft.Web/locations/webAppStacks",
            "Microsoft.Web/functionAppStacks",
            "Microsoft.Web/locations/functionAppStacks",
            "Microsoft.Web/staticSites",
            "Microsoft.Web/locations/previewStaticSiteWorkflowFile",
            "Microsoft.Web/staticSites/userProvidedFunctionApps",
            "Microsoft.Web/staticSites/linkedBackends",
            "Microsoft.Web/staticSites/builds/linkedBackends",
            "Microsoft.Web/staticSites/databaseConnections",
            "Microsoft.Web/staticSites/builds/databaseConnections",
            "Microsoft.Web/staticSites/builds",
            "Microsoft.Web/staticSites/builds/userProvidedFunctionApps",
            "Microsoft.Web/listSitesAssignedToHostName",
            "Microsoft.Web/locations/getNetworkPolicies",
            "Microsoft.Web/locations/operations",
            "Microsoft.Web/locations/operationResults",
            "Microsoft.Web/sites/networkConfig",
            "Microsoft.Web/sites/slots/networkConfig",
            "Microsoft.Web/sites/hostNameBindings",
            "Microsoft.Web/sites/slots/hostNameBindings",
            "Microsoft.Web/operations",
            "Microsoft.Web/certificates",
            "Microsoft.Web/serverFarms",
            "Microsoft.Web/sites",
            "Microsoft.Web/sites/slots",
            "Microsoft.Web/runtimes",
            "Microsoft.Web/recommendations",
            "Microsoft.Web/resourceHealthMetadata",
            "Microsoft.Web/aseregions",
            "Microsoft.Web/georegions",
            "Microsoft.Web/sites/premieraddons",
            "Microsoft.Web/hostingEnvironments",
            "Microsoft.Web/hostingEnvironments/multiRolePools",
            "Microsoft.Web/hostingEnvironments/workerPools",
            "Microsoft.Web/kubeEnvironments",
            "Microsoft.Web/deploymentLocations",
            "Microsoft.Web/deletedSites",
            "Microsoft.Web/locations/deletedSites",
            "Microsoft.Web/ishostingenvironmentnameavailable",
            "Microsoft.Web/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.Web/locations/validateDeleteVirtualNetworkOrSubnets",
            "Microsoft.Web/connections",
            "Microsoft.Web/customApis",
            "Microsoft.Web/locations",
            "Microsoft.Web/locations/listWsdlInterfaces",
            "Microsoft.Web/locations/extractApiDefinitionFromWsdl",
            "Microsoft.Web/locations/managedApis",
            "Microsoft.Web/locations/runtimes",
            "Microsoft.Web/locations/apiOperations",
            "Microsoft.Web/connectionGateways",
            "Microsoft.Web/locations/connectionGatewayInstallations",
            "Microsoft.Web/checkNameAvailability",
            "Microsoft.Web/billingMeters",
            "Microsoft.Web/verifyHostingEnvironmentVnet",
            "Microsoft.Web/serverFarms/eventGridFilters",
            "Microsoft.Web/sites/eventGridFilters",
            "Microsoft.Web/sites/slots/eventGridFilters",
            "Microsoft.Web/hostingEnvironments/eventGridFilters",
            "Microsoft.Web/serverFarms/firstPartyApps",
            "Microsoft.Web/serverFarms/firstPartyApps/keyVaultSettings",
            "Microsoft.Web/containerApps",
            "Microsoft.Web/customhostnameSites",
            "Microsoft.Web/locations/usages",
            "Microsoft.Search/searchServices",
            "Microsoft.Search/checkServiceNameAvailability",
            "Microsoft.Search/checkNameAvailability",
            "Microsoft.Search/resourceHealthMetadata",
            "Microsoft.Search/operations",
            "Microsoft.Search/locations",
            "Microsoft.Search/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.Search/locations/usages",
            "Microsoft.Search/locations/operationResults",
            "Microsoft.DataLakeStore/accounts",
            "Microsoft.DataLakeStore/accounts/firewallRules",
            "Microsoft.DataLakeStore/accounts/eventGridFilters",
            "Microsoft.DataLakeStore/locations",
            "Microsoft.DataLakeStore/locations/operationresults",
            "Microsoft.DataLakeStore/locations/checkNameAvailability",
            "Microsoft.DataLakeStore/locations/capability",
            "Microsoft.DataLakeStore/locations/usages",
            "Microsoft.DataLakeStore/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.DataLakeStore/operations",
            "Microsoft.DataMigration/locations",
            "Microsoft.DataMigration/services",
            "Microsoft.DataMigration/services/projects",
            "Microsoft.DataMigration/locations/operationResults",
            "Microsoft.DataMigration/locations/operationStatuses",
            "Microsoft.DataMigration/locations/checkNameAvailability",
            "Microsoft.DataMigration/operations",
            "Microsoft.DataMigration/migrationServices",
            "Microsoft.DataMigration/SqlMigrationServices",
            "Microsoft.DataMigration/DatabaseMigrations",
            "Microsoft.DataMigration/Locations/OperationTypes",
            "Microsoft.DataMigration/locations/migrationServiceOperationResults",
            "Microsoft.DataMigration/Locations/sqlMigrationServiceOperationResults",
            "Microsoft.Kusto/clusters",
            "Microsoft.Kusto/clusters/databases",
            "Microsoft.Kusto/clusters/attacheddatabaseconfigurations",
            "Microsoft.Kusto/clusters/principalassignments",
            "Microsoft.Kusto/clusters/databases/eventhubconnections",
            "Microsoft.Kusto/clusters/databases/dataconnections",
            "Microsoft.Kusto/clusters/databases/principalassignments",
            "Microsoft.Kusto/locations/operationResults",
            "Microsoft.Kusto/locations",
            "Microsoft.Kusto/locations/checkNameAvailability",
            "Microsoft.Kusto/locations/skus",
            "Microsoft.Kusto/operations",
            "Microsoft.Kusto/clusters/databases/scripts",
            "Microsoft.Kusto/clusters/managedPrivateEndpoints",
            "Microsoft.Kusto/clusters/sandboxCustomImages",
            "Microsoft.ApiManagement/service",
            "Microsoft.ApiManagement/deletedServices",
            "Microsoft.ApiManagement/locations",
            "Microsoft.ApiManagement/locations/deletedServices",
            "Microsoft.ApiManagement/validateServiceName",
            "Microsoft.ApiManagement/checkServiceNameAvailability",
            "Microsoft.ApiManagement/checkNameAvailability",
            "Microsoft.ApiManagement/reportFeedback",
            "Microsoft.ApiManagement/checkFeedbackRequired",
            "Microsoft.ApiManagement/operations",
            "Microsoft.ApiManagement/getDomainOwnershipIdentifier",
            "Microsoft.ApiManagement/service/eventGridFilters",
            "Microsoft.MixedReality/locations",
            "Microsoft.MixedReality/locations/checkNameAvailability",
            "Microsoft.MixedReality/operations",
            "Microsoft.MixedReality/spatialAnchorsAccounts",
            "Microsoft.MixedReality/remoteRenderingAccounts",
            "Microsoft.MixedReality/objectAnchorsAccounts",
            "Microsoft.Maps/accounts",
            "Microsoft.Maps/accounts/creators",
            "Microsoft.Maps/accounts/eventGridFilters",
            "Microsoft.Maps/operations",
            "Microsoft.AVS/locations",
            "Microsoft.AVS/locations/checkQuotaAvailability",
            "Microsoft.AVS/locations/checkTrialAvailability",
            "Microsoft.AVS/locations/usages",
            "Microsoft.AVS/operations",
            "Microsoft.AVS/privateClouds",
            "Microsoft.AVS/privateClouds/addons",
            "Microsoft.AVS/privateClouds/authorizations",
            "Microsoft.AVS/privateClouds/cloudLinks",
            "Microsoft.AVS/privateClouds/clusters",
            "Microsoft.AVS/privateClouds/clusters/datastores",
            "Microsoft.AVS/privateClouds/clusters/placementPolicies",
            "Microsoft.AVS/privateClouds/clusters/virtualMachines",
            "Microsoft.AVS/privateClouds/eventGridFilters",
            "Microsoft.AVS/privateClouds/globalReachConnections",
            "Microsoft.AVS/privateClouds/hcxEnterpriseSites",
            "Microsoft.AVS/privateClouds/scriptExecutions",
            "Microsoft.AVS/privateClouds/scriptPackages",
            "Microsoft.AVS/privateClouds/scriptPackages/scriptCmdlets",
            "Microsoft.AVS/privateClouds/workloadNetworks",
            "Microsoft.AVS/privateClouds/workloadNetworks/dhcpConfigurations",
            "Microsoft.AVS/privateClouds/workloadNetworks/dnsServices",
            "Microsoft.AVS/privateClouds/workloadNetworks/dnsZones",
            "Microsoft.AVS/privateClouds/workloadNetworks/gateways",
            "Microsoft.AVS/privateClouds/workloadNetworks/portMirroringProfiles",
            "Microsoft.AVS/privateClouds/workloadNetworks/publicIPs",
            "Microsoft.AVS/privateClouds/workloadNetworks/segments",
            "Microsoft.AVS/privateClouds/workloadNetworks/virtualMachines",
            "Microsoft.AVS/privateClouds/workloadNetworks/vmGroups",
            "Microsoft.Blueprint/blueprints",
            "Microsoft.Blueprint/blueprints/artifacts",
            "Microsoft.Blueprint/blueprints/versions",
            "Microsoft.Blueprint/blueprints/versions/artifacts",
            "Microsoft.Blueprint/blueprintAssignments",
            "Microsoft.Blueprint/blueprintAssignments/operations",
            "Microsoft.Blueprint/blueprintAssignments/assignmentOperations",
            "Microsoft.Blueprint/operations",
            "Microsoft.HealthcareApis/services",
            "Microsoft.HealthcareApis/services/privateEndpointConnectionProxies",
            "Microsoft.HealthcareApis/services/privateEndpointConnections",
            "Microsoft.HealthcareApis/services/privateLinkResources",
            "Microsoft.HealthcareApis/services/iomtconnectors",
            "Microsoft.HealthcareApis/services/iomtconnectors/connections",
            "Microsoft.HealthcareApis/services/iomtconnectors/mappings",
            "Microsoft.HealthcareApis/workspaces",
            "Microsoft.HealthcareApis/workspaces/privateEndpointConnectionProxies",
            "Microsoft.HealthcareApis/workspaces/privateEndpointConnections",
            "Microsoft.HealthcareApis/workspaces/privateLinkResources",
            "Microsoft.HealthcareApis/workspaces/dicomservices",
            "Microsoft.HealthcareApis/workspaces/iotconnectors",
            "Microsoft.HealthcareApis/workspaces/iotconnectors/fhirdestinations",
            "Microsoft.HealthcareApis/workspaces/fhirservices",
            "Microsoft.HealthcareApis/workspaces/eventGridFilters",
            "Microsoft.HealthcareApis/locations",
            "Microsoft.HealthcareApis/locations/operationresults",
            "Microsoft.HealthcareApis/checkNameAvailability",
            "Microsoft.HealthcareApis/operations",
            "Microsoft.HealthcareApis/validateMedtechMappings",
            "Microsoft.Advisor/suppressions",
            "Microsoft.Advisor/configurations",
            "Microsoft.Advisor/metadata",
            "Microsoft.Advisor/recommendations",
            "Microsoft.Advisor/generateRecommendations",
            "Microsoft.Advisor/operations",
            "Microsoft.Advisor/advisorScore",
            "Microsoft.Advisor/predict",
            "Microsoft.MarketplaceNotifications/reviewsnotifications",
            "Microsoft.MarketplaceNotifications/operations",
            "Microsoft.ServiceLinker/locations",
            "Microsoft.ServiceLinker/locations/operationStatuses",
            "Microsoft.ServiceLinker/operations",
            "Microsoft.ServiceLinker/linkers",
            "Microsoft.ServiceLinker/dryruns",
            "Microsoft.ServiceLinker/locations/connectors",
            "Microsoft.ServiceLinker/locations/dryruns",
            "Microsoft.ServiceLinker/configurationNames",
            "Microsoft.ServiceLinker/daprConfigurations",
            "Microsoft.DataProtection/BackupVaults",
            "Microsoft.DataProtection/ResourceGuards",
            "Microsoft.DataProtection/operations",
            "Microsoft.DataProtection/locations",
            "Microsoft.DataProtection/locations/operationResults",
            "Microsoft.DataProtection/locations/operationStatus",
            "Microsoft.DataProtection/locations/checkNameAvailability",
            "Microsoft.DataProtection/locations/checkFeatureSupport",
            "Microsoft.DataProtection/backupInstances",
            "Microsoft.DataProtection/locations/fetchSecondaryRecoveryPoints",
            "Microsoft.DataProtection/locations/fetchCrossRegionRestoreJobs",
            "Microsoft.DataProtection/locations/fetchCrossRegionRestoreJob",
            "Microsoft.DataProtection/locations/validateCrossRegionRestore",
            "Microsoft.DataProtection/locations/crossRegionRestore",
            "Microsoft.Consumption/Forecasts",
            "Microsoft.Consumption/AggregatedCost",
            "Microsoft.Consumption/tenants",
            "Microsoft.Consumption/ReservationRecommendations",
            "Microsoft.Consumption/ReservationRecommendationDetails",
            "Microsoft.Consumption/ReservationSummaries",
            "Microsoft.Consumption/ReservationTransactions",
            "Microsoft.Consumption/Balances",
            "Microsoft.Consumption/Marketplaces",
            "Microsoft.Consumption/Pricesheets",
            "Microsoft.Consumption/ReservationDetails",
            "Microsoft.Consumption/Budgets",
            "Microsoft.Consumption/CostTags",
            "Microsoft.Consumption/Tags",
            "Microsoft.Consumption/Terms",
            "Microsoft.Consumption/UsageDetails",
            "Microsoft.Consumption/Charges",
            "Microsoft.Consumption/credits",
            "Microsoft.Consumption/events",
            "Microsoft.Consumption/lots",
            "Microsoft.Consumption/products",
            "Microsoft.Consumption/OperationStatus",
            "Microsoft.Consumption/OperationResults",
            "Microsoft.Consumption/Operations",
            "Microsoft.GuestConfiguration/guestConfigurationAssignments",
            "Microsoft.GuestConfiguration/operations",
            "Astronomer.Astro/locations",
            "Astronomer.Astro/operations",
            "Astronomer.Astro/organizations",
            "Astronomer.Astro/locations/operationStatuses",
            "Dynatrace.Observability/operations",
            "Dynatrace.Observability/registeredSubscriptions",
            "Dynatrace.Observability/locations",
            "Dynatrace.Observability/locations/operationStatuses",
            "Dynatrace.Observability/monitors",
            "Dynatrace.Observability/monitors/tagRules",
            "Dynatrace.Observability/monitors/singleSignOnConfigurations",
            "Dynatrace.Observability/checkNameAvailability",
            "Dynatrace.Observability/getMarketplaceSaaSResourceDetails",
            "GitHub.Network/Operations",
            "GitHub.Network/networkSettings",
            "GitHub.Network/registeredSubscriptions",
            "Microsoft.AAD/DomainServices",
            "Microsoft.AAD/DomainServices/oucontainer",
            "Microsoft.AAD/locations",
            "Microsoft.AAD/locations/operationresults",
            "Microsoft.AAD/operations",
            "Microsoft.AadCustomSecurityAttributesDiagnosticSettings/operations",
            "Microsoft.AadCustomSecurityAttributesDiagnosticSettings/diagnosticSettings",
            "Microsoft.AadCustomSecurityAttributesDiagnosticSettings/diagnosticSettingsCategories",
            "microsoft.aadiam/azureADMetrics",
            "microsoft.aadiam/privateLinkForAzureAD",
            "microsoft.aadiam/tenants",
            "microsoft.aadiam/operations",
            "microsoft.aadiam/diagnosticSettings",
            "microsoft.aadiam/diagnosticSettingsCategories",
            "Microsoft.Addons/supportProviders",
            "Microsoft.Addons/operations",
            "Microsoft.Addons/operationResults",
            "Microsoft.ADHybridHealthService/services",
            "Microsoft.ADHybridHealthService/addsservices",
            "Microsoft.ADHybridHealthService/configuration",
            "Microsoft.ADHybridHealthService/operations",
            "Microsoft.ADHybridHealthService/agents",
            "Microsoft.ADHybridHealthService/aadsupportcases",
            "Microsoft.ADHybridHealthService/reports",
            "Microsoft.ADHybridHealthService/servicehealthmetrics",
            "Microsoft.ADHybridHealthService/logs",
            "Microsoft.ADHybridHealthService/anonymousapiusers",
            "Microsoft.AgFoodPlatform/operations",
            "Microsoft.AgFoodPlatform/farmBeatsExtensionDefinitions",
            "Microsoft.AgFoodPlatform/farmBeatsSolutionDefinitions",
            "Microsoft.AgFoodPlatform/checkNameAvailability",
            "Microsoft.AgFoodPlatform/locations",
            "Microsoft.AksHybrid/locations",
            "Microsoft.AnalysisServices/servers",
            "Microsoft.AnalysisServices/locations",
            "Microsoft.AnalysisServices/locations/checkNameAvailability",
            "Microsoft.AnalysisServices/locations/operationresults",
            "Microsoft.AnalysisServices/locations/operationstatuses",
            "Microsoft.AnalysisServices/operations",
            "Microsoft.AnyBuild/Locations",
            "Microsoft.AnyBuild/Locations/OperationStatuses",
            "Microsoft.AnyBuild/clusters",
            "Microsoft.AnyBuild/Operations",
            "Microsoft.ApiCenter/services",
            "Microsoft.ApiCenter/operations",
            "Microsoft.ApiCenter/services/eventGridFilters",
            "Microsoft.ApiSecurity/Locations",
            "Microsoft.ApiSecurity/Locations/OperationStatuses",
            "Microsoft.ApiSecurity/Operations",
            "Microsoft.ApiSecurity/apiCollections",
            "Microsoft.ApiSecurity/apiCollections/apiCollectionDetails",
            "Microsoft.ApiSecurity/apiCollectionsMeta",
            "Microsoft.ApiSecurity/apiCollectionsMeta/apiCollectionMetaDetails",
            "Microsoft.App/managedEnvironments",
            "Microsoft.App/managedEnvironments/certificates",
            "Microsoft.App/managedEnvironments/managedCertificates",
            "Microsoft.App/containerApps",
            "Microsoft.App/jobs",
            "Microsoft.App/locations",
            "Microsoft.App/locations/managedEnvironmentOperationResults",
            "Microsoft.App/locations/managedEnvironmentOperationStatuses",
            "Microsoft.App/locations/containerappOperationResults",
            "Microsoft.App/locations/containerappOperationStatuses",
            "Microsoft.App/locations/containerappsjobOperationResults",
            "Microsoft.App/locations/containerappsjobOperationStatuses",
            "Microsoft.App/locations/sourceControlOperationResults",
            "Microsoft.App/locations/sourceControlOperationStatuses",
            "Microsoft.App/locations/usages",
            "Microsoft.App/operations",
            "Microsoft.App/connectedEnvironments",
            "Microsoft.App/connectedEnvironments/certificates",
            "Microsoft.App/locations/connectedEnvironmentOperationResults",
            "Microsoft.App/locations/connectedEnvironmentOperationStatuses",
            "Microsoft.App/locations/managedCertificateOperationStatuses",
            "Microsoft.App/locations/billingMeters",
            "Microsoft.App/locations/availableManagedEnvironmentsWorkloadProfileTypes",
            "Microsoft.App/getCustomDomainVerificationId",
            "Microsoft.App/builders",
            "Microsoft.App/builders/builds",
            "Microsoft.App/locations/OperationResults",
            "Microsoft.App/locations/OperationStatuses",
            "Microsoft.App/managedEnvironments/dotNetComponents",
            "Microsoft.App/managedEnvironments/javaComponents",
            "Microsoft.App/managedEnvironments/daprComponents",
            "Microsoft.AppAssessment/Locations",
            "Microsoft.AppAssessment/operations",
            "Microsoft.AppAssessment/Locations/OperationStatuses",
            "Microsoft.AppAssessment/Locations/osVersions",
            "Microsoft.AppComplianceAutomation/operations",
            "Microsoft.AppComplianceAutomation/locations",
            "Microsoft.AppComplianceAutomation/locations/operationStatuses",
            "Microsoft.AppComplianceAutomation/reports",
            "Microsoft.AppComplianceAutomation/reports/snapshots",
            "Microsoft.AppComplianceAutomation/onboard",
            "Microsoft.AppComplianceAutomation/triggerEvaluation",
            "Microsoft.AppComplianceAutomation/reports/webhooks",
            "Microsoft.AppComplianceAutomation/reports/evidences",
            "Microsoft.AppComplianceAutomation/listInUseStorageAccounts",
            "Microsoft.AppComplianceAutomation/checkNameAvailability",
            "Microsoft.AppComplianceAutomation/getCollectionCount",
            "Microsoft.AppComplianceAutomation/getOverviewStatus",
            "Microsoft.AppComplianceAutomation/reports/scopingConfigurations",
            "Microsoft.AppConfiguration/configurationStores",
            "Microsoft.AppConfiguration/configurationStores/keyValues",
            "Microsoft.AppConfiguration/configurationStores/eventGridFilters",
            "Microsoft.AppConfiguration/checkNameAvailability",
            "Microsoft.AppConfiguration/locations/checkNameAvailability",
            "Microsoft.AppConfiguration/locations",
            "Microsoft.AppConfiguration/locations/operationsStatus",
            "Microsoft.AppConfiguration/operations",
            "Microsoft.AppConfiguration/deletedConfigurationStores",
            "Microsoft.AppConfiguration/locations/deletedConfigurationStores",
            "Microsoft.AppConfiguration/configurationStores/replicas",
            "Microsoft.AppConfiguration/configurationStores/snapshots",
            "Microsoft.AppConfiguration/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.AppSecurity/operationStatuses",
            "Microsoft.ArcNetworking/locations",
            "Microsoft.ArcNetworking/locations/operationStatuses",
            "Microsoft.ArcNetworking/arcNwLoadBalancers",
            "Microsoft.Attestation/attestationProviders",
            "Microsoft.Attestation/defaultProviders",
            "Microsoft.Attestation/locations",
            "Microsoft.Attestation/locations/defaultProvider",
            "Microsoft.Attestation/operations",
            "Microsoft.Authorization/roleAssignmentScheduleRequests",
            "Microsoft.Authorization/roleEligibilityScheduleRequests",
            "Microsoft.Authorization/roleAssignmentSchedules",
            "Microsoft.Authorization/roleEligibilitySchedules",
            "Microsoft.Authorization/roleAssignmentScheduleInstances",
            "Microsoft.Authorization/roleEligibilityScheduleInstances",
            "Microsoft.Authorization/roleManagementPolicies",
            "Microsoft.Authorization/roleManagementPolicyAssignments",
            "Microsoft.Authorization/eligibleChildResources",
            "Microsoft.Authorization/roleManagementAlerts",
            "Microsoft.Authorization/roleManagementAlertConfigurations",
            "Microsoft.Authorization/roleManagementAlertDefinitions",
            "Microsoft.Authorization/roleManagementAlertOperations",
            "Microsoft.Authorization/roleAssignments",
            "Microsoft.Authorization/roleDefinitions",
            "Microsoft.Authorization/classicAdministrators",
            "Microsoft.Authorization/permissions",
            "Microsoft.Authorization/denyAssignments",
            "Microsoft.Authorization/locks",
            "Microsoft.Authorization/operations",
            "Microsoft.Authorization/policyDefinitions",
            "Microsoft.Authorization/policyDefinitions/versions",
            "Microsoft.Authorization/policySetDefinitions",
            "Microsoft.Authorization/policySetDefinitions/versions",
            "Microsoft.Authorization/policyAssignments",
            "Microsoft.Authorization/policyExemptions",
            "Microsoft.Authorization/listPolicyDefinitionVersions",
            "Microsoft.Authorization/listPolicySetDefinitionVersions",
            "Microsoft.Authorization/dataAliases",
            "Microsoft.Authorization/dataPolicyManifests",
            "Microsoft.Authorization/providerOperations",
            "Microsoft.Authorization/elevateAccess",
            "Microsoft.Authorization/checkAccess",
            "Microsoft.Authorization/batchResourceCheckAccess",
            "Microsoft.Authorization/findOrphanRoleAssignments",
            "Microsoft.Authorization/roleAssignmentsUsageMetrics",
            "Microsoft.Authorization/accessReviewScheduleDefinitions",
            "Microsoft.Authorization/accessReviewScheduleSettings",
            "Microsoft.Authorization/accessReviewHistoryDefinitions",
            "Microsoft.Authorization/roleAssignmentApprovals",
            "Microsoft.Authorization/privateLinkAssociations",
            "Microsoft.Authorization/resourceManagementPrivateLinks",
            "Microsoft.Authorization/EnablePrivateLinkNetworkAccess",
            "Microsoft.Authorization/operationStatus",
            "Microsoft.Authorization/diagnosticSettings",
            "Microsoft.Authorization/diagnosticSettingsCategories",
            "Microsoft.Automanage/configurationProfileAssignments",
            "Microsoft.Automanage/configurationProfiles",
            "Microsoft.Automanage/configurationProfiles/versions",
            "Microsoft.Automanage/bestPractices",
            "Microsoft.Automanage/bestPractices/versions",
            "Microsoft.Automanage/operations",
            "Microsoft.Automanage/servicePrincipals",
            "Microsoft.AutonomousDevelopmentPlatform/operations",
            "Microsoft.AutonomousDevelopmentPlatform/locations",
            "Microsoft.AutonomousDevelopmentPlatform/locations/operationstatuses",
            "Microsoft.AutonomousDevelopmentPlatform/checknameavailability",
            "Microsoft.AutonomousDevelopmentPlatform/workspaces/eventgridfilters",
            "Microsoft.AwsConnector/Locations",
            "Microsoft.AwsConnector/Operations",
            "Microsoft.AzureActiveDirectory/ciamDirectories",
            "Microsoft.AzureActiveDirectory/guestUsages",
            "Microsoft.AzureActiveDirectory/b2cDirectories",
            "Microsoft.AzureActiveDirectory/checkNameAvailability",
            "Microsoft.AzureActiveDirectory/operations",
            "Microsoft.AzureActiveDirectory/b2ctenants",
            "Microsoft.AzureActiveDirectory/operationStatuses",
            "Microsoft.AzureArcData/Locations",
            "Microsoft.AzureArcData/Locations/OperationStatuses",
            "Microsoft.AzureArcData/DataControllers",
            "Microsoft.AzureArcData/SqlManagedInstances",
            "Microsoft.AzureArcData/PostgresInstances",
            "Microsoft.AzureArcData/SqlServerInstances",
            "Microsoft.AzureArcData/Operations",
            "Microsoft.AzureArcData/DataControllers/ActiveDirectoryConnectors",
            "Microsoft.AzureArcData/SqlServerInstances/Databases",
            "Microsoft.AzureArcData/SqlManagedInstances/FailoverGroups",
            "Microsoft.AzureArcData/SqlServerInstances/AvailabilityGroups",
            "Microsoft.AzureFleet/locations",
            "Microsoft.AzureLargeInstance/azureLargeInstances",
            "Microsoft.AzureLargeInstance/azureLargeStorageInstances",
            "Microsoft.AzureLargeInstance/locations",
            "Microsoft.AzureLargeInstance/locations/operationsStatus",
            "Microsoft.AzureLargeInstance/operations",
            "Microsoft.AzurePercept/checkNameAvailability",
            "Microsoft.AzurePercept/operations",
            "Microsoft.AzurePlaywrightService/operations",
            "Microsoft.AzurePlaywrightService/checkNameAvailability",
            "Microsoft.AzurePlaywrightService/Locations",
            "Microsoft.AzurePlaywrightService/Locations/OperationStatuses",
            "Microsoft.AzurePlaywrightService/accounts",
            "Microsoft.AzurePlaywrightService/registeredSubscriptions",
            "Microsoft.AzurePlaywrightService/Locations/Quotas",
            "Microsoft.AzureScan/scanningAccounts",
            "Microsoft.AzureScan/locations",
            "Microsoft.AzureScan/locations/OperationStatuses",
            "Microsoft.AzureScan/Operations",
            "Microsoft.AzureScan/checkNameAvailability",
            "Microsoft.AzureSphere/catalogs",
            "Microsoft.AzureSphere/catalogs/products",
            "Microsoft.AzureSphere/catalogs/products/devicegroups",
            "Microsoft.AzureSphere/locations",
            "Microsoft.AzureSphere/catalogs/certificates",
            "Microsoft.AzureSphere/catalogs/images",
            "Microsoft.AzureSphere/operations",
            "Microsoft.AzureSphere/locations/operationStatuses",
            "Microsoft.AzureSphere/catalogs/products/devicegroups/devices",
            "Microsoft.AzureSphere/catalogs/products/devicegroups/deployments",
            "Microsoft.AzureStack/operations",
            "Microsoft.AzureStack/registrations",
            "Microsoft.AzureStack/registrations/products",
            "Microsoft.AzureStack/registrations/customerSubscriptions",
            "Microsoft.AzureStack/cloudManifestFiles",
            "Microsoft.AzureStack/linkedSubscriptions",
            "Microsoft.AzureStack/generateDeploymentLicense",
            "Microsoft.AzureStackHCI/operations",
            "Microsoft.AzureStackHCI/locations",
            "Microsoft.AzureStackHCI/locations/operationstatuses",
            "Microsoft.AzureStackHCI/galleryImages",
            "Microsoft.AzureStackHCI/networkInterfaces",
            "Microsoft.AzureStackHCI/virtualMachines",
            "Microsoft.AzureStackHCI/virtualNetworks",
            "Microsoft.AzureStackHCI/virtualHardDisks",
            "Microsoft.AzureStackHCI/clusters",
            "Microsoft.AzureStackHCI/clusters/arcSettings",
            "Microsoft.AzureStackHCI/clusters/arcSettings/extensions",
            "Microsoft.AzureStackHCI/virtualMachines/extensions",
            "Microsoft.AzureStackHCI/virtualMachines/hybrididentitymetadata",
            "Microsoft.AzureStackHCI/clusters/publishers",
            "Microsoft.AzureStackHCI/clusters/offers",
            "Microsoft.AzureStackHCI/clusters/publishers/offers",
            "Microsoft.AzureStackHCI/clusters/publishers/offers/skus",
            "Microsoft.AzureStackHCI/marketplaceGalleryImages",
            "Microsoft.AzureStackHCI/storageContainers",
            "Microsoft.AzureStackHCI/clusters/updates",
            "Microsoft.AzureStackHCI/clusters/updates/updateRuns",
            "Microsoft.AzureStackHCI/clusters/updateSummaries",
            "Microsoft.AzureStackHCI/registeredSubscriptions",
            "Microsoft.AzureStackHCI/virtualMachineInstances",
            "Microsoft.AzureStackHCI/clusters/deploymentSettings",
            "Microsoft.AzureStackHCI/edgeDevices",
            "Microsoft.AzureStackHCI/logicalNetworks",
            "Microsoft.AzureStackHCI/clusters/securitySettings",
            "Microsoft.BackupSolutions/VMwareApplications",
            "Microsoft.BackupSolutions/locations",
            "Microsoft.BackupSolutions/locations/operationstatuses",
            "Microsoft.BackupSolutions/operations",
            "Microsoft.BareMetal/bareMetalConnections",
            "Microsoft.BareMetal/operations",
            "Microsoft.BareMetal/locations",
            "Microsoft.BareMetal/locations/operationResults",
            "Microsoft.BareMetal/utilization",
            "Microsoft.BareMetalInfrastructure/bareMetalInstances",
            "Microsoft.BareMetalInfrastructure/bareMetalStorageInstances",
            "Microsoft.BareMetalInfrastructure/locations",
            "Microsoft.BareMetalInfrastructure/locations/operationsStatus",
            "Microsoft.BareMetalInfrastructure/operations",
            "Microsoft.Batch/batchAccounts",
            "Microsoft.Batch/batchAccounts/pools",
            "Microsoft.Batch/batchAccounts/detectors",
            "Microsoft.Batch/batchAccounts/certificates",
            "Microsoft.Batch/batchAccounts/operationResults",
            "Microsoft.Batch/batchAccounts/poolOperationResults",
            "Microsoft.Batch/batchAccounts/certificateOperationResults",
            "Microsoft.Batch/batchAccounts/privateEndpointConnectionProxyResults",
            "Microsoft.Batch/batchAccounts/privateEndpointConnectionResults",
            "Microsoft.Batch/operations",
            "Microsoft.Batch/locations",
            "Microsoft.Batch/locations/quotas",
            "Microsoft.Batch/locations/checkNameAvailability",
            "Microsoft.Batch/locations/accountOperationResults",
            "Microsoft.Batch/locations/virtualMachineSkus",
            "Microsoft.Batch/locations/cloudServiceSkus",
            "Microsoft.Billing/billingPeriods",
            "Microsoft.Billing/invoices",
            "Microsoft.Billing/enrollmentAccounts",
            "Microsoft.Billing/permissionRequests",
            "Microsoft.Billing/billingAccounts/permissionRequests",
            "Microsoft.Billing/billingAccounts/associatedTenants",
            "Microsoft.Billing/billingRoleDefinitions",
            "Microsoft.Billing/billingRoleAssignments",
            "Microsoft.Billing/createBillingRoleAssignment",
            "Microsoft.Billing/billingAccounts/createBillingRoleAssignment",
            "Microsoft.Billing/billingAccounts/signAgreement",
            "Microsoft.Billing/billingAccounts/previewAgreements",
            "Microsoft.Billing/billingAccounts/billingProfiles/createBillingRoleAssignment",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/createBillingRoleAssignment",
            "Microsoft.Billing/billingAccounts/customers/createBillingRoleAssignment",
            "Microsoft.Billing/billingPermissions",
            "Microsoft.Billing/billingAccounts/billingRoleDefinitions",
            "Microsoft.Billing/billingAccounts/billingRoleAssignments",
            "Microsoft.Billing/billingAccounts/billingPermissions",
            "Microsoft.Billing/billingAccounts",
            "Microsoft.Billing/billingAccounts/billingProfilesSummaries",
            "Microsoft.Billing/billingAccounts/billingProfiles/billingRoleDefinitions",
            "Microsoft.Billing/billingAccounts/billingProfiles/billingRoleAssignments",
            "Microsoft.Billing/billingAccounts/billingProfiles/billingPermissions",
            "Microsoft.Billing/billingAccounts/customers",
            "Microsoft.Billing/billingAccounts/billingProfiles/customers",
            "Microsoft.Billing/billingAccounts/billingProfiles/instructions",
            "Microsoft.Billing/billingAccounts/customers/billingSubscriptions",
            "Microsoft.Billing/billingAccounts/customers/products",
            "Microsoft.Billing/billingAccounts/customers/transactions",
            "Microsoft.Billing/billingAccounts/invoiceSections",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/billingRoleDefinitions",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/billingRoleAssignments",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/billingPermissions",
            "Microsoft.Billing/billingAccounts/customers/billingRoleDefinitions",
            "Microsoft.Billing/billingAccounts/billingProfiles/customers/billingRoleDefinitions",
            "Microsoft.Billing/billingAccounts/customers/billingRoleAssignments",
            "Microsoft.Billing/billingAccounts/billingProfiles/customers/billingRoleAssignments",
            "Microsoft.Billing/billingAccounts/customers/billingPermissions",
            "Microsoft.Billing/billingAccounts/billingProfiles/customers/billingPermissions",
            "Microsoft.Billing/billingAccounts/invoiceSections/elevate",
            "Microsoft.Billing/billingAccounts/createInvoiceSectionOperations",
            "Microsoft.Billing/billingAccounts/patchOperations",
            "Microsoft.Billing/billingAccounts/invoiceSections/patchOperations",
            "Microsoft.Billing/billingAccounts/invoiceSections/productMoveOperations",
            "Microsoft.Billing/billingAccounts/invoiceSections/billingSubscriptionMoveOperations",
            "Microsoft.Billing/billingAccounts/listInvoiceSectionsWithCreateSubscriptionPermission",
            "Microsoft.Billing/billingAccounts/billingProfiles",
            "Microsoft.Billing/billingAccounts/BillingProfiles/patchOperations",
            "Microsoft.Billing/departments",
            "Microsoft.Billing/billingAccounts/departments",
            "Microsoft.Billing/billingAccounts/billingProfiles/departments",
            "Microsoft.Billing/billingAccounts/notificationContacts",
            "Microsoft.Billing/billingAccounts/billingProfiles/notificationContacts",
            "Microsoft.Billing/billingAccounts/departments/billingRoleDefinitions",
            "Microsoft.Billing/billingAccounts/billingProfiles/departments/billingRoleDefinitions",
            "Microsoft.Billing/billingAccounts/departments/billingRoleAssignments",
            "Microsoft.Billing/billingAccounts/billingProfiles/departments/billingRoleAssignments",
            "Microsoft.Billing/billingAccounts/departments/billingPermissions",
            "Microsoft.Billing/billingAccounts/billingProfiles/departments/billingPermissions",
            "Microsoft.Billing/billingAccounts/enrollmentAccounts",
            "Microsoft.Billing/billingAccounts/departments/enrollmentAccounts",
            "Microsoft.Billing/billingAccounts/billingProfiles/enrollmentAccounts",
            "Microsoft.Billing/billingAccounts/billingProfiles/departments/enrollmentAccounts",
            "Microsoft.Billing/billingAccounts/enrollmentAccounts/billingRoleDefinitions",
            "Microsoft.Billing/billingAccounts/enrollmentAccounts/billingRoleAssignments",
            "Microsoft.Billing/billingAccounts/enrollmentAccounts/billingPermissions",
            "Microsoft.Billing/billingAccounts/billingProfiles/enrollmentAccounts/billingPermissions",
            "Microsoft.Billing/billingAccounts/enrollmentAccounts/billingSubscriptions",
            "Microsoft.Billing/billingAccounts/departments/billingSubscriptions",
            "Microsoft.Billing/billingAccounts/billingProfiles/paymentMethods",
            "Microsoft.Billing/billingAccounts/availableBalance",
            "Microsoft.Billing/billingAccounts/billingProfiles/availableBalance",
            "Microsoft.Billing/billingAccounts/invoices",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoices",
            "Microsoft.Billing/billingAccounts/transactions",
            "Microsoft.Billing/billingAccounts/billingProfiles/transactions",
            "Microsoft.Billing/billingAccounts/invoiceSections/transactions",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/transactions",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoices/transactions",
            "Microsoft.Billing/billingAccounts/invoices/transactions",
            "Microsoft.Billing/billingAccounts/invoices/summary",
            "Microsoft.Billing/billingAccounts/billingProfiles/validateDeleteBillingProfileEligibility",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/validateDeleteInvoiceSectionEligibility",
            "Microsoft.Billing/billingAccounts/invoices/transactionSummary",
            "Microsoft.Billing/billingAccounts/billingSubscriptions",
            "Microsoft.Billing/billingAccounts/billingSubscriptionAliases",
            "Microsoft.Billing/billingAccounts/billingSubscriptions/invoices",
            "Microsoft.Billing/billingAccounts/billingSubscriptions/policies",
            "Microsoft.Billing/billingAccounts/billingProfiles/billingSubscriptions",
            "Microsoft.Billing/billingAccounts/billingProfiles/departments/billingSubscriptions",
            "Microsoft.Billing/billingAccounts/billingProfiles/enrollmentAccounts/billingSubscriptions",
            "Microsoft.Billing/billingAccounts/invoiceSections/billingSubscriptions",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/billingSubscriptions",
            "Microsoft.Billing/billingAccounts/invoiceSections/products",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/products",
            "Microsoft.Billing/billingAccounts/invoiceSections/products/updateAutoRenew",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/products/updateAutoRenew",
            "Microsoft.Billing/billingAccounts/billingProfiles/products",
            "Microsoft.Billing/billingAccounts/products",
            "Microsoft.Billing/operations",
            "Microsoft.Billing/billingAccounts/invoiceSections/initiateTransfer",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/initiateTransfer",
            "Microsoft.Billing/billingAccounts/invoiceSections/transfers",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/transfers",
            "Microsoft.Billing/transfers/acceptTransfer",
            "Microsoft.Billing/transfers",
            "Microsoft.Billing/transfers/declineTransfer",
            "Microsoft.Billing/transfers/validateTransfer",
            "Microsoft.Billing/billingAccounts/customers/initiateTransfer",
            "Microsoft.Billing/billingAccounts/customers/transfers",
            "Microsoft.Billing/billingAccounts/customers/transferSupportedAccounts",
            "Microsoft.Billing/billingProperty",
            "Microsoft.Billing/policies",
            "Microsoft.Billing/billingAccounts/policies",
            "Microsoft.Billing/billingAccounts/billingProfiles/policies",
            "Microsoft.Billing/billingAccounts/customers/policies",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoices/pricesheet",
            "Microsoft.Billing/billingAccounts/billingProfiles/pricesheet",
            "Microsoft.Billing/billingAccounts/invoiceSections/billingSubscriptions/transfer",
            "Microsoft.Billing/billingAccounts/invoiceSections/products/transfer",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoiceSections/products/transfer",
            "Microsoft.Billing/billingAccounts/invoiceSections/productTransfersResults",
            "Microsoft.Billing/billingAccounts/agreements",
            "Microsoft.Billing/billingAccounts/lineOfCredit",
            "Microsoft.Billing/billingAccounts/paymentMethods",
            "Microsoft.Billing/paymentMethods",
            "Microsoft.Billing/billingAccounts/billingProfiles/paymentMethodLinks",
            "Microsoft.Billing/billingAccounts/payableOverage",
            "Microsoft.Billing/billingAccounts/payNow",
            "Microsoft.Billing/billingAccounts/reservationOrders",
            "Microsoft.Billing/billingAccounts/reservationOrders/reservations",
            "Microsoft.Billing/billingAccounts/reservations",
            "Microsoft.Billing/billingAccounts/billingProfiles/reservations",
            "Microsoft.Billing/billingAccounts/billingProfiles/validateDetachPaymentMethodEligibility",
            "Microsoft.Billing/validateAddress",
            "Microsoft.Billing/promotions",
            "Microsoft.Billing/promotions/checkeligibility",
            "Microsoft.Billing/billingAccounts/billingSubscriptions/elevateRole",
            "Microsoft.Billing/billingAccounts/appliedReservationOrders",
            "Microsoft.Billing/promotionalCredits",
            "Microsoft.Billing/billingAccounts/promotionalCredits",
            "Microsoft.Billing/billingAccounts/savingsPlanOrders/savingsPlans",
            "Microsoft.Billing/billingAccounts/savingsPlanOrders",
            "Microsoft.Billing/billingAccounts/savingsPlans",
            "Microsoft.Billing/billingAccounts/alerts",
            "Microsoft.Billing/billingAccounts/billingProfiles/alerts",
            "Microsoft.Billing/billingAccounts/listProductRecommendations",
            "Microsoft.Billing/billingAccounts/incentiveSchedules",
            "Microsoft.Billing/billingAccounts/incentiveSchedules/milestones",
            "Microsoft.Billing/operationStatus",
            "Microsoft.Billing/transfers/operationStatus",
            "Microsoft.Billing/operationResults",
            "Microsoft.Billing/billingAccounts/operationResults",
            "Microsoft.Billing/billingAccounts/billingProfiles/invoices/operationResults",
            "Microsoft.Billing/billingAccounts/billingProfiles/pricesheetDownloadOperations",
            "Microsoft.Billing/billingAccounts/billingSubscriptions/operationResults",
            "Microsoft.Billing/billingAccounts/billingSubscriptions/invoices/operationResults",
            "Microsoft.Billing/billingAccounts/enrollmentAccounts/activationStatus",
            "Microsoft.Billing/billingAccounts/invoices/operationResults",
            "Microsoft.Billing/promotionalCredits/operationResults",
            "Microsoft.Billing/billingAccounts/addresses",
            "Microsoft.BillingBenefits/savingsPlanOrders",
            "Microsoft.BillingBenefits/savingsPlanOrders/savingsPlans",
            "Microsoft.BillingBenefits/savingsPlanOrders/return",
            "Microsoft.BillingBenefits/validate",
            "Microsoft.BillingBenefits/calculateMigrationCost",
            "Microsoft.BillingBenefits/operationResults",
            "Microsoft.BillingBenefits/operations",
            "Microsoft.BillingBenefits/savingsPlanOrderAliases",
            "Microsoft.BillingBenefits/reservationOrderAliases",
            "Microsoft.BillingBenefits/savingsPlans",
            "Microsoft.BillingBenefits/incentiveSchedules",
            "Microsoft.BillingBenefits/incentiveSchedules/milestones",
            "Microsoft.BillingBenefits/maccs",
            "Microsoft.BillingBenefits/maccs/contributors",
            "Microsoft.BillingBenefits/listSellerResources",
            "Microsoft.BillingBenefits/credits",
            "Microsoft.Bing/locations",
            "Microsoft.Bing/accounts/skus",
            "Microsoft.Bing/accounts/usages",
            "Microsoft.Bing/registeredSubscriptions",
            "Microsoft.Bing/operations",
            "Microsoft.Bing/locations/operationStatuses",
            "Microsoft.Bing/accounts",
            "Microsoft.BlockchainTokens/Operations",
            "Microsoft.Capacity/resourceProviders",
            "Microsoft.Capacity/resourceProviders/locations",
            "Microsoft.Capacity/resourceProviders/locations/serviceLimits",
            "Microsoft.Capacity/resourceProviders/locations/serviceLimitsRequests",
            "Microsoft.Capacity/resources",
            "Microsoft.Capacity/reservationOrders",
            "Microsoft.Capacity/reservationOrders/reservations",
            "Microsoft.Capacity/listbenefits",
            "Microsoft.Capacity/reservations",
            "Microsoft.Capacity/reservationOrders/reservations/revisions",
            "Microsoft.Capacity/operations",
            "Microsoft.Capacity/catalogs",
            "Microsoft.Capacity/appliedReservations",
            "Microsoft.Capacity/checkOffers",
            "Microsoft.Capacity/checkScopes",
            "Microsoft.Capacity/calculatePrice",
            "Microsoft.Capacity/calculateExchange",
            "Microsoft.Capacity/exchange",
            "Microsoft.Capacity/reservationOrders/calculateRefund",
            "Microsoft.Capacity/reservationOrders/return",
            "Microsoft.Capacity/reservationOrders/split",
            "Microsoft.Capacity/reservationOrders/merge",
            "Microsoft.Capacity/reservationOrders/swap",
            "Microsoft.Capacity/reservationOrders/changeDirectory",
            "Microsoft.Capacity/validateReservationOrder",
            "Microsoft.Capacity/reservationOrders/availableScopes",
            "Microsoft.Capacity/reservationOrders/reservations/availableScopes",
            "Microsoft.Capacity/commercialReservationOrders",
            "Microsoft.Capacity/calculatePurchasePrice",
            "Microsoft.Capacity/placePurchaseOrder",
            "Microsoft.Capacity/checkPurchaseStatus",
            "Microsoft.Capacity/ownReservations",
            "Microsoft.Capacity/operationResults",
            "Microsoft.Capacity/listSkus",
            "Microsoft.Capacity/checkBenefitScopes",
            "Microsoft.Carbon/carbonEmissionReports",
            "Microsoft.Carbon/queryCarbonEmissionDataAvailableDateRange",
            "Microsoft.Carbon/operations",
            "Microsoft.CertificateRegistration/certificateOrders",
            "Microsoft.CertificateRegistration/certificateOrders/certificates",
            "Microsoft.CertificateRegistration/validateCertificateRegistrationInformation",
            "Microsoft.CertificateRegistration/operations",
            "Microsoft.Certify/operations",
            "Microsoft.ChangeAnalysis/operations",
            "Microsoft.ChangeAnalysis/resourceChanges",
            "Microsoft.ChangeAnalysis/changes",
            "Microsoft.ChangeAnalysis/changeSnapshots",
            "Microsoft.ChangeAnalysis/computeChanges",
            "Microsoft.Chaos/operations",
            "Microsoft.Chaos/targets",
            "Microsoft.Chaos/locations",
            "Microsoft.Chaos/locations/targetTypes",
            "Microsoft.Chaos/experiments",
            "Microsoft.Chaos/locations/operationStatuses",
            "Microsoft.Chaos/locations/operationResults",
            "Microsoft.Chaos/privateAccesses",
            "Microsoft.ClassicCompute/domainNames",
            "Microsoft.ClassicCompute/domainNames/internalLoadBalancers",
            "Microsoft.ClassicCompute/checkDomainNameAvailability",
            "Microsoft.ClassicCompute/domainNames/slots",
            "Microsoft.ClassicCompute/domainNames/slots/roles",
            "Microsoft.ClassicCompute/domainNames/slots/roles/metricDefinitions",
            "Microsoft.ClassicCompute/domainNames/slots/roles/metrics",
            "Microsoft.ClassicCompute/virtualMachines",
            "Microsoft.ClassicCompute/capabilities",
            "Microsoft.ClassicCompute/domainNames/capabilities",
            "Microsoft.ClassicCompute/domainNames/serviceCertificates",
            "Microsoft.ClassicCompute/quotas",
            "Microsoft.ClassicCompute/virtualMachines/diagnosticSettings",
            "Microsoft.ClassicCompute/virtualMachines/metricDefinitions",
            "Microsoft.ClassicCompute/virtualMachines/metrics",
            "Microsoft.ClassicCompute/operations",
            "Microsoft.ClassicCompute/resourceTypes",
            "Microsoft.ClassicCompute/moveSubscriptionResources",
            "Microsoft.ClassicCompute/validateSubscriptionMoveAvailability",
            "Microsoft.ClassicCompute/operationStatuses",
            "Microsoft.ClassicCompute/operatingSystems",
            "Microsoft.ClassicCompute/operatingSystemFamilies",
            "Microsoft.ClassicInfrastructureMigrate/classicInfrastructureResources",
            "Microsoft.ClassicNetwork/virtualNetworks",
            "Microsoft.ClassicNetwork/virtualNetworks/virtualNetworkPeerings",
            "Microsoft.ClassicNetwork/virtualNetworks/remoteVirtualNetworkPeeringProxies",
            "Microsoft.ClassicNetwork/reservedIps",
            "Microsoft.ClassicNetwork/quotas",
            "Microsoft.ClassicNetwork/gatewaySupportedDevices",
            "Microsoft.ClassicNetwork/operations",
            "Microsoft.ClassicNetwork/networkSecurityGroups",
            "Microsoft.ClassicNetwork/capabilities",
            "Microsoft.ClassicNetwork/expressRouteCrossConnections",
            "Microsoft.ClassicNetwork/expressRouteCrossConnections/peerings",
            "Microsoft.ClassicStorage/storageAccounts",
            "Microsoft.ClassicStorage/quotas",
            "Microsoft.ClassicStorage/checkStorageAccountAvailability",
            "Microsoft.ClassicStorage/storageAccounts/services",
            "Microsoft.ClassicStorage/storageAccounts/services/diagnosticSettings",
            "Microsoft.ClassicStorage/storageAccounts/services/metricDefinitions",
            "Microsoft.ClassicStorage/storageAccounts/services/metrics",
            "Microsoft.ClassicStorage/storageAccounts/metricDefinitions",
            "Microsoft.ClassicStorage/storageAccounts/metrics",
            "Microsoft.ClassicStorage/capabilities",
            "Microsoft.ClassicStorage/storageAccounts/blobServices",
            "Microsoft.ClassicStorage/storageAccounts/tableServices",
            "Microsoft.ClassicStorage/storageAccounts/fileServices",
            "Microsoft.ClassicStorage/storageAccounts/queueServices",
            "Microsoft.ClassicStorage/disks",
            "Microsoft.ClassicStorage/images",
            "Microsoft.ClassicStorage/vmImages",
            "Microsoft.ClassicStorage/storageAccounts/vmImages",
            "Microsoft.ClassicStorage/publicImages",
            "Microsoft.ClassicStorage/osImages",
            "Microsoft.ClassicStorage/osPlatformImages",
            "Microsoft.ClassicStorage/operations",
            "Microsoft.ClassicSubscription/operations",
            "Microsoft.CleanRoom/Locations",
            "Microsoft.CleanRoom/Operations",
            "Microsoft.CleanRoom/Locations/OperationStatuses",
            "Microsoft.CloudHealth/Locations",
            "Microsoft.CloudHealth/Locations/operationstatuses",
            "Microsoft.CloudHealth/Operations",
            "Microsoft.CloudShell/operations",
            "Microsoft.CloudTest/accounts",
            "Microsoft.CloudTest/pools",
            "Microsoft.CloudTest/hostedpools",
            "Microsoft.CloudTest/images",
            "Microsoft.CloudTest/operations",
            "Microsoft.CloudTest/locations",
            "Microsoft.CloudTest/locations/operations",
            "Microsoft.CodeSigning/Locations",
            "Microsoft.CodeSigning/Locations/OperationStatuses",
            "Microsoft.CodeSigning/Operations",
            "Microsoft.CodeSigning/checkNameAvailability",
            "Microsoft.Commerce/UsageAggregates",
            "Microsoft.Commerce/RateCard",
            "Microsoft.Commerce/operations",
            "Microsoft.Communication/Locations",
            "Microsoft.Communication/CommunicationServices",
            "Microsoft.Communication/CommunicationServices/eventGridFilters",
            "Microsoft.Communication/operations",
            "Microsoft.Communication/registeredSubscriptions",
            "Microsoft.Communication/locations/operationStatuses",
            "Microsoft.Communication/CheckNameAvailability",
            "Microsoft.Communication/EmailServices",
            "Microsoft.Communication/EmailServices/Domains",
            "Microsoft.Communication/EmailServices/Domains/SenderUsernames",
            "Microsoft.Community/communityTrainings",
            "Microsoft.Community/Operations",
            "Microsoft.Community/Locations",
            "Microsoft.Community/Locations/OperationStatuses",
            "Microsoft.ComputeSchedule/Locations",
            "Microsoft.ConfidentialLedger/Locations",
            "Microsoft.ConfidentialLedger/Ledgers",
            "Microsoft.ConfidentialLedger/checkNameAvailability",
            "Microsoft.ConfidentialLedger/Locations/operations",
            "Microsoft.ConfidentialLedger/Locations/operationstatuses",
            "Microsoft.ConfidentialLedger/ManagedCCFs",
            "Microsoft.ConfidentialLedger/operations",
            "Microsoft.Confluent/operations",
            "Microsoft.Confluent/locations",
            "Microsoft.Confluent/locations/OperationStatuses",
            "Microsoft.Confluent/organizations",
            "Microsoft.Confluent/checkNameAvailability",
            "Microsoft.Confluent/agreements",
            "Microsoft.Confluent/validations",
            "Microsoft.Confluent/organizations/access",
            "Microsoft.Confluent/organizations/access/deleteRoleBinding",
            "Microsoft.Confluent/organizations/environments",
            "Microsoft.Confluent/organizations/environments/clusters",
            "Microsoft.Confluent/organizations/environments/schemaRegistryClusters",
            "Microsoft.Confluent/organizations/environments/clusters/createAPIKey",
            "Microsoft.Confluent/organizations/apiKeys",
            "Microsoft.Confluent/organizations/listRegions",
            "Microsoft.ConnectedCache/cacheNodes",
            "Microsoft.ConnectedCache/enterpriseCustomers",
            "Microsoft.ConnectedCache/Operations",
            "Microsoft.ConnectedCache/locations",
            "Microsoft.ConnectedCache/locations/operationstatuses",
            "Microsoft.ConnectedCache/ispCustomers",
            "Microsoft.ConnectedCache/ispCustomers/ispCacheNodes",
            "Microsoft.ConnectedCache/enterpriseMccCustomers",
            "Microsoft.ConnectedCache/enterpriseMccCustomers/enterpriseMccCacheNodes",
            "Microsoft.ConnectedCache/registeredSubscriptions",
            "Microsoft.ConnectedCredentials/locations",
            "Microsoft.ConnectedCredentials/locations/operationstatuses",
            "Microsoft.ConnectedCredentials/credentials",
            "Microsoft.ConnectedCredentials/operations",
            "microsoft.connectedopenstack/operations",
            "microsoft.connectedopenstack/locations",
            "microsoft.connectedopenstack/locations/operationStatuses",
            "Microsoft.ConnectedVehicle/locations",
            "Microsoft.ConnectedVehicle/operations",
            "Microsoft.ConnectedVehicle/Locations/OperationStatuses",
            "Microsoft.ConnectedVehicle/checkNameAvailability",
            "Microsoft.ConnectedVehicle/registeredSubscriptions",
            "Microsoft.ConnectedVMwarevSphere/locations",
            "Microsoft.ConnectedVMwarevSphere/locations/operationstatuses",
            "Microsoft.ConnectedVMwarevSphere/VCenters",
            "Microsoft.ConnectedVMwarevSphere/resourcepools",
            "Microsoft.ConnectedVMwarevSphere/virtualnetworks",
            "Microsoft.ConnectedVMwarevSphere/virtualmachinetemplates",
            "Microsoft.ConnectedVMwarevSphere/operations",
            "Microsoft.ConnectedVMwarevSphere/virtualmachines",
            "Microsoft.ConnectedVMwarevSphere/vcenters/inventoryitems",
            "Microsoft.ConnectedVMwarevSphere/virtualmachines/hybrididentitymetadata",
            "Microsoft.ConnectedVMwarevSphere/virtualmachines/extensions",
            "Microsoft.ConnectedVMwarevSphere/virtualmachines/guestagents",
            "Microsoft.ConnectedVMwarevSphere/clusters",
            "Microsoft.ConnectedVMwarevSphere/datastores",
            "Microsoft.ConnectedVMwarevSphere/hosts",
            "Microsoft.ConnectedVMwarevSphere/virtualmachineinstances",
            "Microsoft.CostManagement/Connectors",
            "Microsoft.CostManagement/CloudConnectors",
            "Microsoft.CostManagement/CheckConnectorEligibility",
            "Microsoft.CostManagement/ExternalBillingAccounts",
            "Microsoft.CostManagement/ExternalBillingAccounts/Dimensions",
            "Microsoft.CostManagement/ExternalBillingAccounts/Query",
            "Microsoft.CostManagement/ExternalSubscriptions/Dimensions",
            "Microsoft.CostManagement/ExternalSubscriptions/Query",
            "Microsoft.CostManagement/ExternalSubscriptions",
            "Microsoft.CostManagement/Forecast",
            "Microsoft.CostManagement/ExternalSubscriptions/Forecast",
            "Microsoft.CostManagement/ExternalBillingAccounts/Forecast",
            "Microsoft.CostManagement/Settings",
            "Microsoft.CostManagement/operations",
            "Microsoft.CostManagement/register",
            "Microsoft.CostManagement/Query",
            "Microsoft.CostManagement/Dimensions",
            "Microsoft.CostManagement/Budgets",
            "Microsoft.CostManagement/ExternalSubscriptions/Alerts",
            "Microsoft.CostManagement/ExternalBillingAccounts/Alerts",
            "Microsoft.CostManagement/Alerts",
            "Microsoft.CostManagement/showbackRules",
            "Microsoft.CostManagement/costAllocationRules",
            "Microsoft.CostManagement/Exports",
            "Microsoft.CostManagement/Reports",
            "Microsoft.CostManagement/Reportconfigs",
            "Microsoft.CostManagement/BillingAccounts",
            "Microsoft.CostManagement/Departments",
            "Microsoft.CostManagement/EnrollmentAccounts",
            "Microsoft.CostManagement/Views",
            "Microsoft.CostManagement/Publish",
            "Microsoft.CostManagement/ScheduledActions",
            "Microsoft.CostManagement/CheckNameAvailability",
            "Microsoft.CostManagement/BenefitUtilizationSummaries",
            "Microsoft.CostManagement/BenefitRecommendations",
            "Microsoft.CostManagement/Insights",
            "Microsoft.CostManagement/fetchPrices",
            "Microsoft.CostManagement/fetchMicrosoftPrices",
            "Microsoft.CostManagement/fetchMarketplacePrices",
            "Microsoft.CostManagement/calculatePrice",
            "Microsoft.CostManagement/CalculateCost",
            "Microsoft.CostManagement/GenerateBenefitUtilizationSummariesReport",
            "Microsoft.CostManagement/BenefitUtilizationSummariesOperationResults",
            "Microsoft.CostManagement/GenerateReservationDetailsReport",
            "Microsoft.CostManagement/ReservationDetailsOperationResults",
            "Microsoft.CostManagement/GenerateDetailedCostReport",
            "Microsoft.CostManagement/GenerateCostDetailsReport",
            "Microsoft.CostManagement/CostDetailsOperationResults",
            "Microsoft.CostManagement/OperationStatus",
            "Microsoft.CostManagement/OperationResults",
            "Microsoft.CostManagement/Pricesheets",
            "Microsoft.CostManagement/MarkupRules",
            "Microsoft.CostManagement/StartConversation",
            "Microsoft.CostManagement/SendMessage",
            "Microsoft.CostManagementExports/Operations",
            "Microsoft.CustomerLockbox/operations",
            "Microsoft.CustomerLockbox/TenantOptedIn",
            "Microsoft.CustomerLockbox/EnableLockbox",
            "Microsoft.CustomerLockbox/DisableLockbox",
            "Microsoft.CustomerLockbox/requests",
            "Microsoft.D365CustomerInsights/instances",
            "Microsoft.D365CustomerInsights/operations",
            "Microsoft.Dashboard/locations",
            "Microsoft.Dashboard/checkNameAvailability",
            "Microsoft.Dashboard/locations/operationStatuses",
            "Microsoft.Dashboard/grafana",
            "Microsoft.Dashboard/operations",
            "Microsoft.Dashboard/grafana/privateEndpointConnections",
            "Microsoft.Dashboard/grafana/privateLinkResources",
            "Microsoft.Dashboard/locations/checkNameAvailability",
            "Microsoft.Dashboard/grafana/managedPrivateEndpoints",
            "Microsoft.DatabaseWatcher/locations",
            "Microsoft.DatabaseWatcher/operations",
            "Microsoft.DataBox/jobs",
            "Microsoft.DataBox/locations",
            "Microsoft.DataBox/locations/validateAddress",
            "Microsoft.DataBox/locations/checkNameAvailability",
            "Microsoft.DataBox/locations/operationresults",
            "Microsoft.DataBox/operations",
            "Microsoft.DataBox/locations/availableSkus",
            "Microsoft.DataBox/locations/validateInputs",
            "Microsoft.DataBox/locations/regionConfiguration",
            "Microsoft.DataBox/jobs/eventGridFilters",
            "Microsoft.DataBoxEdge/DataBoxEdgeDevices",
            "Microsoft.DataBoxEdge/DataBoxEdgeDevices/checkNameAvailability",
            "Microsoft.DataBoxEdge/operations",
            "Microsoft.DataBoxEdge/availableSkus",
            "Microsoft.DataCatalog/catalogs",
            "Microsoft.DataCatalog/checkNameAvailability",
            "Microsoft.DataCatalog/operations",
            "Microsoft.DataCatalog/locations",
            "Microsoft.DataCatalog/locations/jobs",
            "Microsoft.Datadog/registeredSubscriptions",
            "Microsoft.Datadog/locations",
            "Microsoft.Datadog/locations/operationStatuses",
            "Microsoft.Datadog/operations",
            "Microsoft.Datadog/monitors",
            "Microsoft.Datadog/monitors/tagRules",
            "Microsoft.Datadog/monitors/listMonitoredResources",
            "Microsoft.Datadog/monitors/listApiKeys",
            "Microsoft.Datadog/monitors/getDefaultKey",
            "Microsoft.Datadog/monitors/setDefaultKey",
            "Microsoft.Datadog/monitors/singleSignOnConfigurations",
            "Microsoft.Datadog/monitors/listHosts",
            "Microsoft.Datadog/monitors/listLinkedResources",
            "Microsoft.Datadog/monitors/refreshSetPasswordLink",
            "Microsoft.Datadog/agreements",
            "Microsoft.Datadog/monitors/monitoredSubscriptions",
            "Microsoft.Datadog/subscriptionStatuses",
            "Microsoft.DataFactory/factories",
            "Microsoft.DataFactory/factories/integrationRuntimes",
            "Microsoft.DataFactory/factories/privateEndpointConnectionProxies",
            "Microsoft.DataFactory/CheckNameAvailability",
            "Microsoft.DataFactory/operations",
            "Microsoft.DataFactory/locations",
            "Microsoft.DataFactory/locations/configureFactoryRepo",
            "Microsoft.DataFactory/locations/getFeatureValue",
            "Microsoft.DataReplication/replicationVaults",
            "Microsoft.DataReplication/replicationFabrics",
            "Microsoft.DataReplication/operations",
            "Microsoft.DataShare/accounts",
            "Microsoft.DataShare/accounts/shares",
            "Microsoft.DataShare/accounts/shares/datasets",
            "Microsoft.DataShare/accounts/shares/synchronizationSettings",
            "Microsoft.DataShare/accounts/shares/invitations",
            "Microsoft.DataShare/accounts/sharesubscriptions",
            "Microsoft.DataShare/accounts/shares/providersharesubscriptions",
            "Microsoft.DataShare/accounts/sharesubscriptions/datasetmappings",
            "Microsoft.DataShare/accounts/sharesubscriptions/triggers",
            "Microsoft.DataShare/accounts/sharesubscriptions/consumerSourceDataSets",
            "Microsoft.DataShare/listinvitations",
            "Microsoft.DataShare/locations",
            "Microsoft.DataShare/locations/operationResults",
            "Microsoft.DataShare/locations/registerEmail",
            "Microsoft.DataShare/locations/activateEmail",
            "Microsoft.DataShare/locations/rejectInvitation",
            "Microsoft.DataShare/locations/consumerInvitations",
            "Microsoft.DataShare/operations",
            "Microsoft.DelegatedNetwork/operations",
            "Microsoft.DevAI/Locations",
            "Microsoft.DevAI/Locations/operationstatuses",
            "Microsoft.DevAI/instances",
            "Microsoft.DevAI/instances/experiments",
            "Microsoft.DevAI/instances/sandboxes",
            "Microsoft.DevAI/instances/sandboxes/experiments",
            "Microsoft.DevAI/Operations",
            "Microsoft.DevAI/registeredSubscriptions",
            "Microsoft.DevCenter/operations",
            "Microsoft.DevCenter/Locations",
            "Microsoft.DevCenter/Locations/OperationStatuses",
            "Microsoft.DevCenter/devcenters",
            "Microsoft.DevCenter/devcenters/catalogs",
            "Microsoft.DevCenter/devcenters/attachednetworks",
            "Microsoft.DevCenter/devcenters/devboxdefinitions",
            "Microsoft.DevCenter/devcenters/environmentTypes",
            "Microsoft.DevCenter/devcenters/galleries",
            "Microsoft.DevCenter/devcenters/galleries/images/versions",
            "Microsoft.DevCenter/devcenters/galleries/images",
            "Microsoft.DevCenter/devcenters/images",
            "Microsoft.DevCenter/networkconnections",
            "Microsoft.DevCenter/networkconnections/healthchecks",
            "Microsoft.DevCenter/projects",
            "Microsoft.DevCenter/projects/attachednetworks",
            "Microsoft.DevCenter/projects/environmentTypes",
            "Microsoft.DevCenter/projects/pools",
            "Microsoft.DevCenter/projects/pools/schedules",
            "Microsoft.DevCenter/projects/devboxdefinitions",
            "Microsoft.DevCenter/projects/allowedEnvironmentTypes",
            "Microsoft.DevCenter/checkNameAvailability",
            "Microsoft.DevCenter/networkconnections/outboundNetworkDependenciesEndpoints",
            "Microsoft.DevCenter/Locations/usages",
            "Microsoft.DevCenter/devcenters/catalogs/devboxdefinitions",
            "Microsoft.DevCenter/devcenters/catalogs/environmentDefinitions",
            "Microsoft.DevCenter/devcenters/catalogs/tasks",
            "Microsoft.DevCenter/checkScopedNameAvailability",
            "Microsoft.DevelopmentWindows365/DevelopmentCloudPcDelegatedMsis",
            "Microsoft.DevHub/operations",
            "Microsoft.DevHub/workflows",
            "Microsoft.DevHub/locations",
            "Microsoft.DevHub/locations/githuboauth",
            "Microsoft.DevHub/locations/generatePreviewArtifacts",
            "Microsoft.DeviceRegistry/locations",
            "Microsoft.DeviceRegistry/operations",
            "Microsoft.DeviceRegistry/operationStatuses",
            "Microsoft.DeviceRegistry/locations/operationStatuses",
            "Microsoft.DeviceRegistry/assets",
            "Microsoft.DeviceRegistry/assetEndpointProfiles",
            "Microsoft.DeviceUpdate/locations",
            "Microsoft.DeviceUpdate/locations/operationStatuses",
            "Microsoft.DeviceUpdate/operations",
            "Microsoft.DeviceUpdate/accounts",
            "Microsoft.DeviceUpdate/accounts/instances",
            "Microsoft.DeviceUpdate/checkNameAvailability",
            "Microsoft.DeviceUpdate/registeredSubscriptions",
            "Microsoft.DeviceUpdate/accounts/privateLinkResources",
            "Microsoft.DeviceUpdate/accounts/privateEndpointConnections",
            "Microsoft.DeviceUpdate/accounts/privateEndpointConnectionProxies",
            "Microsoft.DigitalTwins/locations",
            "Microsoft.DigitalTwins/locations/checkNameAvailability",
            "Microsoft.DigitalTwins/digitalTwinsInstances",
            "Microsoft.DigitalTwins/digitalTwinsInstances/operationResults",
            "Microsoft.DigitalTwins/locations/operationResults",
            "Microsoft.DigitalTwins/locations/operationsStatuses",
            "Microsoft.DigitalTwins/digitalTwinsInstances/endpoints",
            "Microsoft.DigitalTwins/digitalTwinsInstances/timeSeriesDatabaseConnections",
            "Microsoft.DigitalTwins/operations",
            "Microsoft.DomainRegistration/domains",
            "Microsoft.DomainRegistration/domains/domainOwnershipIdentifiers",
            "Microsoft.DomainRegistration/topLevelDomains",
            "Microsoft.DomainRegistration/checkDomainAvailability",
            "Microsoft.DomainRegistration/listDomainRecommendations",
            "Microsoft.DomainRegistration/validateDomainRegistrationInformation",
            "Microsoft.DomainRegistration/generateSsoRequest",
            "Microsoft.DomainRegistration/operations",
            "Microsoft.Easm/workspaces",
            "Microsoft.Easm/workspaces/labels",
            "Microsoft.Easm/operations",
            "Microsoft.Easm/workspaces/tasks",
            "Microsoft.EdgeManagement/locations",
            "Microsoft.EdgeManagement/operations",
            "Microsoft.EdgeMarketplace/operations",
            "Microsoft.EdgeMarketplace/locations",
            "Microsoft.EdgeMarketplace/locations/operationStatuses",
            "Microsoft.EdgeMarketplace/publishers",
            "Microsoft.EdgeMarketplace/offers",
            "Microsoft.EdgeOrder/addresses",
            "Microsoft.EdgeOrder/orderItems",
            "Microsoft.EdgeOrder/orders",
            "Microsoft.EdgeOrder/locations",
            "Microsoft.EdgeOrder/locations/orders",
            "Microsoft.EdgeOrder/listProductFamilies",
            "Microsoft.EdgeOrder/listConfigurations",
            "Microsoft.EdgeOrder/productFamiliesMetadata",
            "Microsoft.EdgeOrder/locations/hciCatalog",
            "Microsoft.EdgeOrder/locations/hciCatalog/vendors",
            "Microsoft.EdgeOrder/locations/hciCatalog/platforms",
            "Microsoft.EdgeOrder/locations/hciCatalog/projects",
            "Microsoft.EdgeOrder/locations/hciFlightCatalog",
            "Microsoft.EdgeOrder/locations/hciFlightCatalog/vendors",
            "Microsoft.EdgeOrder/locations/hciFlightCatalog/platforms",
            "Microsoft.EdgeOrder/locations/hciFlightCatalog/projects",
            "Microsoft.EdgeOrder/operations",
            "Microsoft.EdgeOrder/locations/operationresults",
            "Microsoft.EdgeOrderPartner/operations",
            "Microsoft.Elastic/operations",
            "Microsoft.Elastic/locations",
            "Microsoft.Elastic/locations/operationStatuses",
            "Microsoft.Elastic/monitors",
            "Microsoft.Elastic/monitors/tagRules",
            "Microsoft.Elastic/checkNameAvailability",
            "Microsoft.Elastic/elasticVersions",
            "Microsoft.Elastic/getOrganizationApiKey",
            "Microsoft.Elastic/getElasticOrganizationToAzureSubscriptionMapping",
            "Microsoft.ElasticSan/elasticSans",
            "Microsoft.ElasticSan/elasticSans/volumeGroups",
            "Microsoft.ElasticSan/operations",
            "Microsoft.ElasticSan/locations/asyncoperations",
            "Microsoft.ElasticSan/locations",
            "Microsoft.EnterpriseSupport/EnterpriseSupports",
            "Microsoft.EnterpriseSupport/operationStatuses",
            "Microsoft.EnterpriseSupport/validate",
            "Microsoft.EnterpriseSupport/Operations",
            "Microsoft.EntitlementManagement/Operations",
            "Microsoft.Experimentation/Operations",
            "Microsoft.ExtendedLocation/locations",
            "Microsoft.ExtendedLocation/customLocations",
            "Microsoft.ExtendedLocation/customLocations/enabledResourceTypes",
            "Microsoft.ExtendedLocation/customLocations/resourceSyncRules",
            "Microsoft.ExtendedLocation/locations/operationsstatus",
            "Microsoft.ExtendedLocation/locations/operationresults",
            "Microsoft.ExtendedLocation/operations",
            "Microsoft.Fabric/capacities",
            "Microsoft.Fabric/locations",
            "Microsoft.Fabric/locations/checkNameAvailability",
            "Microsoft.Fabric/locations/operationresults",
            "Microsoft.Fabric/locations/operationstatuses",
            "Microsoft.Fabric/operations",
            "Microsoft.Falcon/namespaces",
            "Microsoft.Features/features",
            "Microsoft.Features/providers",
            "Microsoft.Features/featureProviders",
            "Microsoft.Features/subscriptionFeatureRegistrations",
            "Microsoft.Features/featureProviderNamespaces",
            "Microsoft.Features/featureConfigurations",
            "Microsoft.Features/operations",
            "Microsoft.FluidRelay/fluidRelayServers",
            "Microsoft.FluidRelay/Operations",
            "Microsoft.FluidRelay/fluidRelayServers/fluidRelayContainers",
            "Microsoft.FluidRelay/Locations",
            "Microsoft.FluidRelay/Locations/OperationStatuses",
            "Microsoft.GraphServices/accounts",
            "Microsoft.GraphServices/Operations",
            "Microsoft.GraphServices/RegisteredSubscriptions",
            "Microsoft.GraphServices/Locations",
            "Microsoft.GraphServices/Locations/OperationStatuses",
            "Microsoft.HanaOnAzure/hanaInstances",
            "Microsoft.HanaOnAzure/locations/operationsStatus",
            "Microsoft.HanaOnAzure/locations",
            "Microsoft.HanaOnAzure/locations/operations",
            "Microsoft.HanaOnAzure/operations",
            "Microsoft.HardwareSecurityModules/cloudHsmClusters",
            "Microsoft.HardwareSecurityModules/locations",
            "Microsoft.HardwareSecurityModules/operations",
            "Microsoft.HealthBot/Operations",
            "Microsoft.HealthBot/Locations",
            "Microsoft.HealthBot/Locations/OperationStatuses",
            "Microsoft.HealthBot/healthBots",
            "Microsoft.HealthDataAIServices/locations",
            "Microsoft.HealthDataAIServices/locations/operationStatuses",
            "Microsoft.HealthDataAIServices/Operations",
            "Microsoft.HealthModel/Operations",
            "Microsoft.Help/operations",
            "Microsoft.Help/operationResults",
            "Microsoft.Help/discoverySolutions",
            "Microsoft.Help/discoverSolutions",
            "Microsoft.Help/diagnostics",
            "Microsoft.Help/checkNameAvailability",
            "Microsoft.Help/solutions",
            "Microsoft.Help/troubleshooters",
            "Microsoft.Help/SelfHelp",
            "Microsoft.HybridCloud/cloudConnectors",
            "Microsoft.HybridCloud/cloudConnections",
            "Microsoft.HybridCompute/machines",
            "Microsoft.HybridCompute/machines/hybridIdentityMetadata",
            "Microsoft.HybridCompute/machines/privateLinkScopes",
            "Microsoft.HybridCompute/machines/extensions",
            "Microsoft.HybridCompute/locations",
            "Microsoft.HybridCompute/locations/publishers",
            "Microsoft.HybridCompute/locations/publishers/extensionTypes",
            "Microsoft.HybridCompute/locations/publishers/extensionTypes/versions",
            "Microsoft.HybridCompute/locations/operationStatus",
            "Microsoft.HybridCompute/locations/operationResults",
            "Microsoft.HybridCompute/operations",
            "Microsoft.HybridCompute/machines/assessPatches",
            "Microsoft.HybridCompute/machines/installPatches",
            "Microsoft.HybridCompute/locations/updateCenterOperationResults",
            "Microsoft.HybridCompute/privateLinkScopes",
            "Microsoft.HybridCompute/privateLinkScopes/privateEndpointConnections",
            "Microsoft.HybridCompute/privateLinkScopes/privateEndpointConnectionProxies",
            "Microsoft.HybridCompute/locations/privateLinkScopes",
            "Microsoft.HybridCompute/osType",
            "Microsoft.HybridCompute/osType/agentVersions",
            "Microsoft.HybridCompute/osType/agentVersions/latest",
            "Microsoft.HybridCompute/machines/runcommands",
            "Microsoft.HybridCompute/machines/licenseProfiles",
            "Microsoft.HybridCompute/licenses",
            "Microsoft.HybridCompute/validateLicense",
            "Microsoft.HybridCompute/networkConfigurations",
            "Microsoft.HybridCompute/privateLinkScopes/networkSecurityPerimeterConfigurations",
            "Microsoft.HybridCompute/privateLinkScopes/networkSecurityPerimeterAssociationProxies",
            "Microsoft.HybridCompute/locations/notifyNetworkSecurityPerimeterUpdatesAvailable",
            "Microsoft.HybridCompute/locations/notifyExtension",
            "Microsoft.HybridConnectivity/endpoints",
            "Microsoft.HybridConnectivity/Operations",
            "Microsoft.HybridConnectivity/Locations",
            "Microsoft.HybridConnectivity/Locations/OperationStatuses",
            "Microsoft.HybridContainerService/Locations",
            "Microsoft.HybridContainerService/Locations/operationStatuses",
            "Microsoft.HybridContainerService/provisionedClusters",
            "Microsoft.HybridContainerService/provisionedClusters/hybridIdentityMetadata",
            "Microsoft.HybridContainerService/provisionedClusters/agentPools",
            "Microsoft.HybridContainerService/virtualNetworks",
            "Microsoft.HybridContainerService/Operations",
            "Microsoft.HybridContainerService/provisionedClusters/upgradeProfiles",
            "Microsoft.HybridContainerService/kubernetesVersions",
            "Microsoft.HybridContainerService/skus",
            "Microsoft.HybridContainerService/provisionedClusterInstances",
            "Microsoft.HybridNetwork/Operations",
            "Microsoft.HybridNetwork/Locations",
            "Microsoft.HybridNetwork/Locations/OperationStatuses",
            "Microsoft.HybridNetwork/devices",
            "Microsoft.HybridNetwork/networkfunctions",
            "Microsoft.HybridNetwork/networkFunctionVendors",
            "Microsoft.HybridNetwork/networkFunctions/components",
            "Microsoft.HybridNetwork/sites",
            "Microsoft.HybridNetwork/siteNetworkServices",
            "Microsoft.HybridNetwork/configurationGroupValues",
            "Microsoft.HybridNetwork/publishers",
            "Microsoft.HybridNetwork/publishers/networkFunctionDefinitionGroups",
            "Microsoft.HybridNetwork/publishers/networkFunctionDefinitionGroups/networkFunctionDefinitionVersions",
            "Microsoft.HybridNetwork/publishers/artifactStores",
            "Microsoft.HybridNetwork/publishers/artifactStores/artifactManifests",
            "Microsoft.HybridNetwork/publishers/artifactstores/artifacts",
            "Microsoft.HybridNetwork/publishers/artifactstores/artifactversions",
            "Microsoft.Impact/Operations",
            "Microsoft.IntegrationSpaces/Spaces",
            "Microsoft.IntegrationSpaces/Spaces/InfrastructureResources",
            "Microsoft.IntegrationSpaces/Spaces/Applications",
            "Microsoft.IntegrationSpaces/Spaces/applications/resources",
            "Microsoft.IntegrationSpaces/Spaces/applications/BusinessProcesses",
            "Microsoft.IntegrationSpaces/Spaces/applications/BusinessProcesses/versions",
            "Microsoft.IntegrationSpaces/locations",
            "Microsoft.IntegrationSpaces/locations/OperationStatuses",
            "Microsoft.IntegrationSpaces/operations",
            "Microsoft.IoTCentral/IoTApps",
            "Microsoft.IoTCentral/checkNameAvailability",
            "Microsoft.IoTCentral/checkSubdomainAvailability",
            "Microsoft.IoTCentral/operations",
            "Microsoft.IoTCentral/locations",
            "Microsoft.IoTCentral/locations/operationResults",
            "Microsoft.IoTCentral/appTemplates",
            "Microsoft.IoTFirmwareDefense/operations",
            "Microsoft.IoTFirmwareDefense/workspaces",
            "Microsoft.IoTFirmwareDefense/workspaces/firmwares",
            "Microsoft.IoTFirmwareDefense/workspaces/firmwares/sbomComponents",
            "Microsoft.IoTFirmwareDefense/workspaces/firmwares/binaryHardeningResults",
            "Microsoft.IoTFirmwareDefense/workspaces/firmwares/cryptoCertificates",
            "Microsoft.IoTFirmwareDefense/workspaces/firmwares/cryptoKeys",
            "Microsoft.IoTFirmwareDefense/workspaces/firmwares/passwordHashes",
            "Microsoft.IoTFirmwareDefense/workspaces/firmwares/cves",
            "Microsoft.IoTFirmwareDefense/workspaces/firmwares/summaries",
            "Microsoft.IoTFirmwareDefense/locations",
            "Microsoft.IoTFirmwareDefense/locations/operationStatuses",
            "Microsoft.IoTOperationsDataProcessor/locations",
            "Microsoft.IoTOperationsDataProcessor/locations/operationStatuses",
            "Microsoft.IoTOperationsDataProcessor/instances",
            "Microsoft.IoTOperationsDataProcessor/instances/datasets",
            "Microsoft.IoTOperationsDataProcessor/instances/pipelines",
            "Microsoft.IoTOperationsDataProcessor/operations",
            "Microsoft.IoTOperationsMQ/Locations",
            "Microsoft.IoTOperationsMQ/Operations",
            "Microsoft.IoTOperationsMQ/Locations/OperationStatuses",
            "Microsoft.IoTOperationsMQ/mq",
            "Microsoft.IoTOperationsMQ/mq/broker",
            "Microsoft.IoTOperationsMQ/mq/broker/authentication",
            "Microsoft.IoTOperationsMQ/mq/broker/authorization",
            "Microsoft.IoTOperationsMQ/mq/broker/listener",
            "Microsoft.IoTOperationsMQ/mq/dataLakeConnector",
            "Microsoft.IoTOperationsMQ/mq/dataLakeConnector/topicMap",
            "Microsoft.IoTOperationsMQ/mq/diagnosticService",
            "Microsoft.IoTOperationsMQ/mq/kafkaConnector",
            "Microsoft.IoTOperationsMQ/mq/kafkaConnector/topicMap",
            "Microsoft.IoTOperationsMQ/mq/mqttBridgeConnector",
            "Microsoft.IoTOperationsMQ/mq/mqttBridgeConnector/topicMap",
            "Microsoft.IoTOperationsOrchestrator/locations",
            "Microsoft.IoTOperationsOrchestrator/locations/operationStatuses",
            "Microsoft.IoTOperationsOrchestrator/targets",
            "Microsoft.IoTOperationsOrchestrator/solutions",
            "Microsoft.IoTOperationsOrchestrator/instances",
            "Microsoft.IoTOperationsOrchestrator/operations",
            "Microsoft.IoTSecurity/Operations",
            "Microsoft.IoTSecurity/defenderSettings",
            "Microsoft.IoTSecurity/locations",
            "Microsoft.IoTSecurity/locations/deviceGroups",
            "Microsoft.IoTSecurity/locations/deviceGroups/devices",
            "Microsoft.IoTSecurity/locations/endpoints",
            "Microsoft.IoTSecurity/locations/deviceGroups/vulnerabilities",
            "Microsoft.IoTSecurity/locations/deviceGroups/alerts",
            "Microsoft.IoTSecurity/locations/deviceGroups/alerts/pcaps",
            "Microsoft.IoTSecurity/locations/deviceGroups/alerts/learn",
            "Microsoft.IoTSecurity/locations/deviceGroups/recommendations",
            "Microsoft.IoTSecurity/locations/sites",
            "Microsoft.IoTSecurity/locations/sites/sensors",
            "Microsoft.IoTSecurity/sites",
            "Microsoft.IoTSecurity/sensors",
            "Microsoft.IoTSecurity/onPremiseSensors",
            "Microsoft.IoTSecurity/alertTypes",
            "Microsoft.IoTSecurity/recommendationTypes",
            "Microsoft.IoTSecurity/licenseSkus",
            "Microsoft.Kubernetes/connectedClusters",
            "Microsoft.Kubernetes/locations",
            "Microsoft.Kubernetes/locations/operationStatuses",
            "Microsoft.Kubernetes/registeredSubscriptions",
            "Microsoft.Kubernetes/Operations",
            "Microsoft.KubernetesConfiguration/sourceControlConfigurations",
            "Microsoft.KubernetesConfiguration/extensions",
            "Microsoft.KubernetesConfiguration/fluxConfigurations",
            "Microsoft.KubernetesConfiguration/operations",
            "Microsoft.KubernetesConfiguration/extensionTypes",
            "Microsoft.KubernetesConfiguration/locations/extensionTypes",
            "Microsoft.KubernetesConfiguration/locations/extensionTypes/versions",
            "Microsoft.KubernetesConfiguration/privateLinkScopes",
            "Microsoft.KubernetesConfiguration/privateLinkScopes/privateEndpointConnections",
            "Microsoft.KubernetesConfiguration/privateLinkScopes/privateEndpointConnectionProxies",
            "Microsoft.KubernetesRuntime/storageClasses",
            "Microsoft.KubernetesRuntime/loadBalancers",
            "Microsoft.KubernetesRuntime/bgpPeers",
            "Microsoft.KubernetesRuntime/operations",
            "Microsoft.KubernetesRuntime/locations",
            "Microsoft.KubernetesRuntime/locations/operationStatuses",
            "Microsoft.KubernetesRuntime/services",
            "Microsoft.LabServices/labplans",
            "Microsoft.LabServices/labs",
            "Microsoft.LabServices/labaccounts",
            "Microsoft.LabServices/locations/operationResults",
            "Microsoft.LabServices/locations/operations",
            "Microsoft.LabServices/operations",
            "Microsoft.LabServices/users",
            "Microsoft.LabServices/locations",
            "Microsoft.LabServices/locations/usages",
            "Microsoft.LoadTestService/operations",
            "Microsoft.LoadTestService/checkNameAvailability",
            "Microsoft.LoadTestService/loadtests",
            "Microsoft.LoadTestService/Locations",
            "Microsoft.LoadTestService/Locations/OperationStatuses",
            "Microsoft.LoadTestService/registeredSubscriptions",
            "Microsoft.LoadTestService/loadtests/outboundNetworkDependenciesEndpoints",
            "Microsoft.LoadTestService/Locations/Quotas",
            "Microsoft.Logz/operations",
            "Microsoft.Logz/locations",
            "Microsoft.Logz/registeredSubscriptions",
            "Microsoft.Logz/locations/operationStatuses",
            "Microsoft.Logz/monitors",
            "Microsoft.Logz/monitors/tagRules",
            "Microsoft.Logz/monitors/singleSignOnConfigurations",
            "Microsoft.Logz/monitors/accounts",
            "Microsoft.Logz/monitors/accounts/tagRules",
            "Microsoft.MachineLearning/Workspaces",
            "Microsoft.MachineLearning/webServices",
            "Microsoft.MachineLearning/operations",
            "Microsoft.MachineLearning/locations",
            "Microsoft.MachineLearning/locations/operations",
            "Microsoft.MachineLearning/locations/operationsStatus",
            "Microsoft.MachineLearning/commitmentPlans",
            "Microsoft.ManagedNetworkFabric/Operations",
            "Microsoft.ManagedNetworkFabric/NetworkFabricControllers",
            "Microsoft.ManagedNetworkFabric/Locations",
            "Microsoft.ManagedNetworkFabric/Locations/OperationStatuses",
            "Microsoft.ManagedNetworkFabric/NetworkFabrics",
            "Microsoft.ManagedNetworkFabric/NetworkRacks",
            "Microsoft.ManagedNetworkFabric/NetworkDevices",
            "Microsoft.ManagedNetworkFabric/NetworkDevices/NetworkInterfaces",
            "Microsoft.ManagedNetworkFabric/L2IsolationDomains",
            "Microsoft.ManagedNetworkFabric/L3IsolationDomains",
            "Microsoft.ManagedNetworkFabric/accesscontrollists",
            "Microsoft.ManagedNetworkFabric/RoutePolicies",
            "Microsoft.ManagedNetworkFabric/L3IsolationDomains/externalNetworks",
            "Microsoft.ManagedNetworkFabric/L3IsolationDomains/internalNetworks",
            "Microsoft.ManagedNetworkFabric/NetworkFabrics/NetworkToNetworkInterconnects",
            "Microsoft.ManagedNetworkFabric/IpExtendedCommunities",
            "Microsoft.ManagedNetworkFabric/IpCommunities",
            "Microsoft.ManagedNetworkFabric/IpPrefixes",
            "Microsoft.ManagedNetworkFabric/InternetGateways",
            "Microsoft.ManagedNetworkFabric/internetgatewayrules",
            "Microsoft.ManagedNetworkFabric/networkpacketbrokers",
            "Microsoft.ManagedNetworkFabric/networktaps",
            "Microsoft.ManagedNetworkFabric/networktaprules",
            "Microsoft.ManagedNetworkFabric/neighborgroups",
            "Microsoft.ManufacturingPlatform/locations",
            "Microsoft.ManufacturingPlatform/operations",
            "Microsoft.Marketplace/register",
            "Microsoft.Marketplace/privategalleryitems",
            "Microsoft.Marketplace/products",
            "Microsoft.Marketplace/offers",
            "Microsoft.Marketplace/macc",
            "Microsoft.Marketplace/offerTypes",
            "Microsoft.Marketplace/offerTypes/publishers",
            "Microsoft.Marketplace/offerTypes/publishers/offers",
            "Microsoft.Marketplace/offerTypes/publishers/offers/plans",
            "Microsoft.Marketplace/offerTypes/publishers/offers/plans/configs",
            "Microsoft.Marketplace/offerTypes/publishers/offers/plans/configs/importImage",
            "Microsoft.Marketplace/offerTypes/publishers/offers/plans/agreements",
            "Microsoft.Marketplace/operations",
            "Microsoft.Marketplace/listAvailableOffers",
            "Microsoft.Marketplace/publishers",
            "Microsoft.Marketplace/publishers/offers",
            "Microsoft.Marketplace/publishers/offers/amendments",
            "Microsoft.Marketplace/privateStoreClient",
            "Microsoft.Marketplace/privateStores",
            "Microsoft.Marketplace/privateStores/offers",
            "Microsoft.Marketplace/search",
            "Microsoft.Marketplace/privateStores/requestApprovals/query",
            "Microsoft.Marketplace/privateStores/requestApprovals/withdrawPlan",
            "Microsoft.Marketplace/privateStores/RequestApprovals",
            "Microsoft.Marketplace/privateStores/queryNotificationsState",
            "Microsoft.Marketplace/privateStores/fetchAllSubscriptionsInTenant",
            "Microsoft.Marketplace/privateStores/listNewPlansNotifications",
            "Microsoft.Marketplace/privateStores/listStopSellOffersPlansNotifications",
            "Microsoft.Marketplace/privateStores/listSubscriptionsContext",
            "Microsoft.Marketplace/privateStores/offers/acknowledgeNotification",
            "Microsoft.Marketplace/privateStores/AdminRequestApprovals",
            "Microsoft.Marketplace/privateStores/collections",
            "Microsoft.Marketplace/privateStores/collections/approveAllItems",
            "Microsoft.Marketplace/privateStores/collections/disableApproveAllItems",
            "Microsoft.Marketplace/privateStores/collections/offers",
            "Microsoft.Marketplace/privateStores/collections/mapOffersToContexts",
            "Microsoft.Marketplace/privateStores/collections/queryRules",
            "Microsoft.Marketplace/privateStores/collections/setRules",
            "Microsoft.Marketplace/privateStores/collections/offers/upsertOfferWithMultiContext",
            "Microsoft.Marketplace/privateStores/bulkCollectionsAction",
            "Microsoft.Marketplace/privateStores/collections/transferOffers",
            "Microsoft.Marketplace/privateStores/anyExistingOffersInTheCollections",
            "Microsoft.Marketplace/privateStores/queryOffers",
            "Microsoft.Marketplace/privateStores/queryUserOffers",
            "Microsoft.Marketplace/privateStores/queryUserRules",
            "Microsoft.Marketplace/privateStores/collectionsToSubscriptionsMapping",
            "Microsoft.Marketplace/privateStores/billingAccounts",
            "Microsoft.Marketplace/privateStores/queryApprovedPlans",
            "Microsoft.Marketplace/locations",
            "Microsoft.Marketplace/locations/edgeZones",
            "Microsoft.Marketplace/locations/edgeZones/products",
            "Microsoft.Marketplace/mysolutions",
            "Microsoft.Marketplace/products/reviews",
            "Microsoft.Marketplace/products/reviews/comments",
            "Microsoft.Marketplace/products/reviews/helpful",
            "Microsoft.Marketplace/products/usermetadata",
            "Microsoft.MarketplaceOrdering/agreements",
            "Microsoft.MarketplaceOrdering/operations",
            "Microsoft.MarketplaceOrdering/offertypes",
            "Microsoft.Migrate/migrateprojects",
            "Microsoft.Migrate/assessmentProjects",
            "Microsoft.Migrate/moveCollections",
            "Microsoft.Migrate/operations",
            "Microsoft.Migrate/locations",
            "Microsoft.Migrate/locations/rmsOperationResults",
            "Microsoft.Migrate/modernizeProjects",
            "Microsoft.Mission/Locations",
            "Microsoft.Mission/Locations/OperationStatuses",
            "Microsoft.Mission/Operations",
            "Microsoft.Mission/virtualEnclaves/endpoints",
            "Microsoft.Mission/checkNameAvailability",
            "Microsoft.MobileNetwork/Locations",
            "Microsoft.MobileNetwork/Locations/OperationStatuses",
            "Microsoft.MobileNetwork/Operations",
            "Microsoft.MobileNetwork/packetCoreControlPlaneVersions",
            "Microsoft.MobilePacketCore/Locations",
            "Microsoft.MobilePacketCore/Locations/OperationStatuses",
            "Microsoft.MobilePacketCore/Operations",
            "Microsoft.ModSimWorkbench/Locations/operationStatuses",
            "Microsoft.ModSimWorkbench/Locations",
            "Microsoft.ModSimWorkbench/Operations",
            "Microsoft.Monitor/operations",
            "Microsoft.Monitor/accounts",
            "Microsoft.Monitor/locations/locationOperationStatuses",
            "Microsoft.Monitor/locations/operationResults",
            "Microsoft.Monitor/locations",
            "Microsoft.Monitor/locations/operationStatuses",
            "Microsoft.MySQLDiscovery/locations",
            "Microsoft.MySQLDiscovery/locations/operationStatuses",
            "Microsoft.MySQLDiscovery/MySQLSites",
            "Microsoft.MySQLDiscovery/MySQLSites/MySQLServers",
            "Microsoft.MySQLDiscovery/MySQLSites/Refresh",
            "Microsoft.MySQLDiscovery/MySQLSites/Summaries",
            "Microsoft.MySQLDiscovery/MySQLSites/ErrorSummaries",
            "Microsoft.MySQLDiscovery/operations",
            "Microsoft.NetApp/netAppAccounts",
            "Microsoft.NetApp/netAppAccounts/snapshotPolicies",
            "Microsoft.NetApp/netAppAccounts/volumeGroups",
            "Microsoft.NetApp/netAppAccounts/capacityPools",
            "Microsoft.NetApp/netAppAccounts/capacityPools/volumes",
            "Microsoft.NetApp/netAppAccounts/capacityPools/volumes/mountTargets",
            "Microsoft.NetApp/netAppAccounts/capacityPools/volumes/snapshots",
            "Microsoft.NetApp/locations",
            "Microsoft.NetApp/locations/checkNameAvailability",
            "Microsoft.NetApp/locations/checkFilePathAvailability",
            "Microsoft.NetApp/operations",
            "Microsoft.NetApp/locations/checkQuotaAvailability",
            "Microsoft.NetApp/locations/queryNetworkSiblingSet",
            "Microsoft.NetApp/locations/updateNetworkSiblingSet",
            "Microsoft.NetApp/locations/regionInfo",
            "Microsoft.NetApp/locations/regionInfos",
            "Microsoft.NetApp/locations/QuotaLimits",
            "Microsoft.NetApp/locations/CheckInventory",
            "Microsoft.NetApp/locations/operationResults",
            "Microsoft.NetworkAnalytics/Locations",
            "Microsoft.NetworkAnalytics/Locations/OperationStatuses",
            "Microsoft.NetworkAnalytics/Operations",
            "Microsoft.NetworkAnalytics/registeredSubscriptions",
            "Microsoft.NetworkCloud/locations",
            "Microsoft.NetworkCloud/locations/operationStatuses",
            "Microsoft.NetworkCloud/clusterManagers",
            "Microsoft.NetworkCloud/racks",
            "Microsoft.NetworkCloud/clusters",
            "Microsoft.NetworkCloud/bareMetalMachines",
            "Microsoft.NetworkCloud/virtualMachines",
            "Microsoft.NetworkCloud/operations",
            "Microsoft.NetworkCloud/rackSkus",
            "Microsoft.NetworkCloud/cloudServicesNetworks",
            "Microsoft.NetworkCloud/l2Networks",
            "Microsoft.NetworkCloud/storageAppliances",
            "Microsoft.NetworkCloud/trunkedNetworks",
            "Microsoft.NetworkCloud/l3Networks",
            "Microsoft.NetworkCloud/clusters/metricsConfigurations",
            "Microsoft.NetworkCloud/virtualMachines/consoles",
            "Microsoft.NetworkCloud/clusters/bareMetalMachineKeySets",
            "Microsoft.NetworkCloud/clusters/bmcKeySets",
            "Microsoft.NetworkCloud/volumes",
            "Microsoft.NetworkCloud/registeredSubscriptions",
            "Microsoft.NetworkCloud/kubernetesClusters",
            "Microsoft.NetworkCloud/kubernetesClusters/agentPools",
            "Microsoft.NetworkFunction/azureTrafficCollectors",
            "Microsoft.NetworkFunction/azureTrafficCollectors/collectorPolicies",
            "Microsoft.NetworkFunction/meshVpns",
            "Microsoft.NetworkFunction/meshVpns/connectionPolicies",
            "Microsoft.NetworkFunction/meshVpns/privateEndpointConnections",
            "Microsoft.NetworkFunction/meshVpns/privateEndpointConnectionProxies",
            "Microsoft.NetworkFunction/operations",
            "Microsoft.NetworkFunction/locations",
            "Microsoft.NetworkFunction/locations/nfvOperations",
            "Microsoft.NetworkFunction/locations/nfvOperationResults",
            "Microsoft.Nutanix/operations",
            "Microsoft.Nutanix/locations",
            "Microsoft.ObjectStore/osNamespaces",
            "Microsoft.OffAzure/VMwareSites",
            "Microsoft.OffAzure/HyperVSites",
            "Microsoft.OffAzure/ServerSites",
            "Microsoft.OffAzure/ImportSites",
            "Microsoft.OffAzure/MasterSites",
            "Microsoft.OffAzure/locations",
            "Microsoft.OffAzure/locations/operationResults",
            "Microsoft.OffAzure/operations",
            "Microsoft.OffAzureSpringBoot/locations",
            "Microsoft.OffAzureSpringBoot/locations/operationStatuses",
            "Microsoft.OffAzureSpringBoot/springbootsites",
            "Microsoft.OffAzureSpringBoot/springbootsites/springbootservers",
            "Microsoft.OffAzureSpringBoot/springbootsites/springbootapps",
            "Microsoft.OffAzureSpringBoot/operations",
            "Microsoft.OffAzureSpringBoot/springbootsites/summaries",
            "Microsoft.OffAzureSpringBoot/springbootsites/errorsummaries",
            "Microsoft.OpenEnergyPlatform/Locations",
            "Microsoft.OpenEnergyPlatform/Locations/OperationStatuses",
            "Microsoft.OpenEnergyPlatform/energyservices",
            "Microsoft.OpenEnergyPlatform/checkNameAvailability",
            "Microsoft.OpenEnergyPlatform/Operations",
            "Microsoft.OpenEnergyPlatform/energyservices/privateEndpointConnections",
            "Microsoft.OpenEnergyPlatform/energyservices/privateLinkResources",
            "Microsoft.OpenEnergyPlatform/energyservices/privateEndpointConnectionProxies",
            "Microsoft.OperatorVoicemail/Operations",
            "Microsoft.OperatorVoicemail/Locations",
            "Microsoft.OperatorVoicemail/Locations/OperationStatuses",
            "Microsoft.OperatorVoicemail/Locations/checkNameAvailability",
            "Microsoft.OracleDiscovery/locations",
            "Microsoft.OracleDiscovery/locations/operationStatuses",
            "Microsoft.OracleDiscovery/oraclesites",
            "Microsoft.OracleDiscovery/oraclesites/oracleservers",
            "Microsoft.OracleDiscovery/oraclesites/oracledatabases",
            "Microsoft.OracleDiscovery/oraclesites/summaries",
            "Microsoft.OracleDiscovery/oraclesites/errorSummaries",
            "Microsoft.OracleDiscovery/operations",
            "Microsoft.Orbital/availableGroundStations",
            "Microsoft.Orbital/contactProfiles",
            "Microsoft.Orbital/spacecrafts",
            "Microsoft.Orbital/spacecrafts/contacts",
            "Microsoft.Orbital/groundStations",
            "Microsoft.Orbital/globalCommunicationsSites",
            "Microsoft.Orbital/l2Connections",
            "Microsoft.Orbital/edgeSites",
            "Microsoft.Orbital/operations",
            "Microsoft.Orbital/locations",
            "Microsoft.Orbital/locations/operationResults",
            "Microsoft.Orbital/locations/operationStatuses",
            "Microsoft.PartnerManagedConsumerRecurrence/recurrences",
            "Microsoft.PartnerManagedConsumerRecurrence/operations",
            "Microsoft.PartnerManagedConsumerRecurrence/checkEligibility",
            "Microsoft.PartnerManagedConsumerRecurrence/operationStatuses",
            "Microsoft.Peering/peerings",
            "Microsoft.Peering/peeringLocations",
            "Microsoft.Peering/legacyPeerings",
            "Microsoft.Peering/peerAsns",
            "Microsoft.Peering/peeringServices",
            "Microsoft.Peering/peeringServiceCountries",
            "Microsoft.Peering/peeringServiceLocations",
            "Microsoft.Peering/peeringServiceProviders",
            "Microsoft.Peering/checkServiceProviderAvailability",
            "Microsoft.Peering/lookingGlass",
            "Microsoft.Peering/cdnPeeringPrefixes",
            "Microsoft.Peering/operations",
            "Microsoft.Pki/Operations",
            "Microsoft.Portal/dashboards",
            "Microsoft.Portal/tenantconfigurations",
            "Microsoft.Portal/listTenantConfigurationViolations",
            "Microsoft.Portal/operations",
            "Microsoft.Portal/locations",
            "Microsoft.Portal/consoles",
            "Microsoft.Portal/locations/consoles",
            "Microsoft.Portal/userSettings",
            "Microsoft.Portal/locations/userSettings",
            "Microsoft.PowerBI/workspaceCollections",
            "Microsoft.PowerBI/locations",
            "Microsoft.PowerBI/locations/checkNameAvailability",
            "Microsoft.PowerBI/privateLinkServicesForPowerBI",
            "Microsoft.PowerBI/privateLinkServicesForPowerBI/operationResults",
            "Microsoft.PowerBI/operations",
            "Microsoft.PowerPlatform/operations",
            "Microsoft.PowerPlatform/enterprisePolicies",
            "Microsoft.PowerPlatform/accounts",
            "Microsoft.PowerPlatform/locations",
            "Microsoft.PowerPlatform/locations/deleteVirtualNetworkOrSubnets",
            "Microsoft.PowerPlatform/locations/validateDeleteVirtualNetworkOrSubnets",
            "Microsoft.ProfessionalService/checkNameAvailability",
            "Microsoft.ProfessionalService/eligibilityCheck",
            "Microsoft.ProfessionalService/operationResults",
            "Microsoft.ProfessionalService/operations",
            "Microsoft.ProfessionalService/resources",
            "Microsoft.ProgrammableConnectivity/operations",
            "Microsoft.ProgrammableConnectivity/locations",
            "Microsoft.ProgrammableConnectivity/locations/operationStatuses",
            "Microsoft.ProgrammableConnectivity/gateways",
            "Microsoft.ProgrammableConnectivity/openApiGateways",
            "Microsoft.ProgrammableConnectivity/openApiGatewayOfferings",
            "Microsoft.ProgrammableConnectivity/OperatorOfferings",
            "Microsoft.ProgrammableConnectivity/OperatorConnections",
            "Microsoft.ProgrammableConnectivity/operatorApiPlans",
            "Microsoft.ProgrammableConnectivity/operatorApiConnections",
            "Microsoft.ProviderHub/providerRegistrations",
            "Microsoft.ProviderHub/operationStatuses",
            "Microsoft.ProviderHub/providerRegistrations/resourceTypeRegistrations",
            "Microsoft.ProviderHub/providerRegistrations/defaultRollouts",
            "Microsoft.ProviderHub/providerRegistrations/customRollouts",
            "Microsoft.ProviderHub/providerRegistrations/checkinmanifest",
            "Microsoft.ProviderHub/providerRegistrations/resourceActions",
            "Microsoft.ProviderHub/availableAccounts",
            "Microsoft.ProviderHub/providerRegistrations/authorizedApplications",
            "Microsoft.Purview/accounts",
            "Microsoft.Purview/accounts/kafkaConfigurations",
            "Microsoft.Purview/operations",
            "Microsoft.Purview/setDefaultAccount",
            "Microsoft.Purview/removeDefaultAccount",
            "Microsoft.Purview/getDefaultAccount",
            "Microsoft.Purview/checkNameAvailability",
            "Microsoft.Purview/locations",
            "Microsoft.Purview/locations/operationResults",
            "Microsoft.Purview/locations/listFeatures",
            "Microsoft.Purview/locations/usages",
            "Microsoft.Purview/policies",
            "Microsoft.Quantum/Workspaces",
            "Microsoft.Quantum/Operations",
            "Microsoft.Quantum/Locations",
            "Microsoft.Quantum/Locations/OperationStatuses",
            "Microsoft.Quantum/locations/offerings",
            "Microsoft.Quantum/Locations/CheckNameAvailability",
            "Microsoft.Quota/usages",
            "Microsoft.Quota/quotas",
            "Microsoft.Quota/quotaRequests",
            "Microsoft.Quota/operationsStatus",
            "Microsoft.Quota/operations",
            "Microsoft.Quota/groupQuotas",
            "Microsoft.Quota/groupQuotas/groupQuotaLimits",
            "Microsoft.Quota/groupQuotas/subscriptions",
            "Microsoft.Quota/groupQuotas/groupQuotaRequests",
            "Microsoft.Quota/groupQuotas/quotaAllocations",
            "Microsoft.Quota/groupQuotas/quotaAllocationRequests",
            "Microsoft.Quota/groupQuotas/groupQuotaOperationsStatus",
            "Microsoft.Quota/groupQuotas/subscriptionRequests",
            "Microsoft.Quota/groupQuotas/quotaAllocationOperationsStatus",
            "Microsoft.RecommendationsService/locations",
            "Microsoft.RecommendationsService/locations/operationStatuses",
            "Microsoft.RecommendationsService/accounts",
            "Microsoft.RecommendationsService/accounts/modeling",
            "Microsoft.RecommendationsService/accounts/serviceEndpoints",
            "Microsoft.RecommendationsService/operations",
            "Microsoft.RecommendationsService/checkNameAvailability",
            "Microsoft.RedHatOpenShift/locations",
            "Microsoft.RedHatOpenShift/locations/operationresults",
            "Microsoft.RedHatOpenShift/locations/operationsstatus",
            "Microsoft.RedHatOpenShift/OpenShiftClusters",
            "Microsoft.RedHatOpenShift/operations",
            "Microsoft.RedHatOpenShift/locations/openshiftversions",
            "Microsoft.ResourceConnector/locations",
            "Microsoft.ResourceConnector/appliances",
            "Microsoft.ResourceConnector/locations/operationsstatus",
            "Microsoft.ResourceConnector/locations/operationresults",
            "Microsoft.ResourceConnector/operations",
            "Microsoft.ResourceConnector/telemetryconfig",
            "Microsoft.ResourceGraph/resources",
            "Microsoft.ResourceGraph/resourcesHistory",
            "Microsoft.ResourceGraph/resourceChanges",
            "Microsoft.ResourceGraph/resourceChangeDetails",
            "Microsoft.ResourceGraph/operations",
            "Microsoft.ResourceGraph/subscriptionsStatus",
            "Microsoft.ResourceGraph/queries",
            "Microsoft.ResourceGraph/generateQuery",
            "Microsoft.ResourceNotifications/eventGridFilters",
            "Microsoft.ResourceNotifications/operations",
            "Microsoft.Resources/deploymentScripts",
            "Microsoft.Resources/deploymentScripts/logs",
            "Microsoft.Resources/locations/deploymentScriptOperationResults",
            "Microsoft.Resources/templateSpecs",
            "Microsoft.Resources/templateSpecs/versions",
            "Microsoft.Resources/builtInTemplateSpecs",
            "Microsoft.Resources/builtInTemplateSpecs/versions",
            "Microsoft.Resources/deploymentStacks",
            "Microsoft.Resources/locations/deploymentStackOperationStatus",
            "Microsoft.Resources/mobobrokers",
            "Microsoft.Resources/tenants",
            "Microsoft.Resources/locations",
            "Microsoft.Resources/operationresults",
            "Microsoft.Resources/notifyResourceJobs",
            "Microsoft.Resources/tags",
            "Microsoft.Resources/checkPolicyCompliance",
            "Microsoft.Resources/providers",
            "Microsoft.Resources/checkresourcename",
            "Microsoft.Resources/calculateTemplateHash",
            "Microsoft.Resources/resources",
            "Microsoft.Resources/subscriptions",
            "Microsoft.Resources/subscriptions/resources",
            "Microsoft.Resources/subscriptions/providers",
            "Microsoft.Resources/subscriptions/operationresults",
            "Microsoft.Resources/resourceGroups",
            "Microsoft.Resources/subscriptions/resourceGroups",
            "Microsoft.Resources/subscriptions/resourcegroups/resources",
            "Microsoft.Resources/subscriptions/locations",
            "Microsoft.Resources/subscriptions/tagnames",
            "Microsoft.Resources/subscriptions/tagNames/tagValues",
            "Microsoft.Resources/deployments",
            "Microsoft.Resources/deployments/operations",
            "Microsoft.Resources/validateResources",
            "Microsoft.Resources/links",
            "Microsoft.Resources/operations",
            "Microsoft.Resources/bulkDelete",
            "Microsoft.Resources/changes",
            "Microsoft.Resources/snapshots",
            "Microsoft.Resources/dataBoundaries",
            "Microsoft.Resources/deploymentStacks/snapshots",
            "Microsoft.Resources/checkZonePeers",
            "Microsoft.SaaS/applications",
            "Microsoft.SaaS/checknameavailability",
            "Microsoft.SaaS/saasresources",
            "Microsoft.SaaS/operationResults",
            "Microsoft.SaaS/operations",
            "Microsoft.SaaS/resources",
            "Microsoft.SaaSHub/operationStatuses",
            "Microsoft.SaaSHub/cloudServices",
            "Microsoft.SaaSHub/operations",
            "Microsoft.SaaSHub/registeredSubscriptions",
            "Microsoft.SaaSHub/checkNameAvailability",
            "Microsoft.SaaSHub/canCreate",
            "Microsoft.SaaSHub/locations",
            "Microsoft.SaaSHub/locations/operationStatuses",
            "Microsoft.Scom/locations/operationStatuses",
            "Microsoft.Scom/operations",
            "Microsoft.Scom/locations",
            "Microsoft.Scom/managedInstances",
            "Microsoft.Scom/managedInstances/monitoredResources",
            "Microsoft.Scom/managedInstances/managedGateways",
            "Microsoft.ScVmm/locations",
            "Microsoft.ScVmm/Locations/OperationStatuses",
            "Microsoft.ScVmm/operations",
            "Microsoft.ScVmm/VMMServers",
            "Microsoft.ScVmm/Clouds",
            "Microsoft.ScVmm/VirtualNetworks",
            "Microsoft.ScVmm/VirtualMachineTemplates",
            "Microsoft.ScVmm/VirtualMachines",
            "Microsoft.ScVmm/AvailabilitySets",
            "Microsoft.ScVmm/VMMServers/InventoryItems",
            "Microsoft.ScVmm/VirtualMachines/HybridIdentityMetadata",
            "Microsoft.ScVmm/VirtualMachines/GuestAgents",
            "Microsoft.ScVmm/VirtualMachines/Extensions",
            "Microsoft.ScVmm/VirtualMachineInstances",
            "Microsoft.SecurityDetonation/chambers",
            "Microsoft.SecurityDetonation/operations",
            "Microsoft.SecurityDetonation/operationResults",
            "Microsoft.SecurityDetonation/checkNameAvailability",
            "Microsoft.SecurityDevOps/Locations",
            "Microsoft.SecurityDevOps/Locations/OperationStatuses",
            "Microsoft.SecurityDevOps/gitHubConnectors",
            "Microsoft.SecurityDevOps/azureDevOpsConnectors",
            "Microsoft.SecurityDevOps/azureDevOpsConnectors/orgs",
            "Microsoft.SecurityDevOps/gitHubConnectors/owners",
            "Microsoft.SecurityDevOps/azureDevOpsConnectors/orgs/projects",
            "Microsoft.SecurityDevOps/gitHubConnectors/owners/repos",
            "Microsoft.SecurityDevOps/azureDevOpsConnectors/orgs/projects/repos",
            "Microsoft.SecurityDevOps/Operations",
            "Microsoft.SecurityDevOps/gitHubConnectors/stats",
            "Microsoft.SecurityDevOps/gitHubConnectors/repos",
            "Microsoft.SecurityDevOps/azureDevOpsConnectors/stats",
            "Microsoft.SecurityDevOps/azureDevOpsConnectors/repos",
            "Microsoft.SecurityDevOps/gitLabConnectors",
            "Microsoft.SecurityDevOps/gitHubConnectors/gitHubInstallations",
            "Microsoft.SecurityDevOps/gitHubConnectors/gitHubInstallations/gitHubRepositories",
            "Microsoft.SecurityDevOps/gitLabConnectors/groups",
            "Microsoft.SecurityDevOps/gitLabConnectors/projects",
            "Microsoft.SecurityDevOps/gitLabConnectors/stats",
            "Microsoft.SecurityDevOps/gitLabConnectors/groups/projects",
            "Microsoft.SerialConsole/consoleServices",
            "Microsoft.SerialConsole/serialPorts",
            "Microsoft.SerialConsole/locations",
            "Microsoft.SerialConsole/locations/consoleServices",
            "Microsoft.SerialConsole/operations",
            "Microsoft.ServiceNetworking/trafficControllers",
            "Microsoft.ServiceNetworking/trafficControllers/frontends",
            "Microsoft.ServiceNetworking/trafficControllers/associations",
            "Microsoft.ServiceNetworking/operations",
            "Microsoft.ServiceNetworking/locations",
            "Microsoft.ServiceNetworking/locations/operations",
            "Microsoft.ServiceNetworking/locations/operationResults",
            "Microsoft.ServicesHub/connectors",
            "Microsoft.ServicesHub/workspaces",
            "Microsoft.ServicesHub/supportOfferingEntitlement",
            "Microsoft.ServicesHub/operations",
            "Microsoft.ServicesHub/getRecommendationsContent",
            "Microsoft.ServicesHub/connectors/connectorSpaces",
            "Microsoft.SignalRService/SignalR",
            "Microsoft.SignalRService/WebPubSub",
            "Microsoft.SignalRService/SignalR/replicas",
            "Microsoft.SignalRService/WebPubSub/replicas",
            "Microsoft.SignalRService/locations",
            "Microsoft.SignalRService/locations/operationResults",
            "Microsoft.SignalRService/locations/operationStatuses",
            "Microsoft.SignalRService/operations",
            "Microsoft.SignalRService/locations/checkNameAvailability",
            "Microsoft.SignalRService/locations/usages",
            "Microsoft.SignalRService/SignalR/eventGridFilters",
            "Microsoft.Singularity/accounts",
            "Microsoft.Singularity/accounts/storageContainers",
            "Microsoft.Singularity/accounts/networks",
            "Microsoft.Singularity/accounts/secrets",
            "Microsoft.Singularity/accounts/accountQuotaPolicies",
            "Microsoft.Singularity/accounts/groupPolicies",
            "Microsoft.Singularity/accounts/jobs",
            "Microsoft.Singularity/accounts/models",
            "Microsoft.Singularity/locations",
            "Microsoft.Singularity/locations/instanceTypeSeries",
            "Microsoft.Singularity/locations/instanceTypeSeries/instanceTypes",
            "Microsoft.Singularity/locations/operationResults",
            "Microsoft.Singularity/locations/operationStatus",
            "Microsoft.Singularity/operations",
            "Microsoft.Singularity/images",
            "Microsoft.Singularity/quotas",
            "Microsoft.SoftwarePlan/hybridUseBenefits",
            "Microsoft.SoftwarePlan/operations",
            "Microsoft.Solutions/applications",
            "Microsoft.Solutions/applicationDefinitions",
            "Microsoft.Solutions/locations",
            "Microsoft.Solutions/jitRequests",
            "Microsoft.Solutions/locations/operationstatuses",
            "Microsoft.Solutions/Operations",
            "Microsoft.Sovereign/Locations",
            "Microsoft.Sovereign/Locations/OperationStatuses",
            "Microsoft.Sovereign/landingZoneConfigurations",
            "Microsoft.Sovereign/landingZoneRegistrations",
            "Microsoft.Sovereign/Operations",
            "Microsoft.Sovereign/checkNameAvailability",
            "Microsoft.SqlVirtualMachine/SqlVirtualMachineGroups",
            "Microsoft.SqlVirtualMachine/SqlVirtualMachines",
            "Microsoft.SqlVirtualMachine/SqlVirtualMachineGroups/AvailabilityGroupListeners",
            "Microsoft.SqlVirtualMachine/operations",
            "Microsoft.SqlVirtualMachine/Locations",
            "Microsoft.SqlVirtualMachine/Locations/OperationTypes",
            "Microsoft.SqlVirtualMachine/Locations/sqlVirtualMachineOperationResults",
            "Microsoft.SqlVirtualMachine/Locations/sqlVirtualMachineGroupOperationResults",
            "Microsoft.SqlVirtualMachine/Locations/availabilityGroupListenerOperationResults",
            "Microsoft.SqlVirtualMachine/Locations/registerSqlVmCandidate",
            "Microsoft.StandbyPool/Locations",
            "Microsoft.StandbyPool/Locations/OperationStatuses",
            "Microsoft.StandbyPool/Operations",
            "Microsoft.StorageActions/storageTasks",
            "Microsoft.StorageActions/operations",
            "Microsoft.StorageActions/locations/asyncoperations",
            "Microsoft.StorageActions/locations/previewActions",
            "Microsoft.StorageActions/locations",
            "Microsoft.StorageCache/caches",
            "Microsoft.StorageCache/caches/storageTargets",
            "Microsoft.StorageCache/amlFilesystems",
            "Microsoft.StorageCache/operations",
            "Microsoft.StorageCache/usageModels",
            "Microsoft.StorageCache/checkAmlFSSubnets",
            "Microsoft.StorageCache/getRequiredAmlFSSubnetsSize",
            "Microsoft.StorageCache/locations",
            "Microsoft.StorageCache/locations/ascoperations",
            "Microsoft.StorageCache/locations/usages",
            "Microsoft.StorageMover/storageMovers",
            "Microsoft.StorageMover/storageMovers/projects",
            "Microsoft.StorageMover/storageMovers/agents",
            "Microsoft.StorageMover/storageMovers/endpoints",
            "Microsoft.StorageMover/storageMovers/projects/jobDefinitions",
            "Microsoft.StorageMover/operations",
            "Microsoft.StorageMover/storageMovers/projects/jobDefinitions/jobRuns",
            "Microsoft.StorageMover/locations",
            "Microsoft.StorageMover/locations/operationStatuses",
            "Microsoft.StorageSync/storageSyncServices",
            "Microsoft.StorageSync/storageSyncServices/syncGroups",
            "Microsoft.StorageSync/storageSyncServices/syncGroups/cloudEndpoints",
            "Microsoft.StorageSync/storageSyncServices/syncGroups/serverEndpoints",
            "Microsoft.StorageSync/storageSyncServices/registeredServers",
            "Microsoft.StorageSync/storageSyncServices/workflows",
            "Microsoft.StorageSync/operations",
            "Microsoft.StorageSync/locations",
            "Microsoft.StorageSync/locations/checkNameAvailability",
            "Microsoft.StorageSync/locations/workflows",
            "Microsoft.StorageSync/locations/operations",
            "Microsoft.StorageSync/locations/operationResults",
            "Microsoft.StorageTasks/locations",
            "Microsoft.Subscription/SubscriptionDefinitions",
            "Microsoft.Subscription/SubscriptionOperations",
            "Microsoft.Subscription/CreateSubscription",
            "Microsoft.Subscription/operations",
            "Microsoft.Subscription/cancel",
            "Microsoft.Subscription/validateCancel",
            "Microsoft.Subscription/rename",
            "Microsoft.Subscription/enable",
            "Microsoft.Subscription/subscriptions",
            "Microsoft.Subscription/aliases",
            "Microsoft.Subscription/operationResults",
            "Microsoft.Subscription/acceptChangeTenant",
            "Microsoft.Subscription/changeTenantStatus",
            "Microsoft.Subscription/changeTenantRequest",
            "Microsoft.Subscription/policies",
            "Microsoft.Subscription/acceptOwnership",
            "Microsoft.Subscription/acceptOwnershipStatus",
            "microsoft.support/operations",
            "microsoft.support/checkNameAvailability",
            "microsoft.support/classifyServices",
            "microsoft.support/services",
            "microsoft.support/services/problemclassifications",
            "microsoft.support/supporttickets",
            "microsoft.support/supporttickets/communications",
            "microsoft.support/operationresults",
            "microsoft.support/operationsstatus",
            "microsoft.support/lookUpResourceId",
            "microsoft.support/fileWorkspaces",
            "microsoft.support/fileWorkspaces/files",
            "Microsoft.Synapse/workspaces",
            "Microsoft.Synapse/workspaces/bigDataPools",
            "Microsoft.Synapse/workspaces/sqlPools",
            "Microsoft.Synapse/workspaces/sqlDatabases",
            "Microsoft.Synapse/locations/sqlDatabaseAzureAsyncOperation",
            "Microsoft.Synapse/locations/sqlDatabaseOperationResults",
            "Microsoft.Synapse/workspaces/kustoPools",
            "Microsoft.Synapse/locations/kustoPoolOperationResults",
            "Microsoft.Synapse/locations/kustoPoolCheckNameAvailability",
            "Microsoft.Synapse/workspaces/kustoPools/databases",
            "Microsoft.Synapse/workspaces/kustoPools/attacheddatabaseconfigurations",
            "Microsoft.Synapse/workspaces/kustoPools/databases/dataconnections",
            "Microsoft.Synapse/locations/sqlPoolAzureAsyncOperation",
            "Microsoft.Synapse/locations/sqlPoolOperationResults",
            "Microsoft.Synapse/workspaces/operationStatuses",
            "Microsoft.Synapse/workspaces/operationResults",
            "Microsoft.Synapse/checkNameAvailability",
            "Microsoft.Synapse/operations",
            "Microsoft.Synapse/kustoOperations",
            "Microsoft.Synapse/privateLinkHubs",
            "Microsoft.Synapse/locations",
            "Microsoft.Synapse/locations/operationResults",
            "Microsoft.Synapse/locations/operationStatuses",
            "Microsoft.Synapse/locations/usages",
            "Microsoft.Synapse/workspaces/usages",
            "Microsoft.Syntex/documentProcessors",
            "Microsoft.Syntex/operations",
            "Microsoft.Syntex/accounts",
            "Microsoft.Syntex/Locations",
            "Microsoft.Syntex/Locations/OperationStatuses",
            "Microsoft.TestBase/locations",
            "Microsoft.TestBase/locations/operationstatuses",
            "Microsoft.TestBase/skus",
            "Microsoft.TestBase/operations",
            "Microsoft.TestBase/testBaseAccounts",
            "Microsoft.TestBase/testBaseAccounts/usages",
            "Microsoft.TestBase/testBaseAccounts/availableOSs",
            "Microsoft.TestBase/testBaseAccounts/testTypes",
            "Microsoft.TestBase/testBaseAccounts/flightingRings",
            "Microsoft.TestBase/testBaseAccounts/packages",
            "Microsoft.TestBase/testBaseAccounts/packages/osUpdates",
            "Microsoft.TestBase/testBaseAccounts/testSummaries",
            "Microsoft.TestBase/testBaseAccounts/packages/favoriteProcesses",
            "Microsoft.TestBase/testBaseAccounts/packages/testResults",
            "Microsoft.TestBase/testBaseAccounts/packages/testResults/analysisResults",
            "Microsoft.TestBase/testBaseAccounts/emailEvents",
            "Microsoft.TestBase/testBaseAccounts/customerEvents",
            "Microsoft.TestBase/testBaseAccounts/featureUpdateSupportedOses",
            "Microsoft.TestBase/testBaseAccounts/availableInplaceUpgradeOSs",
            "Microsoft.TestBase/testBaseAccounts/firstPartyApps",
            "Microsoft.TestBase/testBaseAccounts/draftPackages",
            "Microsoft.TestBase/testBaseAccounts/actionRequests",
            "Microsoft.TestBase/testBaseAccounts/testConfigurations",
            "Microsoft.TestBase/testBaseAccounts/availableVMConfigurationTypes",
            "Microsoft.TestBase/testBaseAccounts/customImages",
            "Microsoft.TestBase/testBaseAccounts/vhds",
            "Microsoft.TestBase/testBaseAccounts/imageDefinitions",
            "Microsoft.TestBase/testBaseAccounts/galleryApps",
            "Microsoft.TestBase/testBaseAccounts/galleryApps/galleryAppSkus",
            "Microsoft.TestBase/testBaseAccounts/chatSessions",
            "Microsoft.TestBase/testBaseAccounts/freeHourBalances",
            "Microsoft.TestBase/testBaseAccounts/credentials",
            "Microsoft.TestBase/testBaseAccounts/testConfigurations/testResults",
            "Microsoft.UsageBilling/operations",
            "Microsoft.VideoIndexer/operations",
            "Microsoft.VideoIndexer/locations",
            "Microsoft.VideoIndexer/locations/operationstatuses",
            "Microsoft.VideoIndexer/accounts",
            "Microsoft.VideoIndexer/checknameavailability",
            "Microsoft.VideoIndexer/locations/userclassicaccounts",
            "Microsoft.VideoIndexer/locations/classicaccounts",
            "Microsoft.VirtualMachineImages/imageTemplates",
            "Microsoft.VirtualMachineImages/imageTemplates/runOutputs",
            "Microsoft.VirtualMachineImages/imageTemplates/triggers",
            "Microsoft.VirtualMachineImages/locations",
            "Microsoft.VirtualMachineImages/locations/operations",
            "Microsoft.VirtualMachineImages/operations",
            "microsoft.visualstudio/account",
            "microsoft.visualstudio/operations",
            "microsoft.visualstudio/account/extension",
            "microsoft.visualstudio/checkNameAvailability",
            "Microsoft.VMware/Locations",
            "Microsoft.VMware/Locations/OperationStatuses",
            "Microsoft.VMware/Operations",
            "Microsoft.VMware/VCenters/InventoryItems",
            "Microsoft.VoiceServices/Operations",
            "Microsoft.VoiceServices/locations",
            "Microsoft.VoiceServices/locations/checkNameAvailability",
            "Microsoft.VoiceServices/registeredSubscriptions",
            "Microsoft.VSOnline/accounts",
            "Microsoft.VSOnline/plans",
            "Microsoft.VSOnline/operations",
            "Microsoft.VSOnline/registeredSubscriptions",
            "Microsoft.WindowsIoT/DeviceServices",
            "Microsoft.WindowsIoT/operations",
            "Microsoft.WindowsPushNotificationServices/checkNameAvailability",
            "Microsoft.WorkloadBuilder/Locations",
            "Microsoft.WorkloadBuilder/Locations/OperationStatuses",
            "Microsoft.WorkloadBuilder/Operations",
            "Microsoft.Workloads/Locations",
            "Microsoft.Workloads/Locations/OperationStatuses",
            "Microsoft.Workloads/sapVirtualInstances",
            "Microsoft.Workloads/sapVirtualInstances/applicationInstances",
            "Microsoft.Workloads/sapVirtualInstances/centralInstances",
            "Microsoft.Workloads/sapVirtualInstances/databaseInstances",
            "Microsoft.Workloads/Operations",
            "Microsoft.Workloads/monitors",
            "Microsoft.Workloads/monitors/providerInstances",
            "Microsoft.Workloads/Locations/sapVirtualInstanceMetadata",
            "Microsoft.Workloads/connectors",
            "Microsoft.Workloads/connectors/acssBackups",
            "Microsoft.Workloads/monitors/sapLandscapeMonitor",
            "NewRelic.Observability/operations",
            "NewRelic.Observability/registeredSubscriptions",
            "NewRelic.Observability/locations",
            "NewRelic.Observability/locations/operationStatuses",
            "NewRelic.Observability/monitors",
            "NewRelic.Observability/monitors/tagRules",
            "NewRelic.Observability/checkNameAvailability",
            "NewRelic.Observability/accounts",
            "NewRelic.Observability/plans",
            "NewRelic.Observability/organizations",
            "NewRelic.Observability/monitors/monitoredSubscriptions",
            "NGINX.NGINXPLUS/operations",
            "NGINX.NGINXPLUS/locations",
            "NGINX.NGINXPLUS/locations/operationStatuses",
            "NGINX.NGINXPLUS/nginxDeployments/configurations",
            "NGINX.NGINXPLUS/nginxDeployments",
            "NGINX.NGINXPLUS/nginxDeployments/certificates",
            "Oracle.Database/Locations",
            "Oracle.Database/Locations/OperationStatuses",
            "Oracle.Database/Operations",
            "PaloAltoNetworks.Cloudngfw/operations",
            "PaloAltoNetworks.Cloudngfw/locations",
            "PaloAltoNetworks.Cloudngfw/registeredSubscriptions",
            "PaloAltoNetworks.Cloudngfw/checkNameAvailability",
            "PaloAltoNetworks.Cloudngfw/Locations/operationStatuses",
            "PaloAltoNetworks.Cloudngfw/firewalls",
            "PaloAltoNetworks.Cloudngfw/localRulestacks",
            "PaloAltoNetworks.Cloudngfw/globalRulestacks",
            "PaloAltoNetworks.Cloudngfw/localRulestacks/localRules",
            "PaloAltoNetworks.Cloudngfw/localRulestacks/fqdnlists",
            "PaloAltoNetworks.Cloudngfw/globalRulestacks/fqdnlists",
            "PaloAltoNetworks.Cloudngfw/globalRulestacks/preRules",
            "PaloAltoNetworks.Cloudngfw/globalRulestacks/postRules",
            "PaloAltoNetworks.Cloudngfw/globalRulestacks/prefixlists",
            "PaloAltoNetworks.Cloudngfw/localRulestacks/prefixlists",
            "PaloAltoNetworks.Cloudngfw/globalRulestacks/certificates",
            "PaloAltoNetworks.Cloudngfw/localRulestacks/certificates",
            "PaloAltoNetworks.Cloudngfw/firewalls/statuses",
            "PureStorage.Block/operations",
            "PureStorage.Block/locations",
            "PureStorage.Block/checkNameAvailability",
            "PureStorage.Block/locations/operationStatuses",
            "Qumulo.Storage/registeredSubscriptions",
            "Qumulo.Storage/locations",
            "Qumulo.Storage/locations/operationStatuses",
            "Qumulo.Storage/checkNameAvailability",
            "Qumulo.Storage/operations",
            "Qumulo.Storage/fileSystems",
            "SolarWinds.Observability/operations",
            "SolarWinds.Observability/registeredSubscriptions",
            "SolarWinds.Observability/locations",
            "SolarWinds.Observability/locations/operationStatuses",
            "SolarWinds.Observability/checkNameAvailability",
            "SplitIO.Experimentation/operations",
            "SplitIO.Experimentation/locations",
            "SplitIO.Experimentation/locations/operationStatuses",
            "SplitIO.Experimentation/checkNameAvailability",
            "Wandisco.Fusion/Locations",
            "Wandisco.Fusion/Locations/operationStatuses",
            "Wandisco.Fusion/registeredSubscriptions",
            "Wandisco.Fusion/Operations",
            "Wandisco.Fusion/migrators",
            "Wandisco.Fusion/migrators/targets",
            "Wandisco.Fusion/migrators/liveDataMigrations",
            "Wandisco.Fusion/migrators/exclusionTemplates",
            "Wandisco.Fusion/migrators/metadataMigrations",
            "Wandisco.Fusion/migrators/metadataTargets",
            "Wandisco.Fusion/migrators/pathMappings",
            "Wandisco.Fusion/migrators/dataTransferAgents",
            "Wandisco.Fusion/migrators/verifications"
        ]

    },
    "resources": [
        {
            "condition": "[not(contains(variables('knownPolicyInitativeDefinitionIdsThatRequireParamaeters'), parameters('policySetDefinitionId')))]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]"
            }
        },
        {
            // [Preview]: Australian Government ISM PROTECTED
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/27272c0b-c225-4cc3-b8b0-f2534b093077')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "membersToExclude": {
                        "value": "[parameters('regCompPolParAusGovIsmRestrictedVmAdminsExclude')]"
                    },
                    "logAnalyticsWorkspaceId": {
                        "value": "[parameters('logAnalyticsWorkspaceId')]"
                    },
                    "listOfResourceTypes": {
                        "value": "[if(equals(parameters('regCompPolParAusGovIsmRestrictedResourceTypes'), 'all'), variables('allResourceTypes'), createArray())]"
                    }
                }
            }
        },
        {
            // [Preview]: Motion Picture Association of America (MPAA)
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/92646f03-e39d-47a9-9e24-58d60ef49af8')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "certificateThumbprints": {
                        "value": "[parameters('regCompPolParMPAACertificateThumb')]"
                    },
                    "applicationName": {
                        "value": "[parameters('regCompPolParMPAAApplicationName')]"
                    },
                    "storagePrefix": {
                        "value": "[parameters('regCompPolParMPAAStoragePrefix')]"
                    },
                    "rgName": {
                        "value": "[parameters('regCompPolParMPAAResGroupPrefix')]"
                    },
                    "metricName": {
                        "value": "[parameters('regCompPolParMPAARBatchMetricName')]"
                    }
                }
            }
        },
        {
            // [Preview]: Sovereignty Baseline - Confidential Policies
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/03de05a4-c324-4ccd-882f-a814ea8ab9ea')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "listOfAllowedLocations": {
                        "value": "[parameters('regCompPolParSovBaseConfRegions')]"
                    }
                }
            }
        },
        {
            // [Preview]: Sovereignty Baseline - Global Policies
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/c1cbff38-87c0-4b9f-9f70-035c7a3b5523')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "listOfAllowedLocations": {
                        "value": "[parameters('regCompPolParSovBaseGlobalRegions')]"
                    }
                }
            }
        },
        {
            // [Preview]: SWIFT CSP-CSCF v2020
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/3e0c67fc-8c7c-406c-89bd-6b6bdc986a22')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "workspaceIDsLogAnalyticsAgentShouldConnectTo": {
                        "value": "[parameters('logAnalyticsWorkspaceId')]"
                    },
                    "listOfMembersToIncludeInWindowsVMAdministratorsGroup": {
                        "value": "[parameters('regCompPolParSwift2020VmAdminsInclude')]"
                    },
                    "domainNameFQDN": {
                        "value": "[parameters('regCompPolParSwift2020DomainFqdn')]"
                    }
                }
            }
        },
        {
            // Canada Federal PBMM
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/4c4a5f27-de81-430b-b4e5-9cbd50595a87')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "logAnalyticsWorkspaceIdforVMReporting": {
                        "value": "[parameters('logAnalyticsWorkspaceId')]"
                    },
                    "listOfMembersToExcludeFromWindowsVMAdministratorsGroup": {
                        "value": "[parameters('regCompPolParCanadaFedPbmmVmAdminsExclude')]"
                    },
                    "listOfMembersToIncludeInWindowsVMAdministratorsGroup": {
                        "value": "[parameters('regCompPolParCanadaFedPbmmVmAdminsInclude')]"
                    }
                }
            }
        },
        {
            // CIS Microsoft Azure Foundations Benchmark v2.0.0
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/06f19060-9e68-4070-92ca-f15cc126059e')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "maximumDaysToRotate-d8cf8476-a2ec-4916-896e-992351803c44": {
                        "value": "[parameters('regCompPolParCisV2KeyVaultKeysRotateDays')]"
                    }
                }
            }
        },
        {
            // CMMC Level 3
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/b5629c75-5c77-4422-87b9-2509e680f8de')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "logAnalyticsWorkspaceId-f47b5582-33ec-4c5c-87c0-b010a6b2e917": {
                        "value": "[parameters('logAnalyticsWorkspaceId')]"
                    },
                    "MembersToInclude-30f71ea1-ac77-4f26-9fc5-2d926bbd4ba7": {
                        "value": "[parameters('regCompPolParCmmcL3VmAdminsInclude')]"
                    },
                    "MembersToExclude-69bf4abd-ca1e-4cf6-8b5a-762d42e61d4f": {
                        "value": "[parameters('regCompPolParCmmcL3VmAdminsExclude')]"
                    }
                }
            }
        },
        {
            // HITRUST/HIPAA
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/a169a624-5599-4385-a696-c8d643089fab')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "CertificateThumbprints": {
                        "value": "[parameters('regCompPolParHitrustHipaaCertificateThumb')]"
                    },
                    "installedApplicationsOnWindowsVM": {
                        "value": "[parameters('regCompPolParHitrustHipaaApplicationName')]"
                    },
                    "DeployDiagnosticSettingsforNetworkSecurityGroupsstoragePrefix": {
                        "value": "[parameters('regCompPolParHitrustHipaaStoragePrefix')]"
                    },
                    "DeployDiagnosticSettingsforNetworkSecurityGroupsrgName": {
                        "value": "[parameters('regCompPolParHitrustHipaaResGroupPrefix')]"
                    }
                }
            }
        },
        {
            // IRS1075 September 2016
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/105e0327-6175-4eb2-9af4-1fba43bdb39d')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "logAnalyticsWorkspaceIdforVMReporting": {
                        "value": "[parameters('logAnalyticsWorkspaceId')]"
                    },
                    "listOfMembersToExcludeFromWindowsVMAdministratorsGroup": {
                        "value": "[parameters('regCompPolParIrs1075Sep2016VmAdminsExclude')]"
                    },
                    "listOfMembersToIncludeInWindowsVMAdministratorsGroup": {
                        "value": "[parameters('regCompPolParIrs1075Sep2016VmAdminsInclude')]"
                    }
                }
            }
        },
        {
            // New Zealand ISM Restricted
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/d1a462af-7e6d-4901-98ac-61570b4ed22a')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "MembersToInclude-30f71ea1-ac77-4f26-9fc5-2d926bbd4ba7": {
                        "value": "[parameters('regCompPolParNZIsmRestrictedVmAdminsInclude')]"
                    },
                    "MembersToExclude-69bf4abd-ca1e-4cf6-8b5a-762d42e61d4f": {
                        "value": "[parameters('regCompPolParNZIsmRestrictedVmAdminsExclude')]"
                    }
                }
            }
        },
        {
            // NIST SP 800-171 Rev. 2
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/03055927-78bd-4236-86c0-f36125a10dc9')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "logAnalyticsWorkspaceIDForVMAgents": {
                        "value": "[parameters('logAnalyticsWorkspaceId')]"
                    },
                    "membersToExcludeInLocalAdministratorsGroup": {
                        "value": "[parameters('regCompPolParNistSp800171R2VmAdminsExclude')]"
                    },
                    "membersToIncludeInLocalAdministratorsGroup": {
                        "value": "[parameters('regCompPolParNistSp800171R2VmAdminsInclude')]"
                    }
                }
            }
        },
        {
            // SOC 2 Type 2
            "condition": "[equals(parameters('policySetDefinitionId'), '/providers/Microsoft.Authorization/policySetDefinitions/4054785f-702b-4a98-9215-009cbd58b141')]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2022-06-01",
            "name": "[parameters('policyAssignmentName')]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "[parameters('policySetDefinitionDescription')]",
                "displayName": "[parameters('policySetDefinitionDisplayName')]",
                "policyDefinitionId": "[parameters('policySetDefinitionId')]",
                "enforcementMode": "[parameters('enforcementMode')]",
                "parameters": {
                    "allowedContainerImagesRegex-febd0533-8e55-448f-b837-bd0e06f16469": {
                        "value": "[parameters('regCompPolParSoc2Type2AllowedRegistries')]"
                    },
                    "cpuLimit-e345eecc-fa47-480f-9e88-67dcc122b164": {
                        "value": "[parameters('regCompPolParSoc2Type2MaxCpuUnits')]"
                    },
                    "memoryLimit-e345eecc-fa47-480f-9e88-67dcc122b164": {
                        "value": "[parameters('regCompPolParSoc2Type2MaxMemoryBytes')]"
                    }
                }
            }
        },
        {
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployRoles]",
            "dependsOn": [
                "[parameters('policyAssignmentName')]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacContributor'))]",
                "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', parameters('policyAssignmentName')), '2019-09-01', 'Full' ).identity.principalId)]"
            }
        }
    ],
    "outputs": {}
}