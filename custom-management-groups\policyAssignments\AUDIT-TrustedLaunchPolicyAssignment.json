{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}, "effect": {"type": "string", "allowedValues": ["Disabled", "Audit"], "defaultValue": "Audit"}}, "variables": {"policyDefinitions": {"auditTrustedLaunch": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Audit-TrustedLaunch')]"}, "policyAssignmentNames": {"trustedLaunch": "Audit-TrustedLaunch", "description": "Trusted Launch improves security of a Virtual Machine which requires VM SKU, OS Disk & OS Image to support it (Gen 2). To learn more about Trusted Launch, visit https://aka.ms/trustedlaunch.", "displayName": "Audit virtual machines for Trusted Launch support"}, "nonComplianceMessage": {"message": "Trusted Launch {enforcementMode} be used on supported virtual machines for enhanced security.", "Default": "must", "DoNotEnforce": "should"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentNames').trustedLaunch]", "location": "[deployment().location]", "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').auditTrustedLaunch]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {"effect": {"value": "[parameters('effect')]"}}}}], "outputs": {}}