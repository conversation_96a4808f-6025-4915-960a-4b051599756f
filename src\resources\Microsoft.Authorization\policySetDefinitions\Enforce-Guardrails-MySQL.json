{"name": "Enforce-Guardrails-MySQL", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for MySQL", "description": "This policy initiative is a group of policies that ensures MySQL is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "MySQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"mySqlInfraEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "mySqlAdvThreatProtection": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/80ed5239-4122-41ed-b54a-6f1fa7552816", "policyDefinitionReferenceId": "Dine-MySql-Adv-Threat-Protection", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('mySqlAdvThreatProtection')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3a58212a-c829-4f13-9872-6371df2fd0b4", "policyDefinitionReferenceId": "Deny-MySql-Infra-Encryption", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('mySqlInfraEncryption')]"}}}], "policyDefinitionGroups": null}}