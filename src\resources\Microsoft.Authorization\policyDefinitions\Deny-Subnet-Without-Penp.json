{"name": "<PERSON>y-Subnet-Without-Pen<PERSON>", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Subnets without Private Endpoint Network Policies enabled should be denied", "description": "This policy denies the creation of a subnet without Private Endpoint Netwotk Policies enabled. This policy is intended for 'workload' subnets, not 'central infrastructure' (aka, 'hub') subnets.", "metadata": {"version": "1.0.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>", "metadata": {"displayName": "Effect", "description": "The effect determines what happens when the policy rule is evaluated to match"}}, "excludedSubnets": {"type": "Array", "metadata": {"displayName": "Excluded Subnets", "description": "Array of subnet names that are excluded from this policy"}, "defaultValue": ["GatewaySubnet", "AzureFirewallSubnet", "AzureFirewallManagementSubnet", "AzureBastionSubnet"]}}, "policyRule": {"if": {"anyOf": [{"allOf": [{"equals": "Microsoft.Network/virtualNetworks", "field": "type"}, {"count": {"field": "Microsoft.Network/virtualNetworks/subnets[*]", "where": {"allOf": [{"field": "Microsoft.Network/virtualNetworks/subnets[*].privateEndpointNetworkPolicies", "notEquals": "Enabled"}, {"field": "Microsoft.Network/virtualNetworks/subnets[*].name", "notIn": "[[parameters('excludedSubnets')]"}]}}, "notEquals": 0}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/virtualNetworks/subnets"}, {"field": "name", "notIn": "[[parameters('excludedSubnets')]"}, {"field": "Microsoft.Network/virtualNetworks/subnets/privateEndpointNetworkPolicies", "notEquals": "Enabled"}]}]}, "then": {"effect": "[[parameters('effect')]"}}}}