{"name": "d3584a79-4f0d-5980-aa3c-7a76ba783b76", "type": "Microsoft.Authorization/roleDefinitions", "apiVersion": "2022-04-01", "properties": {"roleName": "Security-Operations", "description": "Security Administrator role with a horizontal view across the entire Azure estate and the Azure Key Vault purge policy.", "type": "customRole", "permissions": [{"actions": ["*/read", "*/register/action", "Microsoft.KeyVault/locations/deletedVaults/purge/action", "Microsoft.PolicyInsights/*", "Microsoft.Authorization/policyAssignments/*", "Microsoft.Authorization/policyDefinitions/*", "Microsoft.Authorization/policyExemptions/*", "Microsoft.Authorization/policySetDefinitions/*", "Microsoft.Insights/alertRules/*", "Microsoft.Resources/deployments/*", "Microsoft.Security/*", "Microsoft.Support/*"], "notActions": [], "dataActions": [], "notDataActions": []}], "assignableScopes": ["/providers/Microsoft.Management/managementGroups/contoso"]}}