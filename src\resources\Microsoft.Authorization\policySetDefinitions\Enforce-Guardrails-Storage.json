{"name": "Enforce-Guardrails-Storage", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Storage Account", "description": "This policy initiative is a group of policies that ensures Storage is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Storage", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"storageKeysExpiration": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountNetworkRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountRestrictNetworkRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageThreatProtection": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "storageClassicToArm": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountsInfraEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountSharedKey": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountsCrossTenant": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountsDoubleEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountsCopyScope": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAccountsAllowedCopyScope": {"type": "string", "defaultValue": "AAD"}, "storageServicesEncryption": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageLocalUser": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageSftp": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageNetworkAclsBypass": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageAllowedNetworkAclsBypass": {"type": "array", "defaultValue": ["None"]}, "storageResourceAccessRulesTenantId": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageResourceAccessRulesResourceId": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageNetworkAclsVirtualNetworkRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageContainerDeleteRetentionPolicy": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "storageMinContainerDeleteRetentionInDays": {"type": "Integer", "defaultValue": 7}, "storageCorsRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "modifyStorageFileSyncPublicEndpoint": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "modifyStorageAccountPublicEndpoint": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "storageAccountsModifyDisablePublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-CopyScope", "policyDefinitionReferenceId": "Deny-Storage-CopyScope", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageAccountsCopyScope')]"}, "allowedCopyScope": {"value": "[[parameters('storageAccountsAllowedCopyScope')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-ServicesEncryption", "policyDefinitionReferenceId": "Deny-Storage-ServicesEncryption", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageServicesEncryption')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-LocalUser", "policyDefinitionReferenceId": "Deny-Storage-LocalUser", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageLocalUser')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-SFTP", "policyDefinitionReferenceId": "Deny-Storage-SFTP", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageSftp')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-NetworkAclsBypass", "policyDefinitionReferenceId": "Deny-Storage-NetworkAclsBypass", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageNetworkAclsBypass')]"}, "allowedBypassOptions": {"value": "[[parameters('storageAllowedNetworkAclsBypass')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-ResourceAccessRulesTenantId", "policyDefinitionReferenceId": "Deny-Storage-ResourceAccessRulesTenantId", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageResourceAccessRulesTenantId')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-ResourceAccessRulesResourceId", "policyDefinitionReferenceId": "Deny-Storage-ResourceAccessRulesResourceId", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageResourceAccessRulesResourceId')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-NetworkAclsVirtualNetworkRules", "policyDefinitionReferenceId": "Deny-Storage-NetworkAclsVirtualNetworkRules", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageNetworkAclsVirtualNetworkRules')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-ContainerDeleteRetentionPolicy", "policyDefinitionReferenceId": "Deny-Storage-ContainerDeleteRetentionPolicy", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageContainerDeleteRetentionPolicy')]"}, "minContainerDeleteRetentionInDays": {"value": "[[parameters('storageMinContainerDeleteRetentionInDays')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Storage-CorsRules", "policyDefinitionReferenceId": "Deny-Storage-CorsRules", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageCorsRules')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/bfecdea6-31c4-4045-ad42-71b9dc87247d", "policyDefinitionReferenceId": "Deny-Storage-Account-Encryption", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageAccountsDoubleEncryption')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/92a89a79-6c52-4a7e-a03f-61306fc49312", "policyDefinitionReferenceId": "Deny-Storage-Cross-Tenant", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageAccountsCrossTenant')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8c6a50c6-9ffd-4ae7-986f-5fa6111f9a54", "policyDefinitionReferenceId": "Deny-Storage-Shared-Key", "definitionVersion": "2.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageAccountSharedKey')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4733ea7b-a883-42fe-8cac-97454c2a9e4a", "policyDefinitionReferenceId": "Deny-Storage-Infra-Encryption", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageAccountsInfraEncryption')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/37e0d2fe-28a5-43d6-a273-67d37d1f5606", "policyDefinitionReferenceId": "Deny-Storage-Classic", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageClassicToArm')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/361c2074-3595-4e5d-8cab-4f21dffc835c", "policyDefinitionReferenceId": "Dine-Storage-Threat-Protection", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageThreatProtection')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/34c877ad-507e-4c82-993e-3452a6e0ad3c", "policyDefinitionReferenceId": "Deny-Storage-Restrict-NetworkRules", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageAccountRestrictNetworkRules')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2a1a9cdf-e04d-429a-8416-3bfb72a1b26f", "policyDefinitionReferenceId": "Deny-Storage-NetworkRules", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageAccountNetworkRules')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/044985bb-afe1-42cd-8a36-9d5d42424537", "policyDefinitionReferenceId": "Deny-Storage-Account-Keys-Expire", "definitionVersion": "3.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageKeysExpiration')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0e07b2e9-6cd9-4c40-9ccb-52817b95133b", "policyDefinitionReferenceId": "Modify-Storage-FileSync-PublicEndpoint", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('modifyStorageFileSyncPublicEndpoint')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/********-8df0-4414-9937-de9c5c4e396b", "policyDefinitionReferenceId": "Modify-Blob-Storage-Account-PublicEndpoint", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('modifyStorageAccountPublicEndpoint')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a06d0189-92e8-4dba-b0c4-08d7669fce7d", "policyDefinitionReferenceId": "Modify-Storage-Account-PublicEndpoint", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('storageAccountsModifyDisablePublicNetworkAccess')]"}}}], "policyDefinitionGroups": null}}