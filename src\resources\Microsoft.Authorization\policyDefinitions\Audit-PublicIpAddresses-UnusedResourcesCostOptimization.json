{"name": "Audit-PublicIpAddresses-UnusedResourcesCostOptimization", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Unused Public IP addresses driving cost should be avoided", "mode": "All", "description": "Optimize cost by detecting unused but chargeable resources. Leverage this Policy definition as a cost control to reveal orphaned Public IP addresses that are driving cost.", "metadata": {"version": "1.1.0", "category": "Cost Optimization", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "Disabled"], "defaultValue": "Audit"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "microsoft.network/publicIpAddresses"}, {"field": "Microsoft.Network/publicIPAddresses/publicIPAllocationMethod", "equals": "Static"}, {"anyOf": [{"field": "Microsoft.Network/publicIPAddresses/natGateway", "exists": false}, {"value": "[[equals(length(field('Microsoft.Network/publicIPAddresses/natGateway')), 0)]", "equals": true}]}, {"anyOf": [{"field": "Microsoft.Network/publicIPAddresses/ipConfiguration", "exists": false}, {"value": "[[equals(length(field('Microsoft.Network/publicIPAddresses/ipConfiguration')), 0)]", "equals": true}]}, {"anyOf": [{"field": "Microsoft.Network/publicIPAddresses/publicIPPrefix", "exists": false}, {"value": "[[equals(length(field('Microsoft.Network/publicIPAddresses/publicIPPrefix')), 0)]", "equals": true}]}]}, "then": {"effect": "[[parameters('effect')]"}}}}