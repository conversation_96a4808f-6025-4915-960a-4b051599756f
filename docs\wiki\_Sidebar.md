# Wiki content

* [What's New?](./Whats-new)
* [Community Calls](./Community-Calls)
* [Frequently Asked Questions (FAQ)](./FAQ)
* [Known issues](./ALZ-Known-Issues)
* [What is Enterprise-Scale](./What-is-Enterprise-Scale)
  * [Architecture](./ALZ-Architecture)
  * [Policies](./ALZ-Policies)
  * [What is the reference implementation?](./What-is-Enterprise-Scale#what-is-enterprise-scale-reference-implementation)
  * [Pricing](./What-is-Enterprise-Scale#pricing)
  * [What if I already have an existing Azure footprint](./What-is-Enterprise-Scale#what-if-i-already-have-an-existing-azure-footprint)
* [How it Works](./How-Enterprise-Scale-Works)
  * [Design principles](./How-Enterprise-Scale-Works#enterprise-scale-design-principles)
  * [Separating platform and landing zones](./How-Enterprise-Scale-Works#separating-platform-and-landing-zones)
  * [Management Group Structure](./How-Enterprise-Scale-Works#enterprise-scale-management-group-structure)
  * [What happens when you deploy Enterprise-Scale?](./How-Enterprise-Scale-Works#what-happens-when-you-deploy-enterprise-scale)
* Deploying Enterprise-Scale
  * [Pre-requisites](./Deploying-ALZ-Pre-requisites)
  * [ALZ Resource Providers Guidance](./ALZ-Resource-Provider-Recommendations)
  * [Configure Microsoft Entra permissions](./ALZ-Setup-aad-permissions)
  * [Configure Azure permissions](./ALZ-Setup-azure)
  * [Deploy landing zones](./ALZ-Deploy-landing-zones)
  * [Deploy reference implementations](./ALZ-Deploy-reference-implementations)
  * [Telemetry Tracking Using Customer Usage Attribution (PID)](./Deploying-ALZ-CustomerUsage)
  * [Deploy without hybrid connectivity to on-premises](./Deploying-ALZ-Foundation)
  * [Deploy with a hub and spoke based network topology](./Deploying-ALZ-HubAndSpoke)
  * [Deploy with a hub and spoke based network topology with Zero Trust principles](./Deploying-ALZ-ZTNetwork)
  * [Deploy with an Azure Virtual WAN based network topology](./Deploying-ALZ-VWAN)
  * [Deploy for Small Enterprises](./Deploying-ALZ-BasicSetup)
  * [Operating the Azure platform using AzOps (Infrastructure as Code with GitHub Actions)](./Deploying-ALZ-Platform-DevOps#operating-the-azure-platform-using-azops-infrastructure-as-code-with-github-actions)
  * [Deploy workloads](./ALZ-Deploy-workloads)
* [Create landing zones (subscriptions) via Subscription Vending](./Create-Landingzones)
* [Azure Landing Zones Deprecated Services](./ALZ-Deprecated-Services)
* Azure Landing Zone (ALZ) Policies
  * [Policies included in Azure landing zones reference implementations](./ALZ-Policies)
  * [Policies included but not assigned by default and Workload Specific Compliance initiatives](./ALZ-Policies-Extra)
  * [Policies FAQ & Tips](./ALZ-Policies-FAQ)
  * [Policies Testing Framework](./ALZ-Policies-Testing)
  * [Migrate Azure landing zones custom policies to Azure built-in policies](./Migrate-ALZ-Policies-to-Built%E2%80%90in)
  * [Updating Azure landing zones custom policies to latest](./Update-ALZ-Custom-Policies-to-Latest)
* MMA Deprecation Guidance
  * [Azure Monitor Agent Update](./ALZ-AMA-Update)
  * [AMA Migration Guidance](./ALZ-AMA-Migration-Guidance)
  * [PowerShell script](./ALZ-AMA-PowerShell-Script)
  * [AMA FAQ](./ALZ-AMA-FAQ)
* [Contributing](./ALZ-Contribution-Guide)
  * [Reporting Bugs](./ALZ-Contribution-Guide#reporting-bugs)
  * [Feature Requests](./ALZ-Contribution-Guide#feature-requests)
  * [Report a security vulnerability](./ALZ-Contribution-Guide#report-a-security-vulnerability)
  * [How to submit a pull request to upstream repo](./ALZ-Contribution-Guide#how-to-submit-pull-request-to-upstream-repo)
  * [ALZ Custom Policies](./ALZ-Contribution-Guide#working-with-alz-custom-policies)
