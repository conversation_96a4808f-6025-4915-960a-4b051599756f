# Azure Landing Zones - Policy Definitions Reference

Đây là danh sách các policy definitions và policy set definitions quan trọng từ Azure Landing Zones.

## 🏷️ Tags Policies

### Audit-Tags-Mandatory
- **Mục đích**: Audit mandatory tags trên resources
- **Mode**: All
- **Category**: Tags
- **Effect**: Audit

### Audit-Tags-Mandatory-Rg
- **Mục đích**: Audit mandatory tags trên resource groups
- **Mode**: All
- **Category**: Tags
- **Effect**: Audit

## 🔒 Security Policies

### Deny-MgmtPorts-From-Internet
- **Mục đích**: Deny management ports accessible from Internet
- **Mode**: All
- **Category**: Network
- **Effect**: Deny
- **Ports**: SSH (22), RDP (3389)

### Audit-PrivateLinkDnsZones
- **Mục đích**: Audit creation of Private Link DNS Zones
- **Mode**: Indexed
- **Category**: Network
- **Effect**: Audit/Deny

### Deploy-Sql-SecurityAlertPolicies
- **Mục đích**: Deploy SQL security alert policies
- **Mode**: Indexed
- **Category**: SQL
- **Effect**: DeployIfNotExists

## 🛡️ Guardrails Policy Set Definitions

### Enforce-Guardrails-Compute
- **Mục đích**: Enforce compute guardrails
- **Category**: Compute
- **Policies Included**:
  - Deny VM and VMSS encryption at host
  - Deny disk double encryption
  - VM backup requirements

### Enforce-Guardrails-Network
- **Mục đích**: Enforce network guardrails
- **Category**: Network
- **Policies Included**:
  - Deny NSG on Gateway subnet
  - Deny VPN Azure AD authentication
  - Network security requirements

### Enforce-Guardrails-Storage
- **Mục đích**: Enforce storage guardrails
- **Category**: Storage
- **Policies Included**:
  - Storage encryption requirements
  - HTTPS only access
  - Public access restrictions

### Enforce-Guardrails-SQL
- **Mục đích**: Enforce SQL guardrails
- **Category**: SQL
- **Policies Included**:
  - SQL encryption requirements
  - Auditing configuration
  - Threat detection

### Enforce-Guardrails-KeyVault
- **Mục đích**: Enforce Key Vault guardrails
- **Category**: Key Vault
- **Policies Included**:
  - Key Vault access policies
  - Network restrictions
  - Encryption requirements

### Enforce-Guardrails-APIM
- **Mục đích**: Enforce API Management guardrails
- **Category**: API Management
- **Policies Included**:
  - APIM without Key Vault integration
  - APIM without VNet integration

### Enforce-Guardrails-OpenAI
- **Mục đích**: Enforce OpenAI/Cognitive Services guardrails
- **Category**: Cognitive Services
- **Policies Included**:
  - Outbound network access restrictions
  - Diagnostic settings requirements

### Enforce-Guardrails-CosmosDb
- **Mục đích**: Enforce Cosmos DB guardrails
- **Category**: Cosmos DB
- **Policies Included**:
  - Cosmos DB encryption
  - Network access restrictions

### Enforce-Guardrails-Automation
- **Mục đích**: Enforce Automation Account guardrails
- **Category**: Automation
- **Policies Included**:
  - Automation account security
  - Network restrictions

## 🏗️ Environment-Specific Policies

### Enforce-ALZ-Sandbox
- **Mục đích**: Enforce sandbox environment restrictions
- **Category**: Sandbox
- **Policies Included**:
  - Resource type restrictions
  - VNet peering denial
  - Cost control measures

### Enforce-ALZ-Decomm
- **Mục đích**: Enforce decommissioned environment policies
- **Category**: Decommissioned
- **Policies Included**:
  - Allow only specific resource types
  - Auto-shutdown VMs

## 🔐 Encryption Policies

### Deny-EH-Premium-CMK
- **Mục đích**: Require customer-managed keys for Event Hub Premium
- **Mode**: All
- **Category**: Event Hub
- **Effect**: Deny

## 📊 Monitoring Policies

### Deploy-MDFC-DefenderSQL-AMA
- **Mục đích**: Deploy Microsoft Defender for SQL with AMA
- **Category**: Security Center
- **Status**: Deprecated
- **Superseded by**: Built-in initiative de01d381-bae9-4670-8870-786f89f49e26

## 🎯 Recommended Policy Assignments by Management Group

### Root Management Group
- Audit-Tags-Mandatory
- Deny-Classic-Resources
- Deploy-ASC-Monitoring
- Deploy-Activity-Log

### Platform Management Group
- Deploy-VM-Monitoring
- Deploy-Resource-Diagnostics
- Enforce-Backup-Policies

### Connectivity Management Group
- Deploy-Private-DNS-Zones
- Enforce-Network-Guardrails
- Deny-Public-Endpoints

### Landing Zones Management Group
- Enforce-Guardrails-Compute
- Enforce-Guardrails-Storage
- Enforce-Guardrails-SQL
- Enforce-Guardrails-KeyVault
- Deploy-VM-Backup
- Deploy-SQL-Threat

### Production Landing Zones
- Stricter enforcement modes
- Mandatory backup policies
- Enhanced monitoring
- Security alert policies

### Non-Production Landing Zones
- Audit mode for most policies
- Cost optimization policies
- Development-friendly configurations

### Sandbox Management Group
- Enforce-ALZ-Sandbox
- Resource type restrictions
- Cost limitations
- Network isolation

### Decommissioned Management Group
- Enforce-ALZ-Decomm
- Minimal allowed resource types
- Auto-shutdown policies

## 📝 Usage Notes

1. **Cloud Environment Support**: Hầu hết policies support AzureCloud, AzureChinaCloud, và AzureUSGovernment
2. **Versioning**: Sử dụng semantic versioning (major.minor.patch)
3. **Parameters**: Hầu hết policies có configurable parameters cho flexibility
4. **Effects**: Support multiple effects (Audit, Deny, DeployIfNotExists, Modify)
5. **Deprecation**: Một số policies đã deprecated và được thay thế bởi built-in policies
