{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"ddosName": {"type": "string", "metadata": {"description": "Provide a name for the DDoS protection plan"}}, "location": {"type": "string", "metadata": {"description": "Provide a location for the DDoS protection plan"}}}, "resources": [{"type": "Microsoft.Network/ddosProtectionPlans", "apiVersion": "2019-02-01", "name": "[parameters('ddosName')]", "location": "[parameters('location')]", "properties": {}}], "outputs": {}}