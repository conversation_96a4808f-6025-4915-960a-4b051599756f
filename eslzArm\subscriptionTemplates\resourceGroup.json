{"$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"rgName": {"type": "string", "metadata": {"description": "Provide name for resource group"}}, "location": {"type": "string", "metadata": {"description": "Provide location for the resource group"}}}, "variables": {}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "apiVersion": "2020-10-01", "name": "[parameters('rgName')]", "location": "[parameters('location')]"}], "outputs": {}}