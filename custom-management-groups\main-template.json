{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"description": "Custom Management Group Structure with Azure Landing Zone Policies"}, "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "defaultValue": "EWH", "maxLength": 10, "metadata": {"description": "Provide a prefix for the management group hierarchy."}}, "enablePolicyDeployment": {"type": "bool", "defaultValue": true, "metadata": {"description": "Enable deployment of policy definitions and assignments."}}, "currentDateTimeUtcNow": {"type": "string", "defaultValue": "[utcNow()]", "metadata": {"description": "Current date time in UTC for unique deployment names."}}}, "variables": {"deploymentSuffix": "[concat('-', deployment().location, '-', uniqueString(parameters('currentDateTimeUtcNow')))]", "managementGroups": {"root": "[parameters('topLevelManagementGroupPrefix')]", "platform": "[concat(parameters('topLevelManagementGroupPrefix'), '-Platform')]", "platformManagement": "[concat(parameters('topLevelManagementGroupPrefix'), '-Platform-Management')]", "platformConnectivity": "[concat(parameters('topLevelManagementGroupPrefix'), '-Platform-Connectivity')]", "landingZones": "[concat(parameters('topLevelManagementGroupPrefix'), '-LandingZones')]", "lzPrd": "[concat(parameters('topLevelManagementGroupPrefix'), '-lz-prd')]", "lzNonPrd": "[concat(parameters('topLevelManagementGroupPrefix'), '-lz-non-prd')]", "lzPrdLegacy": "[concat(parameters('topLevelManagementGroupPrefix'), '-lz-prd-legacy')]", "lzPrdMicrowave": "[concat(parameters('topLevelManagementGroupPrefix'), '-lz-prd-microwave')]", "lzNonPrdUat": "[concat(parameters('topLevelManagementGroupPrefix'), '-lz-non-prd-uat')]", "lzNonPrdDev": "[concat(parameters('topLevelManagementGroupPrefix'), '-lz-non-prd-dev')]", "decommissioned": "[concat(parameters('topLevelManagementGroupPrefix'), '-Decommissioned')]", "sandbox": "[concat(parameters('topLevelManagementGroupPrefix'), '-Sandbox')]"}, "deploymentNames": {"managementGroups": "[take(concat('mgmt-groups', variables('deploymentSuffix')), 64)]", "policyDefinitions": "[take(concat('policy-defs', variables('deploymentSuffix')), 64)]", "policyAssignments": "[take(concat('policy-assign', variables('deploymentSuffix')), 64)]"}}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2021-04-01", "name": "[variables('deploymentNames').managementGroups]", "location": "[deployment().location]", "properties": {"mode": "Incremental", "templateLink": {"uri": "[uri(deployment().properties.templateLink.uri, 'management-groups.json')]", "contentVersion": "*******"}, "parameters": {"managementGroups": {"value": "[variables('managementGroups')]"}}}}, {"condition": "[parameters('enablePolicyDeployment')]", "type": "Microsoft.Resources/deployments", "apiVersion": "2021-04-01", "name": "[variables('deploymentNames').policyDefinitions]", "location": "[deployment().location]", "dependsOn": ["[resourceId('Microsoft.Resources/deployments', variables('deploymentNames').managementGroups)]"], "properties": {"mode": "Incremental", "templateLink": {"uri": "[uri(deployment().properties.templateLink.uri, 'policy-definitions.json')]", "contentVersion": "*******"}, "parameters": {"topLevelManagementGroupId": {"value": "[variables('managementGroups').root]"}}}}, {"condition": "[parameters('enablePolicyDeployment')]", "type": "Microsoft.Resources/deployments", "apiVersion": "2021-04-01", "name": "[variables('deploymentNames').policyAssignments]", "location": "[deployment().location]", "dependsOn": ["[resourceId('Microsoft.Resources/deployments', variables('deploymentNames').managementGroups)]", "[resourceId('Microsoft.Resources/deployments', variables('deploymentNames').policyDefinitions)]"], "properties": {"mode": "Incremental", "templateLink": {"uri": "[uri(deployment().properties.templateLink.uri, 'policy-assignments.json')]", "contentVersion": "*******"}, "parameters": {"managementGroups": {"value": "[variables('managementGroups')]"}}}}], "outputs": {"managementGroupIds": {"type": "object", "value": "[variables('managementGroups')]"}}}