#!/bin/bash

# Validation Script for Custom Management Group Structure
# This script validates the deployed management groups and policies

set -e

# Default parameters
MANAGEMENT_GROUP_PREFIX="EWH"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --prefix)
            MANAGEMENT_GROUP_PREFIX="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --prefix PREFIX    Management group prefix (default: EWH)"
            echo "  -h, --help        Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "Validating Custom Management Group Structure..."
echo "Management Group Prefix: $MANAGEMENT_GROUP_PREFIX"

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo "Azure CLI is not installed. Please install it first."
    exit 1
fi

# Check if user is logged in
if ! az account show &> /dev/null; then
    echo "Please login to Azure first using 'az login'"
    exit 1
fi

echo "Current Azure Account: $(az account show --query user.name -o tsv)"

# Define expected management groups
EXPECTED_MGMT_GROUPS=(
    "$MANAGEMENT_GROUP_PREFIX"
    "$MANAGEMENT_GROUP_PREFIX-Platform"
    "$MANAGEMENT_GROUP_PREFIX-Platform-Management"
    "$MANAGEMENT_GROUP_PREFIX-Platform-Connectivity"
    "$MANAGEMENT_GROUP_PREFIX-LandingZones"
    "$MANAGEMENT_GROUP_PREFIX-lz-prd"
    "$MANAGEMENT_GROUP_PREFIX-lz-non-prd"
    "$MANAGEMENT_GROUP_PREFIX-lz-prd-legacy"
    "$MANAGEMENT_GROUP_PREFIX-lz-prd-microwave"
    "$MANAGEMENT_GROUP_PREFIX-lz-non-prd-uat"
    "$MANAGEMENT_GROUP_PREFIX-lz-non-prd-dev"
    "$MANAGEMENT_GROUP_PREFIX-Decommissioned"
    "$MANAGEMENT_GROUP_PREFIX-Sandbox"
)

echo ""
echo "Validating Management Groups..."
MGMT_GROUPS_VALID=true

for mg_name in "${EXPECTED_MGMT_GROUPS[@]}"; do
    if az account management-group show --name "$mg_name" &> /dev/null; then
        echo "✓ Management Group '$mg_name' exists"
    else
        echo "✗ Management Group '$mg_name' not found"
        MGMT_GROUPS_VALID=false
    fi
done

# Validate policy definitions
echo ""
echo "Validating Policy Definitions..."
EXPECTED_POLICIES=(
    "Audit-Tags-Mandatory-Rg"
    "Audit-PrivateLinkDnsZones"
    "Deploy-Sql-SecurityAlertPolicies"
)

POLICIES_VALID=true

for policy_name in "${EXPECTED_POLICIES[@]}"; do
    if az policy definition show --name "$policy_name" --management-group "$MANAGEMENT_GROUP_PREFIX" &> /dev/null; then
        echo "✓ Policy Definition '$policy_name' exists"
    else
        echo "✗ Policy Definition '$policy_name' not found"
        POLICIES_VALID=false
    fi
done

# Validate policy set definitions (initiatives)
echo ""
echo "Validating Policy Set Definitions..."
EXPECTED_POLICY_SETS=(
    "Enforce-ALZ-Sandbox"
    "Enforce-Guardrails-Compute"
)

POLICY_SETS_VALID=true

for policy_set_name in "${EXPECTED_POLICY_SETS[@]}"; do
    if az policy set-definition show --name "$policy_set_name" --management-group "$MANAGEMENT_GROUP_PREFIX" &> /dev/null; then
        echo "✓ Policy Set Definition '$policy_set_name' exists"
    else
        echo "✗ Policy Set Definition '$policy_set_name' not found"
        POLICY_SETS_VALID=false
    fi
done

# Validate policy assignments
echo ""
echo "Validating Policy Assignments..."

# Define expected assignments with their scopes
declare -A EXPECTED_ASSIGNMENTS
EXPECTED_ASSIGNMENTS["Audit-Tags-RG"]="/providers/Microsoft.Management/managementGroups/$MANAGEMENT_GROUP_PREFIX"
EXPECTED_ASSIGNMENTS["Audit-PrivateDns-Zones"]="/providers/Microsoft.Management/managementGroups/$MANAGEMENT_GROUP_PREFIX-LandingZones"
EXPECTED_ASSIGNMENTS["Deploy-SQL-Security"]="/providers/Microsoft.Management/managementGroups/$MANAGEMENT_GROUP_PREFIX-lz-prd"
EXPECTED_ASSIGNMENTS["Enforce-Sandbox-Policies"]="/providers/Microsoft.Management/managementGroups/$MANAGEMENT_GROUP_PREFIX-Sandbox"
EXPECTED_ASSIGNMENTS["Enforce-Compute-Guardrails"]="/providers/Microsoft.Management/managementGroups/$MANAGEMENT_GROUP_PREFIX-lz-prd"

ASSIGNMENTS_VALID=true

for assignment_name in "${!EXPECTED_ASSIGNMENTS[@]}"; do
    scope="${EXPECTED_ASSIGNMENTS[$assignment_name]}"
    if az policy assignment show --name "$assignment_name" --scope "$scope" &> /dev/null; then
        echo "✓ Policy Assignment '$assignment_name' exists at scope '$scope'"
    else
        echo "✗ Policy Assignment '$assignment_name' not found at scope '$scope'"
        ASSIGNMENTS_VALID=false
    fi
done

# Summary
echo ""
echo "============================================================"
echo "VALIDATION SUMMARY"
echo "============================================================"

if [[ "$MGMT_GROUPS_VALID" == "true" ]]; then
    echo "✓ Management Groups: PASSED"
else
    echo "✗ Management Groups: FAILED"
fi

if [[ "$POLICIES_VALID" == "true" ]]; then
    echo "✓ Policy Definitions: PASSED"
else
    echo "✗ Policy Definitions: FAILED"
fi

if [[ "$POLICY_SETS_VALID" == "true" ]]; then
    echo "✓ Policy Set Definitions: PASSED"
else
    echo "✗ Policy Set Definitions: FAILED"
fi

if [[ "$ASSIGNMENTS_VALID" == "true" ]]; then
    echo "✓ Policy Assignments: PASSED"
else
    echo "✗ Policy Assignments: FAILED"
fi

OVERALL_VALID=true
if [[ "$MGMT_GROUPS_VALID" != "true" ]] || [[ "$POLICIES_VALID" != "true" ]] || [[ "$POLICY_SETS_VALID" != "true" ]] || [[ "$ASSIGNMENTS_VALID" != "true" ]]; then
    OVERALL_VALID=false
fi

echo ""
echo -n "OVERALL VALIDATION: "
if [[ "$OVERALL_VALID" == "true" ]]; then
    echo "PASSED"
    echo "All components have been successfully deployed and validated!"
else
    echo "FAILED"
    echo "Some components are missing or incorrectly configured. Please review the errors above."
    exit 1
fi

echo ""
echo "Validation completed!"
