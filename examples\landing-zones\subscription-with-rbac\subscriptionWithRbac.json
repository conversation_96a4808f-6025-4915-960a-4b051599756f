{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "enterpriseScaleCompanyPrefix": {
            "type": "string",
            "maxLength": 10,
            "metadata": {
                "description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale."
            }
        },
        "subscriptionAliasName": {
            "type": "string",
            "metadata": {
                "description": "Provide alias (and displayName) for the subscription"
            }
        },
        "targetManagementGroup": {
            "type": "string",
            "metadata": {
                "details": "Select targeted management group that the subscription will land into"
            }
        },
        "billingAccountId": {
            "type": "string",
            "metadata": {
                "description": "Provide the resourceId for the enrollment account or MCA"
            }
        },
        "principalId": {
            "type": "array",
            "metadata": {
                "description": "Provide principalId for the user/group/service principal that should be granted access"
            }
        }
    },
    "variables": {
        "targetManagementGroup": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-', parameters('targetManagementGroup'))]"
    },
    "resources": [
        {
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[concat('create-', parameters('subscriptionAliasName'))]",
            "scope": "[concat('Microsoft.Management/managementGroups/', variables('targetManagementGroup'))]",
            "location": "[deployment().location]",
            "properties": {
                "mode": "Incremental",
                "expressionEvaluationOptions": {
                    "scope": "inner"
                },
                "parameters": {
                    // Sharing parameter values from outer to inner execution scope
                    "subAliasName": {
                        "value": "[parameters('subscriptionAliasName')]"
                    },
                    "billingId": {
                        "value": "[parameters('billingAccountId')]"
                    }
                },
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        // parameters for inner scope
                        "subAliasName": {
                            "type": "string"
                        },
                        "billingId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "scope": "/", // routing the request to tenant root
                            "name": "[parameters('subAliasName')]",
                            "type": "Microsoft.Subscription/aliases",
                            "apiVersion": "2020-09-01",
                            "properties": {
                                "workLoad": "Production",
                                "displayName": "[parameters('subAliasName')]",
                                "billingScope": "[parameters('billingId')]"
                            }
                        }
                    ],
                    "outputs": {
                        // Referencing the guid generated for the subscription to be used in subsequent (optional) deployments to this subscription
                        "subscriptionId": {
                            "type": "string",
                            "value": "[reference(parameters('subAliasName')).subscriptionId]"
                        }
                    }
                }
            }
        },
        {
            // Creating deployment to place the new/existing subscription into the management group
            "scope": "[concat('Microsoft.Management/managementGroups/', variables('targetManagementGroup'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-08-01",
            "name": "[concat('mg-', parameters('subscriptionAliasName'))]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[concat('Microsoft.Resources/deployments/', 'create-', parameters('subscriptionAliasName'))]"
            ],
            "properties": {
                "mode": "Incremental",
                "expressionEvaluationOptions": {
                    "scope": "inner"
                },
                "parameters": {
                    // Value coming from the previous deployment's output inner scope to be used to target subscription deployments
                    "targetSubscriptionId": {
                        "value": "[reference(concat('create-', parameters('subscriptionAliasName'))).outputs.subscriptionId.value]"
                    },
                    "mgmtGroupId": {
                        "value": "[variables('targetManagementGroup')]"
                    }
                },
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                         // parameters for inner scope
                        "targetSubscriptionId": {
                            "type": "string"
                        },
                        "mgmtGroupId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            // Place subscription into the management group
                            "scope": "/",
                            "type": "Microsoft.Management/managementGroups/subscriptions",
                            "apiVersion": "2020-10-01",
                            "name": "[concat(parameters('mgmtGroupId'), '/', parameters('targetSubscriptionId'))]",
                            "location": "[deployment().location]",
                            "properties": {}
                        }
                    ]
                }
            }
        },
        {
            // Creating deployment to invoke policyAssignment of newly created subscription
            "scope": "[concat('Microsoft.Management/managementGroups/', variables('targetManagementGroup'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-08-01",
            "name": "[concat('rbac-', parameters('subscriptionAliasName'))]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[concat('Microsoft.Resources/deployments/', 'create-', parameters('subscriptionAliasName'))]"
            ],
            "properties": {
                "mode": "Incremental",
                "expressionEvaluationOptions": {
                    "scope": "inner"
                },
                "parameters": {
                    // Value coming from the previous deployment's output inner scope to be used to target subscription deployments
                    "targetSubscriptionId": {
                        "value": "[reference(concat('create-', parameters('subscriptionAliasName'))).outputs.subscriptionId.value]"
                    },
                    "principalIdInner": {
                        "value": "[array(parameters('principalId'))]"
                    }
                },
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                         // parameters for inner scope
                        "targetSubscriptionId": {
                            "type": "string"
                        },
                        "principalIdInner": {
                            "type": "array"
                        }
                    },
                    "resources": [
                        {
                            // subscription scoped deployment to make policy assignment
                            "type": "Microsoft.Resources/deployments",
                            "apiVersion": "2019-08-01",
                            "name": "subscription-dep",
                            "location": "[deployment().location]",
                            "subscriptionId": "[parameters('targetSubscriptionId')]",
                            "properties": {
                                "mode": "Incremental",
                                "expressionEvaluationOptions": {
                                    "scope": "inner"
                                },
                                "parameters": {
                                    "principalIdInputInner": {
                                        "value": "[parameters('principalIdInner')]"
                                    }
                                },
                                "template": {
                                    "$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#",
                                    "contentVersion": "*******",
                                    "parameters": {
                                         // parameters for inner scope
                                        "principalIdInputInner": {
                                            "type": "array"
                                        }
                                    },
                                    "variables": {},
                                    "resources": [
                                        {
                                            "type": "Microsoft.Authorization/roleAssignments",
                                            "apiVersion": "2019-04-01-preview",
                                            "name": "[guid(subscription().subscriptionId)]",
                                            "properties": {
                                                "roleDefinitionId": "/providers/Microsoft.Authorization/roleDefinitions/8e3af657-a8ff-443c-a75c-2fe8c4bcb635",
                                                "principalId": "[replace(replace(replace(string(parameters('principalIdInputInner')), '\"', ''), '[', ''), ']', '')]"
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    ]
                }
            }
        }      
    ],
    "outputs": {
        "subscriptionIdOuter": {
            "type": "string",
            "value": "[reference(concat('create-', parameters('subscriptionAliasName'))).outputs.subscriptionId.value]"
        }
    }
}