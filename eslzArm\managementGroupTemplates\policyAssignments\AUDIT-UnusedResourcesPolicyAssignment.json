{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}, "effectDisks": {"type": "string", "allowedValues": ["Disabled", "Audit"], "defaultValue": "Audit"}, "effectPublicIpAddresses": {"type": "string", "allowedValues": ["Disabled", "Audit"], "defaultValue": "Audit"}, "effectServerFarms": {"type": "string", "allowedValues": ["Disabled", "Audit"], "defaultValue": "Audit"}}, "variables": {"policyDefinitions": {"auditCostOptimization": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Audit-UnusedResourcesCostOptimization')]"}, "policyAssignmentNames": {"costOptimization": "Audit-UnusedResources", "description": "This Policy initiative is a group of Policy definitions that help optimize cost by detecting unused but chargeable resources. Leverage this Policy initiative as a cost control to reveal orphaned resources that are driving cost.", "displayName": "Unused resources driving cost should be avoided"}, "nonComplianceMessage": {"message": "Unused resources driving cost {enforcementMode} be avoided.", "Default": "must", "DoNotEnforce": "should"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentNames').costOptimization]", "location": "[deployment().location]", "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').auditCostOptimization]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {"EffectDisks": {"value": "[parameters('effectDisks')]"}, "EffectPublicIpAddresses": {"value": "[parameters('effectPublicIpAddresses')]"}, "EffectServerFarms": {"value": "[parameters('effectServerFarms')]"}}}}], "outputs": {}}