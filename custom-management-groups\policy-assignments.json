{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"description": "Assigns policies to management groups"}, "parameters": {"managementGroups": {"type": "object", "metadata": {"description": "Object containing all management group names"}}, "enforcementMode": {"type": "string", "defaultValue": "<PERSON><PERSON><PERSON>", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "metadata": {"description": "Policy enforcement mode"}}}, "variables": {"policyAssignmentNames": {"auditTagsRg": "Audit-Tags-RG", "auditPrivateDns": "Audit-PrivateDns-Zones", "deploySqlSecurity": "Deploy-SQL-Security", "enforceSandbox": "Enforce-Sandbox-Policies", "enforceCompute": "Enforce-Compute-Guardrails"}, "policyDefinitionIds": {"auditTagsRg": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('managementGroups').root, '/providers/Microsoft.Authorization/policyDefinitions/Audit-Tags-Mandatory-Rg')]", "auditPrivateDns": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('managementGroups').root, '/providers/Microsoft.Authorization/policyDefinitions/Audit-PrivateLinkDnsZones')]", "deploySqlSecurity": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('managementGroups').root, '/providers/Microsoft.Authorization/policyDefinitions/Deploy-Sql-SecurityAlertPolicies')]", "enforceSandbox": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('managementGroups').root, '/providers/Microsoft.Authorization/policySetDefinitions/Enforce-ALZ-Sandbox')]", "enforceCompute": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('managementGroups').root, '/providers/Microsoft.Authorization/policySetDefinitions/Enforce-Guardrails-Compute')]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').auditTagsRg]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').root)]", "properties": {"description": "Audit mandatory tags on resource groups across all environments", "displayName": "Audit mandatory tags on resource groups", "policyDefinitionId": "[variables('policyDefinitionIds').auditTagsRg]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {"tagNames": {"value": ["Environment", "CostCenter", "Owner"]}, "effect": {"value": "Audit"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').auditPrivateDns]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').landingZones)]", "properties": {"description": "Audit creation of Private Link DNS Zones in Landing Zones", "displayName": "Audit Private Link DNS Zones", "policyDefinitionId": "[variables('policyDefinitionIds').auditPrivateDns]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {"effect": {"value": "Audit"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').deploySqlSecurity]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').lzPrd)]", "properties": {"description": "Deploy SQL Security Alert Policies in Production environments", "displayName": "Deploy SQL Security Alert Policies", "policyDefinitionId": "[variables('policyDefinitionIds').deploySqlSecurity]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {"effect": {"value": "DeployIfNotExists"}, "emailAddresses": {"value": ["<EMAIL>"]}}, "identity": {"type": "SystemAssigned"}}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[guid(concat(parameters('managementGroups').lzPrd, variables('policyAssignmentNames').deploySqlSecurity))]", "dependsOn": ["[resourceId('Microsoft.Authorization/policyAssignments', variables('policyAssignmentNames').deploySqlSecurity)]"], "properties": {"roleDefinitionId": "/providers/Microsoft.Authorization/roleDefinitions/056cd41c-7e88-42e1-933e-88ba6a50c9c3", "principalId": "[reference(resourceId('Microsoft.Authorization/policyAssignments', variables('policyAssignmentNames').deploySqlSecurity), '2024-04-01', 'Full').identity.principalId]", "principalType": "ServicePrincipal"}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').enforceSandbox]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').sandbox)]", "properties": {"description": "Enforce sandbox policies to limit resource types and prevent VNet peering", "displayName": "Enforce Sandbox Policies", "policyDefinitionId": "[variables('policyDefinitionIds').enforceSandbox]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {"listOfResourceTypesNotAllowed": {"value": ["Microsoft.Compute/virtualMachines", "Microsoft.Network/expressRouteCircuits", "Microsoft.Network/virtualNetworkGateways", "Microsoft.Network/vpnGateways"]}, "effectNotAllowedResources": {"value": "<PERSON><PERSON>"}, "effectDenyVnetPeering": {"value": "<PERSON><PERSON>"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').enforceCompute]", "scope": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('managementGroups').lzPrd)]", "properties": {"description": "Enforce compute guardrails for production workloads", "displayName": "Enforce Compute Guardrails", "policyDefinitionId": "[variables('policyDefinitionIds').enforceCompute]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {"effectVmBackup": {"value": "AuditIfNotExists"}}}}]}