#!/bin/bash

# Custom Management Group Deployment Script
# This script deploys the custom management group structure with Azure Landing Zone policies

set -e

# Default parameters
MANAGEMENT_GROUP_PREFIX="EWH"
LOCATION="East US"
ENABLE_POLICY_DEPLOYMENT=true
ENFORCEMENT_MODE="Default"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --prefix)
            MANAGEMENT_GROUP_PREFIX="$2"
            shift 2
            ;;
        --location)
            LOCATION="$2"
            shift 2
            ;;
        --no-policies)
            ENABLE_POLICY_DEPLOYMENT=false
            shift
            ;;
        --enforcement-mode)
            ENFORCEMENT_MODE="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --prefix PREFIX           Management group prefix (default: EWH)"
            echo "  --location LOCATION       Azure location (default: East US)"
            echo "  --no-policies            Skip policy deployment"
            echo "  --enforcement-mode MODE   Policy enforcement mode (Default/DoNotEnforce)"
            echo "  -h, --help               Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "Starting Custom Management Group Deployment..."
echo "Management Group Prefix: $MANAGEMENT_GROUP_PREFIX"
echo "Location: $LOCATION"
echo "Enable Policy Deployment: $ENABLE_POLICY_DEPLOYMENT"

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo "Azure CLI is not installed. Please install it first."
    exit 1
fi

# Check if user is logged in
if ! az account show &> /dev/null; then
    echo "Please login to Azure first using 'az login'"
    exit 1
fi

echo "Current Azure Account: $(az account show --query user.name -o tsv)"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEMPLATE_PATH="$SCRIPT_DIR/main-template.json"
PARAMETERS_PATH="$SCRIPT_DIR/parameters/main.parameters.json"

# Check if template files exist
if [[ ! -f "$TEMPLATE_PATH" ]]; then
    echo "Template file not found: $TEMPLATE_PATH"
    exit 1
fi

if [[ ! -f "$PARAMETERS_PATH" ]]; then
    echo "Parameters file not found: $PARAMETERS_PATH"
    exit 1
fi

# Create deployment name
DEPLOYMENT_NAME="CustomMgmtGroups-$(date +%Y%m%d-%H%M%S)"

echo "Deploying management groups..."
echo "Deployment Name: $DEPLOYMENT_NAME"

# Deploy the template
az deployment tenant create \
    --name "$DEPLOYMENT_NAME" \
    --location "$LOCATION" \
    --template-file "$TEMPLATE_PATH" \
    --parameters "@$PARAMETERS_PATH" \
    --parameters topLevelManagementGroupPrefix="$MANAGEMENT_GROUP_PREFIX" \
    --parameters enablePolicyDeployment="$ENABLE_POLICY_DEPLOYMENT" \
    --verbose

if [[ $? -eq 0 ]]; then
    echo "Deployment completed successfully!"
    
    # Display management group structure
    echo ""
    echo "Management Group Structure Created:"
    echo "└── $MANAGEMENT_GROUP_PREFIX (EWH - Enterprise-Wide Hub)"
    echo "    ├── $MANAGEMENT_GROUP_PREFIX-Platform"
    echo "    │   ├── $MANAGEMENT_GROUP_PREFIX-Platform-Management"
    echo "    │   └── $MANAGEMENT_GROUP_PREFIX-Platform-Connectivity"
    echo "    ├── $MANAGEMENT_GROUP_PREFIX-LandingZones"
    echo "    │   ├── $MANAGEMENT_GROUP_PREFIX-lz-prd"
    echo "    │   │   ├── $MANAGEMENT_GROUP_PREFIX-lz-prd-legacy"
    echo "    │   │   └── $MANAGEMENT_GROUP_PREFIX-lz-prd-microwave"
    echo "    │   └── $MANAGEMENT_GROUP_PREFIX-lz-non-prd"
    echo "    │       ├── $MANAGEMENT_GROUP_PREFIX-lz-non-prd-uat"
    echo "    │       └── $MANAGEMENT_GROUP_PREFIX-lz-non-prd-dev"
    echo "    ├── $MANAGEMENT_GROUP_PREFIX-Decommissioned"
    echo "    └── $MANAGEMENT_GROUP_PREFIX-Sandbox"
    
    if [[ "$ENABLE_POLICY_DEPLOYMENT" == "true" ]]; then
        echo ""
        echo "Policies Deployed:"
        echo "✓ Audit mandatory tags on resource groups"
        echo "✓ Audit Private Link DNS Zones"
        echo "✓ Deploy SQL Security Alert Policies"
        echo "✓ Enforce Sandbox Policies"
        echo "✓ Enforce Compute Guardrails"
    fi
    
    echo ""
    echo "Next Steps:"
    echo "1. Move subscriptions to appropriate management groups"
    echo "2. Review and adjust policy assignments as needed"
    echo "3. Configure additional policies based on requirements"
    
else
    echo "Deployment failed!"
    exit 1
fi

echo ""
echo "Deployment completed!"
