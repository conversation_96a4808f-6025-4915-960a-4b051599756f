{"name": "Deny-KeyVaultPaasPublicIP", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Preview: Azure Key Vault should disable public network access", "description": "Disable public network access for your key vault so that it's not accessible over the public internet. This can reduce data leakage risks. Learn more at: https://aka.ms/akvprivatelink.", "metadata": {"version": "2.0.0-preview", "category": "<PERSON>", "source": "https://github.com/Azure/Enterprise-Scale/", "preview": true, "alzCloudEnvironments": ["AzureChinaCloud"]}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.KeyVault/vaults"}, {"not": {"field": "Microsoft.KeyVault/vaults/createMode", "equals": "recover"}}, {"field": "Microsoft.KeyVault/vaults/networkAcls.defaultAction", "notEquals": "<PERSON><PERSON>"}]}, "then": {"effect": "[[parameters('effect')]"}}}}