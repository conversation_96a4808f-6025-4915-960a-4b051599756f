---
name: Update ALZ Tools

##########################################
# Start the job on push for all branches #
##########################################

# yamllint disable-line rule:truthy
on:
  schedule:
    - cron: "0 5 * * 0"
  workflow_dispatch: {}

env:
  github_user_name: "github-actions"
  github_email: "41898282+github-actions[bot]@users.noreply.github.com"
  github_commit_message: "Auto-update ALZ.Tools"
  pr_branch_name: "patch-alz-tools"
  pr_title: "Update ALZ.Tools (automated)"
  pr_body:
    "This is an automated 'pull_request' containing updates to the ALZ.Tools stored in 'src/ALZ.Tools'.\n
    Please review the 'files changed' tab to review changes."

permissions:
  contents: write
  id-token: write
  pull-requests: write

###############
# Set the Job #
###############
jobs:
  update-alz-tools:
    name: Update ALZ Tools
    runs-on: ubuntu-latest
    environment: csu-ro

    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Configure local git
        run: |
          git config --global user.name "$github_user_name"
          git config --global user.email "$github_email"

      - name: Azure login
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
          enable-AzPSSession: true

      - name: Create and checkout branch
        run: |
          BRANCH_URL="repos/${{ github.repository }}/branches"
          JQ_FILTER=".[] | select(.name == \"${{ env.pr_branch_name }}\").name"
          CHECK_BRANCH_ORIGIN=$(gh api $BRANCH_URL | jq -r "$JQ_FILTER")
          if [ -z "$CHECK_BRANCH_ORIGIN" ]
          then
            echo "Checkout local branch (create new, no origin)..."
            git checkout -b ${{ env.pr_branch_name }}
          else
            echo "Checkout local branch (create new, track from origin)..."
            git checkout -b ${{ env.pr_branch_name }} --track origin/${{ env.pr_branch_name }}
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.github_token }}

      - name: Update ProviderApiVersions
        uses: azure/powershell@v2
        with:
          inlineScript: src/Alz.Tools/scripts/Update-ProviderApiVersionsZip.ps1
          azPSVersion: "latest"

      - name: Check for changes
        id: git_status
        run: |
          mapfile -t CHECK_GIT_STATUS < <(git status -s)
          printf "%s\n" "${CHECK_GIT_STATUS[@]}"
          echo "changes=${#CHECK_GIT_STATUS[@]}" >> "$GITHUB_OUTPUT"

      - name: Add files, commit and push
        if: steps.git_status.outputs.changes > 0
        run: |
          echo "Pushing changes to origin..."
          git add --all ./src/Alz.Tools/
          git commit -m "$github_commit_message [$GITHUB_ACTOR/${GITHUB_SHA::8}]"
          git push origin ${{ env.pr_branch_name }}

      - name: Create pull request
        if: steps.git_status.outputs.changes > 0
        run: |
          HEAD_LABEL="${{ github.repository_owner }}:${{ env.pr_branch_name }}"
          BASE_LABEL="${{ github.repository_owner }}:$(echo '${{ github.ref }}' | sed 's:refs/heads/::')"
          PULL_REQUEST_URL="repos/${{ github.repository }}/pulls"
          JQ_FILTER=".[] | select(.head.label == \"$HEAD_LABEL\") | select(.base.label == \"$BASE_LABEL\") | .url"
          CHECK_PULL_REQUEST_URL=$(gh api $PULL_REQUEST_URL | jq -r "$JQ_FILTER")
          if [ -z "$CHECK_PULL_REQUEST_URL" ]
          then
            CHECK_PULL_REQUEST_URL=$(gh pr create \
            --title "${{ env.pr_title }}" \
            --body "${{ env.pr_body }}" \
            --base "${{ github.ref }}" \
            --head "${{ env.pr_branch_name }}" \
            --draft)
            echo "Created new PR: $CHECK_PULL_REQUEST_URL"
          else
            echo "Existing PR found: $CHECK_PULL_REQUEST_URL"
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.github_token }}
