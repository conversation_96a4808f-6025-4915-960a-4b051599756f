{"name": "Enforce-Guardrails-Network_20250326", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Network and Networking services", "description": "This policy initiative is a group of policies that ensures Network and Networking services are compliant per regulated Landing Zones.", "metadata": {"version": "2.0.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "replacesPolicy": "Enforce-Guardrails-Network", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"subnetUdr": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "subnetNsg": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "subnetServiceEndpoint": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "appGwWaf": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "vnetModifyDdos": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Audit", "Modify", "Disabled"]}, "ddosPlanResourceId": {"type": "string", "defaultValue": ""}, "wafMode": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "wafModeRequirement": {"type": "string", "defaultValue": "Prevention"}, "wafFwRules": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "wafModeAppGw": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "wafModeAppGwRequirement": {"type": "string", "defaultValue": "Prevention"}, "denyMgmtFromInternet": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "denyMgmtFromInternetPorts": {"type": "Array", "metadata": {"displayName": "Ports", "description": "Ports to be blocked"}, "defaultValue": ["22", "3389"]}, "wafAfdEnabled": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "vpnAzureAD": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "appGwTlsVersion": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "modifyUdr": {"type": "string", "defaultValue": "Disabled"}, "modifyUdrNextHopIpAddress": {"type": "string", "defaultValue": ""}, "modifyUdrNextHopType": {"type": "string", "defaultValue": "None"}, "modifyUdrAddressPrefix": {"type": "string", "defaultValue": "0.0.0.0/0"}, "modifyNsg": {"type": "string", "defaultValue": "Disabled", "allowedValues": ["Modify", "Disabled"]}, "modifyNsgRuleName": {"type": "string", "defaultValue": "DenyAnyInternetOutbound"}, "modifyNsgRulePriority": {"type": "integer", "defaultValue": 1000}, "modifyNsgRuleDirection": {"type": "string", "defaultValue": "Outbound"}, "modifyNsgRuleAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Allow", "<PERSON><PERSON>"]}, "modifyNsgRuleProtocol": {"type": "string", "defaultValue": "*"}, "modifyNsgRuleSourceAddressPrefix": {"type": "string", "defaultValue": "*"}, "modifyNsgRuleSourcePortRange": {"type": "string", "defaultValue": "*"}, "modifyNsgRuleDestinationAddressPrefix": {"type": "string", "defaultValue": "Internet"}, "modifyNsgRuleDestinationPortRange": {"type": "string", "defaultValue": "*"}, "modifyNsgRuleDescription": {"type": "string", "defaultValue": "Deny any outbound traffic to the Internet"}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/35f9c03a-cc27-418e-9c0c-539ff999d010", "policyDefinitionReferenceId": "Deny-Nsg-GW-subnet", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/21a6bc25-125e-4d13-b82d-2e19b7208ab7", "policyDefinitionReferenceId": "Deny-VPN-AzureAD", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('vpnAzureAD')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/055aa869-bc98-4af8-bafc-23f1ab6ffe2c", "policyDefinitionReferenceId": "Deny-Waf-Afd-Enabled", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('wafAfdEnabled')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/12430be1-6cc8-4527-a9a8-e3d38f250096", "policyDefinitionReferenceId": "Deny-Waf-AppGw-mode", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('wafModeAppGw')]"}, "modeRequirement": {"value": "[[parameters('wafModeAppGwRequirement')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/564feb30-bf6a-4854-b4bb-0d2d2d1e6c66", "policyDefinitionReferenceId": "Deny-Waf-Fw-rules", "definitionVersion": "2.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('wafFwRules')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/425bea59-a659-4cbb-8d31-34499bd030b8", "policyDefinitionReferenceId": "Deny-Waf-mode", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('wafMode')]"}, "modeRequirement": {"value": "[[parameters('wafModeRequirement')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/94de2ad3-e0c1-4caf-ad78-5d47bbc83d3d", "policyDefinitionReferenceId": "Modify-vNet-DDoS", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('vnetModifyDdos')]"}, "ddosPlan": {"value": "[[parameters('ddosPlanResourceId')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/88c0b9da-ce96-4b03-9635-f29a937e2900", "policyDefinitionReferenceId": "Deny-Ip-Forwarding", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/83a86a26-fd1f-447c-b59d-e51f44264114", "policyDefinitionReferenceId": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/564feb30-bf6a-4854-b4bb-0d2d2d1e6c66", "policyDefinitionReferenceId": "Deny-AppGw-Without-Waf", "definitionVersion": "2.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appGwWaf')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Subnet-Without-Udr", "policyDefinitionReferenceId": "Deny-Subnet-Without-Udr", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('subnetUdr')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Subnet-Without-Nsg", "policyDefinitionReferenceId": "Deny-Subnet-Without-NSG", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('subnetNsg')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-Service-Endpoints", "policyDefinitionReferenceId": "Deny-Subnet-with-Service-Endpoints", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('subnetServiceEndpoint')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-MgmtPorts-From-Internet", "policyDefinitionReferenceId": "Deny-Mgmt-From-Internet", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('denyMgmtFromInternet')]"}, "ports": {"value": "[[parameters('denyMgmtFromInternetPorts')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-AppGw-Without-Tls", "policyDefinitionReferenceId": "Deny-AppGw-Without-Tls", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appGwTlsVersion')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Modify-UDR", "policyDefinitionReferenceId": "Modify-Udr", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('modifyUdr')]"}, "nextHopIpAddress": {"value": "[[parameters('modifyUdrNextHopIpAddress')]"}, "nextHopType": {"value": "[[parameters('modifyUdrNextHopType')]"}, "addressPrefix": {"value": "[[parameters('modifyUdrAddressPrefix')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Modify-NSG", "policyDefinitionReferenceId": "Modify-Nsg", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('modifyNsg')]"}, "nsgRuleName": {"value": "[[parameters('modifyNsgRuleName')]"}, "nsgRulePriority": {"value": "[[parameters('modifyNsgRulePriority')]"}, "nsgRuleDirection": {"value": "[[parameters('modifyNsgRuleDirection')]"}, "nsgRuleAccess": {"value": "[[parameters('modifyNsgRuleAccess')]"}, "nsgRuleProtocol": {"value": "[[parameters('modifyNsgRuleProtocol')]"}, "nsgRuleSourceAddressPrefix": {"value": "[[parameters('modifyNsgRuleSourceAddressPrefix')]"}, "nsgRuleSourcePortRange": {"value": "[[parameters('modifyNsgRuleSourcePortRange')]"}, "nsgRuleDestinationAddressPrefix": {"value": "[[parameters('modifyNsgRuleDestinationAddressPrefix')]"}, "nsgRuleDestinationPortRange": {"value": "[[parameters('modifyNsgRuleDestinationPortRange')]"}, "nsgRuleDescription": {"value": "[[parameters('modifyNsgRuleDescription')]"}}}], "policyDefinitionGroups": null}}