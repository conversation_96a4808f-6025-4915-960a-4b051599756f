{"$schema": "<relative path to createFormUI.schema.json>", "view": {"kind": "Form", "properties": {"title": "Azure landing zone accelerator", "steps": [{"name": "basics", "label": "Deployment location", "elements": [{"name": "resourceScope", "type": "Microsoft.Common.ResourceScope"}]}, {"name": "lzSettings", "label": "Azure core setup", "subLabel": {"preValidation": "Provide a company prefix for the management group structure that will be created.", "postValidation": "Done"}, "bladeTitle": "Company prefix", "elements": [{"name": "info", "type": "Microsoft.Common.InfoBox", "visible": true, "options": {"text": "Azure Landing Zones ARM deployment requires access at the tenant root (/) scope. Visit this link to ensure you have the appropriate RBAC permission to complete the deployment", "uri": "https://docs.microsoft.com/azure/role-based-access-control/elevate-access-global-admin", "style": "Info"}}, {"name": "mgmtGroup", "type": "Microsoft.Common.TextBlock", "visible": true, "options": {"text": "Azure Landing Zones will create the management group hierarchy under the Tenant Root Group with the prefix provided at this step.", "link": {"label": "Learn more", "uri": "https://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/management-group-and-subscription-organization"}}}, {"name": "esMgmtGroup", "type": "Microsoft.Common.TextBox", "label": "Management Group prefix", "toolTip": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale.", "defaultValue": "", "constraints": {"required": true, "regex": "^[a-z0-9A-Z-]{1,10}$", "validationMessage": "The prefix must be 1-10 characters."}}, {"name": "subOrgsOption", "type": "Microsoft.Common.OptionsGroup", "label": "Select dedicated subscriptions or single subscription for platform resources", "defaultValue": "Dedicated (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continious compliance.", "constraints": {"allowedValues": [{"label": "Dedicated (recommended)", "value": "Dedicated"}, {"label": "Single", "value": "Single"}]}, "visible": true}, {"name": "esSingleSubSection", "type": "Microsoft.Common.Section", "label": "Single platform subscription", "elements": [{"name": "subWarning", "type": "Microsoft.Common.InfoBox", "visible": true, "options": {"icon": "Warning", "text": "Dedicated subscriptions are recommended for the various platform components to ensure scale, sustainability, and segregation of duties. However, a single subscription can also be used in case this is not a concern (e.g., small enterprises).", "uri": "https://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/management-group-and-subscription-organization"}}, {"name": "singleSubText", "type": "Microsoft.Common.TextBlock", "visible": true, "options": {"text": "Select the dedicated, single subscription that will be used for all platform resources during deployment, for security, logging, connectivity, and identity."}}, {"type": "Microsoft.Common.SubscriptionSelector", "name": "esSingleSub", "label": "Single platform subscription"}], "visible": "[equals(steps('lzSettings').subOrgsOption, 'Single')]"}, {"name": "denyClassicResources", "type": "Microsoft.Common.OptionsGroup", "label": "Prevent the deployment of classic resources", "defaultValue": "Yes (recommended)", "visible": true, "toolTip": "If 'Yes' is selected then Azure Policy will prevent deployment of classic resources.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "Audit only", "value": "Audit"}, {"label": "No", "value": "No"}]}}]}, {"name": "esGoalState", "label": "Platform management, security, and governance", "subLabel": {"preValidation": "", "postValidation": ""}, "bladeTitle": "lzGs", "elements": [{"name": "multiPlatformMgmtSub", "type": "Microsoft.Common.InfoBox", "visible": "[not(equals(steps('lzSettings').subOrgsOption, 'Single'))]", "options": {"text": "To enable platform management, security and governance, you must allocate a management Subscription. Please note, this Subscription will be moved to the platform Management Group, and ARM will deploy a Log Analytics workspace and requisite settings. We recommend using a new Subscription with no existing resources. Note that Azure Policy will be used to govern the configuration for the platform at scale.", "uri": "https://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/management-and-monitoring", "style": "Info"}}, {"name": "singlePlatformMgmtSub", "type": "Microsoft.Common.InfoBox", "visible": "[equals(steps('lzSettings').subOrgsOption, 'Single')]", "options": {"text": "To enable platform management, security and governance, you can configure core infra such as Log Analytics, Azure Security Center and additional monitoring solutions to your dedicated platform subscription. Note that Azure Policy will be used to govern the configuration for the platform at scale.", "uri": "https://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/management-and-monitoring", "style": "Info"}}, {"name": "esLogAnalytics", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy Log Analytics workspace and enable monitoring for your platform and resources", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continious compliance.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esLogRetention", "type": "Microsoft.Common.Slider", "min": 30, "max": 730, "label": "Log Analytics Data Retention (days)", "subLabel": "Days", "defaultValue": 30, "showStepMarkers": false, "toolTip": "Select retention days for Azure logs. Default is 30 days.", "constraints": {"required": false}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}, {"name": "esMgmtSubSection", "type": "Microsoft.Common.Section", "label": "Management subscription", "elements": [{"name": "esMgmtSubUniqueWarning", "type": "Microsoft.Common.InfoBox", "visible": true, "options": {"text": "Ensure you select a subscription that is dedicated for Management. Selecting the same Subscription here for Connectivity or Identity will result in a deployment failure. If you want to use a single Subscription for all platform resources, select 'Single' on the 'Azure Core Setup' blade.", "style": "Warning"}}, {"type": "Microsoft.Common.SubscriptionSelector", "name": "esMgmtSub", "label": "Management subscription"}], "visible": "[and(equals(steps('esGoalState').esLogAnalytics, 'Yes'), not(equals(steps('lzSettings').subOrgsOption, 'Single')))]"}, {"name": "monitoring", "type": "Microsoft.Common.TextBlock", "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]", "options": {"text": "Select which Azure Monitor solutions you will enable for your Log Analytics workspace", "link": {"label": "Learn more", "uri": "https://docs.microsoft.com/azure/azure-monitor/insights/solutions"}}}, {"name": "esAgentSolution", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy Agent Health solution", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continous compliance", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}, {"name": "esChangeTracking", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy Change Tracking solution", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continous compliance", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}, {"name": "esUpdateMgmt", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy Update Management solution", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continous compliance", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}, {"name": "esVmInsights", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy VM Insights solution", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continous compliance", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}, {"name": "esSqlAssessment", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy SQL Assessment solution", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continous compliance", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}, {"name": "esSqlVulnerabilityAssessment", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy SQL Vulnerability Assessment solution", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continous compliance", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}, {"name": "esSqlAdvancedThreatProtection", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy SQL Advanced Threat Protection solution", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continous compliance", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}, {"name": "textBlock0", "type": "Microsoft.Common.TextBlock", "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]", "options": {"text": "Select which Microsoft Defender for Cloud solutions you will enable.", "link": {"label": "Learn more", "uri": "https://docs.microsoft.com/azure/security/fundamentals/overview"}}}, {"name": "esAsc", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy Microsoft Defender for Cloud and enable security monitoring for your platform and resources", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continous compliance", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}, {"name": "esAscEmail", "type": "Microsoft.Common.TextBox", "label": "Microsoft Defender for Cloud Email Contact", "toolTip": "Email address to get email notifications from Azure Security Center", "visible": "[equals(steps('esGoalState').esAsc,'Yes')]", "defaultValue": "", "constraints": {"required": "[equals(steps('esGoalState').esAsc,'Yes')]", "regex": "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$", "validationMessage": "Please provide a valid email address"}}, {"name": "esAscVms", "type": "Microsoft.Common.OptionsGroup", "label": "Enable Microsoft Defender for Cloud for servers", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Microsoft Defender for Cloud will be enabled for all servers.", "visible": "[equals(steps('esGoalState').esAsc,'Yes')]", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "DeployIfNotExists"}, {"label": "No", "value": "Disabled"}]}}, {"name": "esAscStorage", "type": "Microsoft.Common.OptionsGroup", "label": "Enable Microsoft Defender for Cloud for Storage", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Microsoft Defender for Cloud will be enabled for Storage.", "visible": "[equals(steps('esGoalState').esAsc,'Yes')]", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "DeployIfNotExists"}, {"label": "No", "value": "Disabled"}]}}, {"name": "esAscSql", "type": "Microsoft.Common.OptionsGroup", "label": "Enable Microsoft Defender for Cloud for Azure SQL Database", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Microsoft Defender for Cloud will be enabled for Azure SQL Database.", "visible": "[equals(steps('esGoalState').esAsc,'Yes')]", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "DeployIfNotExists"}, {"label": "No", "value": "Disabled"}]}}, {"name": "esAscArm", "type": "Microsoft.Common.OptionsGroup", "label": "Enable Microsoft Defender for Cloud for Azure Resource Manager", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Microsoft Defender for Cloud will be enabled for Resource Manager.", "visible": "[equals(steps('esGoalState').esAsc,'Yes')]", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "DeployIfNotExists"}, {"label": "No", "value": "Disabled"}]}}, {"name": "esAscDns", "type": "Microsoft.Common.OptionsGroup", "label": "Enable Microsoft Defender for Cloud for DNS", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Microsoft Defender for Cloud will be enabled for DNS.", "visible": "[equals(steps('esGoalState').esAsc,'Yes')]", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "DeployIfNotExists"}, {"label": "No", "value": "Disabled"}]}}, {"name": "esAscContainers", "type": "Microsoft.Common.OptionsGroup", "label": "Enable Microsoft Defender for Cloud for Containers (Kubernetes and Container Registries)", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Microsoft Defender for Cloud will be enabled for Containers (Kubernetes and Container Registries).", "visible": "[equals(steps('esGoalState').esAsc,'Yes')]", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "DeployIfNotExists"}, {"label": "No", "value": "Disabled"}]}}, {"name": "esSecuritySolution", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy Microsoft Sentinel", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continous compliance", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}]}, {"name": "esConnectivityGoalState", "label": "Network topology and connectivity", "subLabel": {"preValidation": "", "postValidation": ""}, "bladeTitle": "lzGs", "elements": [{"name": "multiPlatformConnectivitySub", "type": "Microsoft.Common.InfoBox", "visible": "[not(equals(steps('lzSettings').subOrgsOption, 'Single'))]", "options": {"text": "To enable network topology and connectivity, you must allocate a dedicated connectivity Subscription. Please note, this Subscription will be moved to the connectivity Management Group, and ARM will deploy the first hub virtual network for either a hub and spoke or Virtual WAN  network topology. Additional networking platform resources such as gateways or Azure Firewall can be deployed. We recommend using a new dedicated Subscription with no existing resources.", "uri": "https://docs.microsoft.com/azure/cloud-adoption-framework/ready/azure-best-practices/define-an-azure-network-topology", "style": "Info"}}, {"name": "singlePlatformConnectivitySub", "type": "Microsoft.Common.InfoBox", "visible": "[equals(steps('lzSettings').subOrgsOption, 'Single')]", "options": {"text": "To enable network topology and connectivity, you can select the preferred networking topology, and deploy this into the dedicated platform subscription. Additional networking platform resources such as gateways or Azure Firewall can also be deployed.", "uri": "https://docs.microsoft.com/azure/cloud-adoption-framework/ready/azure-best-practices/define-an-azure-network-topology", "style": "Info"}}, {"name": "esHub", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy networking topology", "defaultValue": "No", "toolTip": "Select the preferred network topology. If third-party NVA is a requirement, you must deploy this into the connectivity subscription post the deployment.", "constraints": {"allowedValues": [{"label": "<PERSON><PERSON> and spoke with Azure Firewall", "value": "vhub"}, {"label": "<PERSON>b and spoke with your own third-party NVA", "value": "nva"}, {"label": "Virtual WAN (Microsoft managed)", "value": "vwan"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esNwSubSection", "type": "Microsoft.Common.Section", "label": "Connectivity subscription", "elements": [{"name": "esNwSubUniqueWarning", "type": "Microsoft.Common.InfoBox", "visible": true, "options": {"text": "Ensure you select a subscription that is dedicated for Connectivity. Selecting the same Subscription here for Management or Identity will result in a deployment failure. If you want to use a single Subscription for all platform resources, select 'Single' on the 'Azure Core Setup' blade.", "style": "Warning"}}, {"type": "Microsoft.Common.SubscriptionSelector", "name": "esNwSub", "label": "Connectivity subscription"}], "visible": "[and(not(equals(steps('esConnectivityGoalState').esHub, 'No')), not(equals(steps('lzSettings').subOrgsOption, 'Single')))]"}, {"name": "esAddressHubVWAN", "type": "Microsoft.Common.TextBox", "label": "Address space (required for vWAN hub)", "toolTip": "Provide address prefix in CIDR notation (e.g **********/23)", "defaultValue": "**********/23", "visible": "[and(not(equals(steps('esConnectivityGoalState').esHub, 'No')), not(equals(steps('esConnectivityGoalState').esHub, 'nva')), not(equals(steps('esConnectivityGoalState').esHub, 'vhub')))]", "constraints": {"required": true, "validations": [{"regex": "^(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:/(1[0-9]|2[0-4]))$", "message": "Invalid CIDR range. The address prefix must be in the range [10,24]."}]}}, {"name": "esAddressHubHS", "type": "Microsoft.Common.TextBox", "label": "Address space (required for hub virtual network)", "toolTip": "Provide address prefix in CIDR notation (e.g **********/16)", "defaultValue": "**********/16", "visible": "[and(not(equals(steps('esConnectivityGoalState').esHub, 'No')), not(equals(steps('esConnectivityGoalState').esHub, 'vwan')))]", "constraints": {"required": true, "validations": [{"regex": "^(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:/(1[0-9]|2[0-4]))$", "message": "Invalid CIDR range. The address prefix must be in the range [10,24]."}]}}, {"name": "esLocationsApi", "type": "Microsoft.Solutions.ArmApiControl", "request": {"method": "GET", "path": "locations?api-version=2019-11-01"}}, {"name": "esNwLocation", "type": "Microsoft.Common.DropDown", "label": "Region for the first networking hub", "filter": true, "toolTip": "Select the target region for you connectivity deployment (requires you to provide a subscriptionId for connectivity)", "constraints": {"allowedValues": "[map(steps('esConnectivityGoalState').esLocationsApi.value,(item) => parse(concat('{\"label\":\"',item.displayName,'\",\"value\":\"',item.name,'\"}')))]", "required": true}, "visible": "[not(equals(steps('esConnectivityGoalState').esHub, 'No'))]"}, {"name": "esDdoS", "type": "Microsoft.Common.OptionsGroup", "label": "Enable DDoS Network Protection", "defaultValue": "Yes (recommended)", "visible": "[not(equals(steps('esConnectivityGoalState').esHub, 'No'))]", "toolTip": "If 'Yes' is selected when also adding a connectivity subscription, DDoS Network Protection will be enabled.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esPrivateDns", "type": "Microsoft.Common.OptionsGroup", "label": "Create Private DNS Zones for Azure PaaS services", "defaultValue": "Yes (recommended)", "visible": "[or(equals(steps('esConnectivityGoalState').esHub, 'vhub'), equals(steps('esConnectivityGoalState').esHub, 'nva'))]", "toolTip": "If 'Yes' is selected when also adding a subscription for connectivity, ARM will create Private DNS Zones for Azure PaaS services", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esVpnGw", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy VPN Gateway", "defaultValue": "No", "visible": "[not(equals(steps('esConnectivityGoalState').esHub, 'No'))]", "toolTip": "If 'Yes' is selected when also adding a subscription for connectivity, ARM will deploy VPN gateway", "constraints": {"allowedValues": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esGwRegionalOrAz", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy zone redundant or regional VPN Gateway", "defaultValue": "Zone redundant (recommended)", "visible": "[and(and(equals(steps('esConnectivityGoalState').esVpnGw,'Yes'), not(equals(steps('esConnectivityGoalState').esHub, 'vwan'))), equals(steps('esConnectivityGoalState').esVpnGw,'Yes'),contains(split('canadacentral,centralus,eastus,eastus2,southcentralus,westus2,francecentral,germanywestcentral,northeurope,westeurope,uksouth,southafricanorth,japaneast,southeastasia,australiaeast,italynorth', ','), steps('esConnectivityGoalState').esNwLocation))]", "toolTip": "If 'Yes' is selected when also adding a subscription for connectivity, ARM will deploy Virtual Gateway to the selected region and availability zones.", "constraints": {"allowedValues": [{"label": "Zone redundant (recommended)", "value": "Zone"}, {"label": "Regional", "value": "Regional"}]}}, {"name": "esGwNoAzSku", "type": "Microsoft.Common.DropDown", "label": "Select the VPN Gateway SKU", "defaultValue": "", "multiselect": false, "selectAll": false, "filter": false, "multiLine": true, "visible": "[and(and(equals(steps('esConnectivityGoalState').esVpnGw, 'Yes'), not(equals(steps('esConnectivityGoalState').esHub, 'vwan'))), equals(steps('esConnectivityGoalState').esVpnGw,'Yes'), not(contains(split('canadacentral,centralus,eastus,eastus2,southcentralus,westus2,francecentral,germanywestcentral,northeurope,westeurope,uksouth,southafricanorth,japaneast,southeastasia,australiaeast,italynorth', ','), steps('esConnectivityGoalState').esNwLocation)))]", "toolTip": "Select the required SKU for the VPN gateway.", "constraints": {"allowedValues": [{"label": "VpnGw2", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 500 IKEv2/OpenVPN connections, aggregate throughput is 1.25 Gbps", "value": "VpnGw2"}, {"label": "VpnGw3", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 1000 IKEv2/OpenVPN connections, aggregate throughput is 2.5 Gbps", "value": "VpnGw3"}, {"label": "VpnGw4", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 5000 IKEv2/OpenVPN connections, aggregate throughput is 5 Gbps", "value": "VpnGw4"}, {"label": "VpnGw5", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 10000 IKEv2/OpenVPN connections, aggregate throughput is 10 Gbps", "value": "VpnGw5"}]}}, {"name": "esGwAzSku", "type": "Microsoft.Common.DropDown", "label": "Select the VPN Gateway SKU", "defaultValue": "", "multiselect": false, "selectAll": false, "filter": false, "multiLine": true, "visible": "[and(and(equals(steps('esConnectivityGoalState').esVpnGw, 'Yes'), not(equals(steps('esConnectivityGoalState').esHub, 'vwan'))), equals(steps('esConnectivityGoalState').esVpnGw,'Yes'), equals(steps('esConnectivityGoalState').esGwRegionalOrAz, 'Zone') ,contains(split('canadacentral,centralus,eastus,eastus2,southcentralus,westus2,francecentral,germanywestcentral,northeurope,westeurope,uksouth,southafricanorth,japaneast,southeastasia,australiaeast,italynorth', ','), steps('esConnectivityGoalState').esNwLocation))]", "toolTip": "Select the required SKU for the VPN gateway.", "constraints": {"allowedValues": [{"label": "VpnGw2AZ", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 500 IKEv2/OpenVPN connections, aggregate throughput is 1.25 Gbps", "value": "VpnGw2AZ"}, {"label": "VpnGw3AZ", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 1000 IKEv2/OpenVPN connections, aggregate throughput is 2.5 Gbps", "value": "VpnGw3AZ"}, {"label": "VpnGw4AZ", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 5000 IKEv2/OpenVPN connections, aggregate throughput is 5 Gbps", "value": "VpnGw4AZ"}, {"label": "VpnGw5AZ", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 10000 IKEv2/OpenVPN connections, aggregate throughput is 10 Gbps", "value": "VpnGw5AZ"}]}}, {"name": "esGwRegionalSku", "type": "Microsoft.Common.DropDown", "label": "Select the VPN Gateway SKU", "defaultValue": "", "multiselect": false, "selectAll": false, "filter": false, "multiLine": true, "visible": "[and(and(equals(steps('esConnectivityGoalState').esVpnGw, 'Yes'), not(equals(steps('esConnectivityGoalState').esHub, 'vwan'))), equals(steps('esConnectivityGoalState').esVpnGw,'Yes'), equals(steps('esConnectivityGoalState').esGwRegionalOrAz, 'Regional') ,contains(split('canadacentral,centralus,eastus,eastus2,southcentralus,westus2,francecentral,germanywestcentral,northeurope,westeurope,uksouth,southafricanorth,japaneast,southeastasia,australiaeast,italynorth', ','), steps('esConnectivityGoalState').esNwLocation))]", "toolTip": "Select the required SKU for the VPN gateway.", "constraints": {"allowedValues": [{"label": "VpnGw2", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 500 IKEv2/OpenVPN connections, aggregate throughput is 1.25 Gbps", "value": "VpnGw2"}, {"label": "VpnGw3", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 1000 IKEv2/OpenVPN connections, aggregate throughput is 2.5 Gbps", "value": "VpnGw3"}, {"label": "VpnGw4", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 5000 IKEv2/OpenVPN connections, aggregate throughput is 5 Gbps", "value": "VpnGw4"}, {"label": "VpnGw5", "description": "Supports BGP, max 30 S2S/VNet-VNet tunnels, max 128 P2S SSTP connections, max 10000 IKEv2/OpenVPN connections, aggregate throughput is 10 Gbps", "value": "VpnGw5"}]}}, {"name": "esVwanGwScaleUnits", "type": "Microsoft.Common.DropDown", "label": "Select the VPN Gateway scale unit", "defaultValue": "", "multiselect": false, "selectAll": false, "filter": false, "multiLine": true, "visible": "[and(equals(steps('esConnectivityGoalState').esVpnGw, 'Yes'), equals(steps('esConnectivityGoalState').esHub, 'vwan'))]", "toolTip": "Select the VPN Gateway scale unit", "constraints": {"allowedValues": [{"label": "1 scale unit", "description": "Supports 500 Mbps x2", "value": "1"}, {"label": "2 scale units", "description": "Supports 1 Gbps x 2", "value": "2"}, {"label": "3 scale units", "description": "Supports 1.5 Gbps x 2", "value": "3"}, {"label": "4 scale units", "description": "Supports 2 Gbps x 2", "value": "4"}, {"label": "5 scale units", "description": "Supports 2.5 Gbps x 2", "value": "5"}, {"label": "6 scale units", "description": "Supports 3 Gbps x 2", "value": "6"}, {"label": "7 scale units", "description": "Supports 3.5 Gbps x 2", "value": "7"}, {"label": "8 scale units", "description": "Supports 4 Gbps x 2", "value": "8"}, {"label": "9 scale units", "description": "Supports 4.5 Gbps x 2", "value": "9"}, {"label": "10 scale units", "description": "Supports 5 Gbps x 2", "value": "10"}, {"label": "11 scale units", "description": "Supports 5.5 Gbps x 2", "value": "11"}, {"label": "12 scale units", "description": "Supports 6 Gbps x 2", "value": "12"}, {"label": "13 scale units", "description": "Supports 6.5 Gbps x 2", "value": "13"}, {"label": "14 scale units", "description": "Supports 7 Gbps x 2", "value": "14"}, {"label": "15 scale units", "description": "Supports 7.5 Gbps x 2", "value": "15"}, {"label": "16 scale units", "description": "Supports 8 Gbps x 2", "value": "16"}, {"label": "17 scale units", "description": "Supports 8.5 Gbps x 2", "value": "17"}, {"label": "18 scale units", "description": "Supports 9 Gbps x 2", "value": "18"}, {"label": "19 scale units", "description": "Supports 9.5 Gbps x 2", "value": "19"}, {"label": "20 scale units", "description": "Supports 10 Gbps x 2", "value": "20"}]}}, {"name": "esAddressVpnOrEr", "type": "Microsoft.Common.TextBox", "label": "Subnet for VPN/ExpressRoute Gateways", "toolTip": "Provide address prefix in CIDR notation (e.g **********/24)", "defaultValue": "**********/24", "visible": "[and(not(equals(steps('esConnectivityGoalState').esHub, 'vwan')), or(equals(steps('esConnectivityGoalState').esErGw, 'Yes'),equals(steps('esConnectivityGoalState').esVpnGw, 'Yes')))]", "constraints": {"required": true, "validations": [{"regex": "^(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:/(2[0-7]))$", "message": "Invalid CIDR range. The address prefix must be in the range [20,27]."}, {"isValid": "[if(greaterOrEquals(last(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), 8), equals(last(take(split(first(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), '.'), 1)), last(take(split(first(split(steps('esConnectivityGoalState').esAddressVpnOrEr, '/')), '.'), 1))), true)]", "message": "CIDR range not within virtual network CIDR range (first octet)."}, {"isValid": "[if(greaterOrEquals(last(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), 16), equals(last(take(split(first(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), '.'), 2)), last(take(split(first(split(steps('esConnectivityGoalState').esAddressVpnOrEr, '/')), '.'), 2))), true)]", "message": "CIDR range not within virtual network CIDR range (second octet)."}, {"isValid": "[if(greaterOrEquals(last(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), 24), equals(last(take(split(first(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), '.'), 3)), last(take(split(first(split(steps('esConnectivityGoalState').esAddressVpnOrEr, '/')), '.'), 3))), true)]", "message": "CIDR range not within virtual network CIDR range (third octet)."}, {"isValid": "[lessOrEquals(last(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), last(split(steps('esConnectivityGoalState').esAddressVpnOrEr, '/')))]", "message": "CIDR range not within virtual network CIDR range (subnet mask)."}]}}, {"name": "esErGw", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy ExpressRoute Gateway", "defaultValue": "No", "visible": "[not(equals(steps('esConnectivityGoalState').esHub, 'No'))]", "toolTip": "If 'Yes' is selected when also adding a subscription for connectivity, ARM will deploy Express Route gateway", "constraints": {"allowedValues": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esErRegionalOrAz", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy zone redundant or regional ExpressRoute Gateway", "defaultValue": "Zone redundant (recommended)", "visible": "[and(and(equals(steps('esConnectivityGoalState').esErGw, 'Yes'), not(equals(steps('esConnectivityGoalState').esHub, 'vwan'))),equals(steps('esConnectivityGoalState').esErGw,'Yes'),contains(split('canadacentral,centralus,eastus,eastus2,southcentralus,westus2,francecentral,germanywestcentral,northeurope,westeurope,uksouth,southafricanorth,japaneast,southeastasia,australiaeast,italynorth', ','), steps('esConnectivityGoalState').esNwLocation))]", "toolTip": "If 'Yes' is selected when also adding a subscription for connectivity, ARM will deploy Express Route Gateway to the selected region and availability zones.", "constraints": {"allowedValues": [{"label": "Zone redundant (recommended)", "value": "Zone"}, {"label": "Regional", "value": "Regional"}]}}, {"name": "esErAzSku", "type": "Microsoft.Common.DropDown", "label": "Select the ExpressRoute Gateway SKU", "defaultValue": "", "multiselect": false, "selectAll": false, "filter": false, "multiLine": true, "visible": "[and(and(equals(steps('esConnectivityGoalState').esErGw, 'Yes'), not(equals(steps('esConnectivityGoalState').esHub, 'vwan'))),equals(steps('esConnectivityGoalState').esErGw,'Yes'), equals(steps('esConnectivityGoalState').esErRegionalOrAz, 'Zone'), contains(split('canadacentral,centralus,eastus,eastus2,southcentralus,westus2,francecentral,germanywestcentral,northeurope,westeurope,uksouth,southafricanorth,japaneast,southeastasia,australiaeast,italynorth', ','), steps('esConnectivityGoalState').esNwLocation))]", "toolTip": "Select the required SKU for the Express Route gateway.", "constraints": {"allowedValues": [{"label": "ErGw1AZ", "description": "Megabits per second 1000, packets per second 100,000, connections per second 7000, max number of cicuit connections is 4", "value": "ErGw1AZ"}, {"label": "ErGw2AZ", "description": "Megabits per second 2000, packets per second 250,000, connections per second 14000, max number of cicuit connections is 8", "value": "ErGw2AZ"}, {"label": "ErGw3AZ", "description": "Megabits per second 10,000, packets per second 1,000,000, connections per second 28,000, max number of cicuit connections is 16", "value": "ErGw3AZ"}]}}, {"name": "esErRegionalSku", "type": "Microsoft.Common.DropDown", "label": "Select the ExpressRoute Gateway SKU", "defaultValue": "", "multiselect": false, "selectAll": false, "filter": false, "multiLine": true, "visible": "[and(and(equals(steps('esConnectivityGoalState').esErGw, 'Yes'), not(equals(steps('esConnectivityGoalState').esHub, 'vwan'))), equals(steps('esConnectivityGoalState').esErGw,'Yes'), equals(steps('esConnectivityGoalState').esErRegionalOrAz, 'Regional'), contains(split('canadacentral,centralus,eastus,eastus2,southcentralus,westus2,francecentral,germanywestcentral,northeurope,westeurope,uksouth,southafricanorth,japaneast,southeastasia,australiaeast,italynorth', ','), steps('esConnectivityGoalState').esNwLocation))]", "toolTip": "Select the required SKU for the Express Route gateway.", "constraints": {"allowedValues": [{"label": "Standard", "description": "Megabits per second 1000, packets per second 100,000, connections per second 7000, max number of cicuit connections is 4", "value": "Standard"}, {"label": "HighPerformance", "description": "Megabits per second 2000, packets per second 250,000, connections per second 14000, max number of cicuit connections is 8", "value": "HighPerformance"}, {"label": "UltraPerformance", "description": "Megabits per second 10,000, packets per second 1,000,000, connections per second 28,000, max number of cicuit connections is 16", "value": "UltraPerformance"}]}}, {"name": "esErNoAzSku", "type": "Microsoft.Common.DropDown", "label": "Select the ExpressRoute Gateway SKU", "defaultValue": "", "multiselect": false, "selectAll": false, "filter": false, "multiLine": true, "visible": "[and(and(equals(steps('esConnectivityGoalState').esErGw, 'Yes'), not(equals(steps('esConnectivityGoalState').esHub, 'vwan'))),equals(steps('esConnectivityGoalState').esErGw,'Yes'), not(contains(split('canadacentral,centralus,eastus,eastus2,southcentralus,westus2,francecentral,germanywestcentral,northeurope,westeurope,uksouth,southafricanorth,japaneast,southeastasia,australiaeast,italynorth', ','), steps('esConnectivityGoalState').esNwLocation)))]", "toolTip": "Select the required SKU for the Express Route gateway.", "constraints": {"allowedValues": [{"label": "Standard", "description": "Megabits per second 1000, packets per second 100,000, connections per second 7000, max number of cicuit connections is 4", "value": "Standard"}, {"label": "HighPerformance", "description": "Megabits per second 2000, packets per second 250,000, connections per second 14000, max number of cicuit connections is 8", "value": "HighPerformance"}, {"label": "UltraPerformance", "description": "Megabits per second 10,000, packets per second 1,000,000, connections per second 28,000, max number of cicuit connections is 16", "value": "UltraPerformance"}]}}, {"name": "esVwanErScaleUnits", "type": "Microsoft.Common.DropDown", "label": "Select the ExpressRoute Gateway scale unit", "defaultValue": "", "multiselect": false, "selectAll": false, "filter": false, "multiLine": true, "visible": "[and(equals(steps('esConnectivityGoalState').esErGw, 'Yes'), equals(steps('esConnectivityGoalState').esHub, 'vwan'))]", "toolTip": "Select the ExpressRoute Gateway scale unit", "constraints": {"allowedValues": [{"label": "1 scale unit", "description": "Supports 2 Gbps", "value": "1"}, {"label": "2 scale units", "description": "Supports 4 Gbps", "value": "2"}, {"label": "3 scale units", "description": "Supports 6 Gbps", "value": "3"}, {"label": "4 scale units", "description": "Supports 8 Gbps", "value": "4"}, {"label": "5 scale units", "description": "Supports 10 Gbps", "value": "5"}, {"label": "6 scale units", "description": "Supports 12 Gbps", "value": "6"}, {"label": "7 scale units", "description": "Supports 14 Gbps", "value": "7"}, {"label": "8 scale units", "description": "Supports 16 Gbps", "value": "8"}, {"label": "9 scale units", "description": "Supports 18 Gbps", "value": "9"}, {"label": "10 scale units", "description": "Supports 20 Gbps", "value": "10"}]}}, {"name": "esAzFw", "type": "Microsoft.Common.OptionsGroup", "label": "Deploy Azure Firewall", "defaultValue": "Yes (recommended)", "visible": "[or(equals(steps('esConnectivityGoalState').esHub, 'vhub'), equals(steps('esConnectivityGoalState').esHub, 'vwan'))]", "toolTip": "If 'Yes' is selected when also adding a subscription for connectivity, ARM will deploy Azure Firewall", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esAzFwDns", "type": "Microsoft.Common.OptionsGroup", "label": "Enable Azure Firewall as a DNS proxy", "defaultValue": "No", "visible": "[equals(steps('esConnectivityGoalState').esAzFw, 'Yes')]", "toolTip": "If 'Yes' is selected when also adding a subscription for connectivity, ARM will enable Azure Firewall as a DNS Proxy.", "constraints": {"allowedValues": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esAzFwSku", "type": "Microsoft.Common.DropDown", "label": "Select Azure Firewall tier", "defaultValue": "Standard", "multiselect": false, "selectAll": false, "filter": false, "multiLine": true, "visible": "[equals(steps('esConnectivityGoalState').esAzFw, 'Yes')]", "toolTip": "Select Azure Firewall tier", "constraints": {"allowedValues": [{"label": "Standard", "description": "Standard Azure Firewall", "value": "Standard"}, {"label": "Premium", "description": "Premium Azure Firewall adds support for TLS inspection, IDPS, URL filtering and web categories.", "value": "Premium"}]}}, {"name": "esFwAz", "type": "Microsoft.Common.DropDown", "label": "Select Availability Zones for the Azure Firewall", "defaultValue": "None", "multiselect": true, "selectAll": true, "filter": true, "visible": "[if(or(equals(steps('esConnectivityGoalState').esHub, 'vhub'), equals(steps('esConnectivityGoalState').esHub, 'vwan')), and(equals(steps('esConnectivityGoalState').esAzFw,'Yes'), contains(split('canadacentral,centralus,eastus,eastus2,southcentralus,westus2,francecentral,germanywestcentral,northeurope,westeurope,uksouth,southafricanorth,japaneast,southeastasia,australiaeast,italynorth', ','), steps('esConnectivityGoalState').esNwLocation)), false)]", "toolTip": "If 'Yes' is selected when also adding a subscription for connectivity, ARM will deploy Azure Firewall to the selected region and availability zones.", "constraints": {"allowedValues": [{"label": "Zone 1", "value": "1"}, {"label": "Zone 2", "value": "2"}, {"label": "Zone 3", "value": "3"}]}}, {"name": "esAddressFw", "type": "Microsoft.Common.TextBox", "label": "Subnet for Azure Firewall", "toolTip": "Provide address prefix in CIDR notation (e.g **********/24)", "defaultValue": "**********/24", "visible": "[and(equals(steps('esConnectivityGoalState').esAzFw, 'Yes'), not(equals(steps('esConnectivityGoalState').esHub, 'vwan')))]", "constraints": {"required": true, "validations": [{"regex": "^(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:/(2[0-6]))$", "message": "Invalid CIDR range. The address prefix must be in the range [20,26]."}, {"isValid": "[if(greaterOrEquals(last(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), 8), equals(last(take(split(first(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), '.'), 1)), last(take(split(first(split(steps('esConnectivityGoalState').esAddressFw, '/')), '.'), 1))), true)]", "message": "CIDR range not within virtual network CIDR range (first octet)."}, {"isValid": "[if(greaterOrEquals(last(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), 16), equals(last(take(split(first(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), '.'), 2)), last(take(split(first(split(steps('esConnectivityGoalState').esAddressFw, '/')), '.'), 2))), true)]", "message": "CIDR range not within virtual network CIDR range (second octet)."}, {"isValid": "[if(greaterOrEquals(last(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), 24), equals(last(take(split(first(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), '.'), 3)), last(take(split(first(split(steps('esConnectivityGoalState').esAddressFw, '/')), '.'), 3))), true)]", "message": "CIDR range not within virtual network CIDR range (third octet)."}, {"isValid": "[lessOrEquals(last(split(steps('esConnectivityGoalState').esAddressHubHS, '/')), last(split(steps('esConnectivityGoalState').esAddressFw, '/')))]", "message": "CIDR range not within virtual network CIDR range (subnet mask)."}]}}]}, {"name": "esIdentityGoalState", "label": "Identity", "subLabel": {"preValidation": "", "postValidation": ""}, "bladeTitle": "lzGs", "elements": [{"name": "multiPlatformIdentitySub", "type": "Microsoft.Common.InfoBox", "visible": "[not(equals(steps('lzSettings').subOrgsOption, 'Single'))]", "options": {"text": "To enable identity (AuthN/AuthZ) for workloads in landing zones, you must allocate an identity Subscription that is dedicated to host your Active Directory domain controllers. Please note, this Subscription will be moved to the identity Management Group, and ARM will assign the selected policies. We recommend using a new Subscription with no existing resources.", "uri": "https://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/identity-and-access-management", "style": "Info"}}, {"name": "singlePlatformIdentitySub", "type": "Microsoft.Common.InfoBox", "visible": "[equals(steps('lzSettings').subOrgsOption, 'Single')]", "options": {"text": "To enable identity (AuthN/AuthZ) for workloads in landing zones, it is recommended to assign specific policies to govern the virtual machines used for Active Directory domain controllers.", "uri": "https://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/identity-and-access-management", "style": "Info"}}, {"name": "esIdentity", "type": "Microsoft.Common.OptionsGroup", "label": "<PERSON><PERSON> recommended policies to govern identity and domain controllers", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for connectivity, Azure Policy will be assigned at the scope to govern your identity resources.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esIdentitySubSection", "type": "Microsoft.Common.Section", "label": "Identity subscription", "elements": [{"name": "esIdentitySubUniqueWarning", "type": "Microsoft.Common.InfoBox", "visible": true, "options": {"text": "Ensure you select a subscription that is dedicated for Identity. Selecting the same Subscription here for Management or Connectivity will result in a deployment failure. If you want to use a single Subscription for all platform resources, select 'Single' on the 'Azure Core Setup' blade.", "style": "Warning"}}, {"type": "Microsoft.Common.SubscriptionSelector", "name": "esIdentitySub", "label": "Management subscription"}], "visible": "[and(equals(steps('esIdentityGoalState').esIdentity,'Yes'), not(equals(steps('lzSettings').subOrgsOption, 'Single')))]"}, {"name": "identitypolicies", "type": "Microsoft.Common.TextBlock", "visible": "[equals(steps('esIdentityGoalState').esIdentity,'Yes')]", "options": {"text": "Select which of the the recommended policies you will assign to your identity management group.", "link": {"label": "Learn more", "uri": "https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/ready/enterprise-scale/design-principles#policy-driven-governance"}}}, {"name": "esIdDenyRdp", "type": "Microsoft.Common.OptionsGroup", "label": "Prevent inbound RDP from internet", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Azure Policy will be assigned and prevent inbound RDP from internet", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esIdentityGoalState').esIdentity,'Yes')]"}, {"name": "esIdDenySubnetNsg", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure subnets are associated with NSG", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Azure Policy will be assigned to ensure NSGs must be associated with subnets being created", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esIdentityGoalState').esIdentity,'Yes')]"}, {"name": "esIdDenyPublicIp", "type": "Microsoft.Common.OptionsGroup", "label": "Prevent usage of public IP", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Azure Policy will be assigned to ensure public IP resources cannot be created", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[and(equals(steps('esIdentityGoalState').esIdentity,'Yes'), not(equals(steps('lzSettings').subOrgsOption, 'Single')))]"}, {"name": "esIdAzBackup", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure Azure VMs (Windows & Linux) are enabled for Azure Backup", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Azure Policy will be assigned and enable Azure Backup on all VMs in the landing zones.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esIdentityGoalState').esIdentity,'Yes')]"}, {"name": "esIdentityConnectivity", "type": "Microsoft.Common.OptionsGroup", "label": "Create virtual network and connect to the connectivity hub (optional)?", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected for corp landing zones, ARM will connect the subscriptions to the hub virtual network via VNet peering.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[and(and(equals(steps('esIdentityGoalState').esIdentity,'Yes'), not(equals(steps('lzSettings').subOrgsOption, 'Single'))), equals(steps('esIdentityGoalState').esIdentity, 'Yes'), not(equals(steps('esConnectivityGoalState').esHub,'No')))]"}, {"name": "esIdentityCidr", "type": "Microsoft.Common.TextBox", "label": "Virtual network address space", "placeholder": "", "defaultValue": "**********/24", "toolTip": "The virtual network's address space, specified as one address prefixes in CIDR notation (e.g. ***********/24)", "constraints": {"required": true, "validations": [{"regex": "^(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:/(1[0-9]|2[0-9]))$", "message": "Invalid CIDR range. The address prefix must be in the range [10,29]."}]}, "visible": "[and(equals(steps('esIdentityGoalState').esIdentityConnectivity, 'Yes'), not(equals(steps('esConnectivityGoalState').esHub,'No')))]"}]}, {"name": "lzGoalState", "label": "Landing zone configuration", "subLabel": {"preValidation": "", "postValidation": ""}, "bladeTitle": "lzGs", "elements": [{"name": "infoBox1", "type": "Microsoft.Common.InfoBox", "visible": true, "options": {"text": "You can optionally provide subscriptions for your first landing zones for both 'online' and 'corp' and assign recommended policies that will ensure workloads will be secure, monitored, and protected according to best practices.", "uri": "https://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/design-principles#policy-driven-governance", "style": "Info"}}, {"name": "corpText", "type": "Microsoft.Common.TextBlock", "visible": true, "options": {"text": "Select the subscriptions you want to move to corp management group.", "link": {"label": "Learn more", "uri": "https://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/design-principles#subscription-democratization"}}}, {"name": "esLzConnectivity", "type": "Microsoft.Common.OptionsGroup", "label": "Connect corp landing zones to the connectivity hub (optional)?", "defaultValue": "No", "toolTip": "If 'Yes' is selected for corp landing zones, ARM will connect the subscriptions to the hub virtual network via VNet peering.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[or(equals(steps('esConnectivityGoalState').esHub, 'nva'), equals(steps('esConnectivityGoalState').esHub, 'vhub'))]"}, {"name": "lzCorpSubsApi", "type": "Microsoft.Solutions.ArmApiControl", "request": {"method": "GET", "path": "subscriptions?api-version=2020-01-01"}}, {"name": "esCorpLzSub", "type": "Microsoft.Common.DropDown", "label": "Corp landing zone subscriptions (optional)", "toolTip": "", "multiselect": true, "selectAll": true, "filter": true, "filterPlaceholder": "Filter items ...", "multiLine": true, "visible": "[or(or(equals(steps('lzGoalState').esLzConnectivity, 'No'), equals(steps('esConnectivityGoalState').esHub, 'No')), equals(steps('esConnectivityGoalState').esHub, 'vwan'), equals(steps('lzGoalState').esLzConnectivity, 'No'))]", "constraints": {"allowedValues": "[map(filter(steps('lzGoalState').lzCorpSubsApi.value, (sub) => equals(sub.state, 'Enabled')), (sub) => parse(concat('{\"label\":\"', sub.displayName, '\",\"description\":\"', sub.subscriptionId, '\",\"value\":\"', toLower(sub.subscriptionId), '\"}')) )]", "required": false}}, {"name": "lzConnectedSubs", "type": "Microsoft.Common.EditableGrid", "ariaLabel": "Add existing subscriptions into the management group landing zone and provide address space for virtual network peering", "label": "Corp connected landing zone subscriptions (optional)", "visible": "[equals(steps('lzGoalState').esLzConnectivity, 'Yes')]", "constraints": {"width": "Full", "rows": {"count": {"min": 1, "max": 10}}, "columns": [{"id": "subs", "header": "Subscription", "width": "1fr", "element": {"name": "esLzConnectedSub", "type": "Microsoft.Common.DropDown", "label": "Landing zone subscription", "toolTip": "", "multiselect": false, "selectAll": false, "filter": true, "filterPlaceholder": "Filter items ...", "multiLine": false, "constraints": {"allowedValues": "[map(steps('lzGoalState').lzSubsApi.value, (sub) => parse(concat('{\"label\":\"', sub.displayName, '\",\"description\":\"', sub.subscriptionId, '\",\"value\":\"', toLower(sub.subscriptionId), '\"}')) )]", "required": false}}}, {"id": "addresses", "header": "Virtual Network Address space", "width": "1fr", "element": {"type": "Microsoft.Common.TextBox", "placeholder": "Ensure there are no overlapping IP addresses!", "constraints": {"required": true, "validations": [{"regex": "^(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:/(1[0-9]|2[0-4]))$", "message": "Invalid CIDR range. The address prefix must be in the range [10,24]."}]}}}]}}, {"name": "lzSubsApi", "type": "Microsoft.Solutions.ArmApiControl", "request": {"method": "GET", "path": "subscriptions?api-version=2020-01-01"}}, {"name": "onlineText", "type": "Microsoft.Common.TextBlock", "visible": true, "options": {"text": "Select the subscriptions you want to move to online management group.", "link": {"label": "Learn more", "uri": "https://docs.microsoft.com/azure/cloud-adoption-framework/ready/enterprise-scale/design-principles#subscription-democratization"}}}, {"name": "lzOnlineSubsApi", "type": "Microsoft.Solutions.ArmApiControl", "request": {"method": "GET", "path": "subscriptions?api-version=2020-01-01"}}, {"name": "esOnlineLzSub", "type": "Microsoft.Common.DropDown", "label": "Online landing zone subscriptions (optional)", "toolTip": "", "multiselect": true, "selectAll": true, "filter": true, "filterPlaceholder": "Filter items ...", "multiLine": true, "visible": true, "constraints": {"allowedValues": "[map(filter(steps('lzGoalState').lzOnlineSubsApi.value, (sub) => equals(sub.state, 'Enabled')), (sub) => parse(concat('{\"label\":\"',sub.displayName,'\",\"description\":\"',sub.subscriptionId,'\",\"value\":\"',toLower(sub.subscriptionId),'\"}')))]", "required": false}}, {"name": "azMonText", "type": "Microsoft.Common.TextBlock", "visible": true, "options": {"text": "Select which of the the recommended policies you will assign to your landing zones.", "link": {"label": "Learn more", "uri": "https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/ready/enterprise-scale/design-principles#policy-driven-governance"}}}, {"name": "esLzDdoS", "type": "Microsoft.Common.OptionsGroup", "label": "Enable DDoS Protection Standard", "defaultValue": "Yes (recommended)", "visible": "[and(not(equals(steps('esConnectivityGoalState').esHub,'No')),equals(steps('esConnectivityGoalState').esDdoS,'Yes'))]", "toolTip": "If 'Yes' is selected when also adding a connectivity subscription earlier, DDoS Protection Standard will be enabled.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esLzPrivateLink", "type": "Microsoft.Common.OptionsGroup", "label": "Prevent usage of Public Endpoints for Azure PaaS services in the corp connected landing zones", "defaultValue": "Yes (recommended)", "visible": true, "toolTip": "If 'Yes' is selected then Azure Policy will prevent PaaS resources to use public endpoints.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esPrivateDnsZones", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure private endpoints to Azure PaaS services are integrated with Azure Private DNS Zones in the corp connected landing zones", "defaultValue": "Yes (recommended)", "visible": "[equals(steps('esConnectivityGoalState').esPrivateDns, 'Yes')]", "toolTip": "If 'Yes' is selected then Azure Policy will ensure private endpoints to Azure PaaS services are integrated with Azure Private DNS Zones in the connectivity subscription on behalf of the users.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esEncryptionInTransit", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure encryption in transit is enabled for PaaS services", "defaultValue": "Yes (recommended)", "visible": true, "toolTip": "If 'Yes' is selected then Azure Policy will ensure PaaS resources uses TLS and SSL.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esVmMonitoring", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure Azure VMs (Windows & Linux) and Azure Arc-enabled servers are being monitored", "defaultValue": "Yes (recommended)", "toolTip": "Enabling this Azure Policy will ensure that every virtual machine (Windows, Linux, including Azure Arc enabled servers) are onboarded to Azure Monitor and Security", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}, {"name": "esVmssMonitoring", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure Azure VMSS (Windows & Linux) are being monitored", "defaultValue": "Yes (recommended)", "toolTip": "Enabling this Azure Policy will ensure that every virtual machine scale set (Windows & Linux) are onboarded to Azure Monitor and Security", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": "[equals(steps('esGoalState').esLogAnalytics,'Yes')]"}, {"name": "esAksPolicy", "type": "Microsoft.Common.OptionsGroup", "label": "Enable Kubernetes (AKS) for Azure Policy", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continous compliance", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esAksPriv", "type": "Microsoft.Common.OptionsGroup", "label": "Prevent privileged containers in Kubernetes clusters", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, policy will be assigned to prevent privileged containers in AKS", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esAksNoPriv", "type": "Microsoft.Common.OptionsGroup", "label": "Prevent privileged escalation in Kubernetes clusters", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, policy will be assigned to prevent privileged escalations in AKS", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esAksIngress", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure HTTPS ingress is enforced in Kubernetes clusters", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, HTTPS ingress will be required in AKS", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esLzDbPip", "type": "Microsoft.Common.OptionsGroup", "label": "Prevent public IP for Databricks workloads in the corp connected landing zones", "defaultValue": "Yes (recommended)", "visible": true, "toolTip": "If 'Yes' is selected Azure Policy will prevent usage of public IP for Databricks workload.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esLzDbVnet", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure VNet injection is enabled for Databricks workspaces in corp connected landing zones", "defaultValue": "Yes (recommended)", "visible": true, "toolTip": "If 'Yes' is selected Azure Policy will ensure vnet injection is enabled.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esLzDbSku", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure Databricks workloads are using the right SKU to ensure enterprise security and Azure RBAC", "defaultValue": "Yes (recommended)", "visible": true, "toolTip": "If 'Yes' is selected Azure Policy will enforce the sku setting.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}}, {"name": "esAzBackup", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure Azure VMs (Windows & Linux) are enabled for Azure Backup", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Azure Policy will be assigned and enable Azure Backup on all VMs in the landing zones.", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esDenyRdp", "type": "Microsoft.Common.OptionsGroup", "label": "Prevent inbound RDP from internet", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Azure Policy will be assigned and prevent inbound RDP from internet", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esNsg", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure subnets are associated with NSG", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Azure Policy will be assigned to ensure NSGs must be associated with subnets being created", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esIpForwarding", "type": "Microsoft.Common.OptionsGroup", "label": "Prevent IP forwarding", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Azure Policy will be assigned and prevent IP forwarding", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esSqlEncryption", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure Azure SQL is enabled with transparent data encryption", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected when also adding a subscription for management, ARM will deploy resources and enable them for continous compliance", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esSqlAudit", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure auditing is enabled on Azure SQL", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Azure Policy will be assigned to ensure auditing is enabled on Azure SQLs", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}, {"name": "esHttpsStorage", "type": "Microsoft.Common.OptionsGroup", "label": "Ensure secure connections (HTTPS) to storage accounts", "defaultValue": "Yes (recommended)", "toolTip": "If 'Yes' is selected, Azure Policy will be assigned to ensure storage can only be accessed using HTTPS", "constraints": {"allowedValues": [{"label": "Yes (recommended)", "value": "Yes"}, {"label": "No", "value": "No"}]}, "visible": true}]}]}, "outputs": {"parameters": {"subnetMaskForGw": "[steps('esConnectivityGoalState').esAddressVpnOrEr]", "subnetMaskForAzFw": "[steps('esConnectivityGoalState').esAddressFw]", "enableErGw": "[steps('esConnectivityGoalState').esErGw]", "enableVpnGw": "[steps('esConnectivityGoalState').esVpnGw]", "enableHub": "[steps('esConnectivityGoalState').esHub]", "enableDdoS": "[steps('esConnectivityGoalState').esDdoS]", "connectivitySubscriptionId": "[if(not(equals(steps('esConnectivityGoalState').esNwSubSection.esNwSub.subscriptionId,steps('esGoalState').esMgmtSubSection.esMgmtSub.subscriptionId)),steps('esConnectivityGoalState').esNwSubSection.esNwSub.subscriptionId,'')]", "enableAzFw": "[steps('esConnectivityGoalState').esAzFw]", "enableAzFwDnsProxy": "[steps('esConnectivityGoalState').esAzFwDns]", "addressPrefix": "[coalesce(steps('esConnectivityGoalState').esAddressHubVWAN, steps('esConnectivityGoalState').esAddressHubHS, '')]", "location": "[steps('esConnectivityGoalState').esNwLocation]", "managementSubscriptionId": "[steps('esGoalState').esMgmtSubSection.esMgmtSub.subscriptionId]", "identitySubscriptionId": "[if(or(not(equals(steps('esIdentityGoalState').esIdentitySubSection.esIdentitySub.subscriptionId,steps('esGoalState').esMgmtSubSection.esMgmtSub.subscriptionId)),not(equals(steps('esIdentityGoalState').esIdentitySubSection.esIdentitySub.subscriptionId,steps('esConnectivityGoalState').esNwSubSection.esNwSub.subscriptionId))),steps('esIdentityGoalState').esIdentitySubSection.esIdentitySub.subscriptionId,'')]", "onlineLzSubscriptionId": "[if(or(not(contains(steps('lzGoalState').esOnlineLzSub,steps('esGoalState').esMgmtSubSection.esMgmtSub.subscriptionId)),not(contains(steps('lzGoalState').esOnlineLzSub,steps('esConnectivityGoalState').esNwSubSection.esNwSub.subscriptionId))),steps('lzGoalState').esOnlineLzSub,'')]", "corpLzSubscriptionId": "[if(or(not(contains(steps('lzGoalState').esCorpLzSub,steps('esGoalState').esMgmtSubSection.esMgmtSub.subscriptionId)),not(contains(steps('lzGoalState').esCorpLzSub,steps('esConnectivityGoalState').esNwSubSection.esNwSub.subscriptionId))),steps('lzGoalState').esCorpLzSub,'')]", "enableLogAnalytics": "[steps('esGoalState').esLogAnalytics]", "denyRdpForIdentity": "[steps('esIdentityGoalState').esIdDenyRdp]", "denySubnetWithoutNsgForIdentity": "[steps('esIdentityGoalState').esIdDenySubnetNsg]", "denyPipForIdentity": "[steps('esIdentityGoalState').esIdDenyPublicIp]", "enableVmBackupForIdentity": "[steps('esIdentityGoalState').esIdAzBackup]", "enableAsc": "[steps('esGoalState').esAsc]", "emailContactAsc": "[steps('esGoalState').esAscEmail]", "enableAscForServers": "[steps('esGoalState').esAscVms]", "enableAscForStorage": "[steps('esGoalState').esAscStorage]", "enableAscForSql": "[steps('esGoalState').esAscSql]", "enableAscForArm": "[steps('esGoalState').esAscArm]", "enableAscForDns": "[steps('esGoalState').esAscDns]", "enableAscForContainers": "[steps('esGoalState').esAscContainers]", "enableSecuritySolution": "[steps('esGoalState').esSecuritySolution]", "enableAgentHealth": "[steps('esGoalState').esAgentSolution]", "enableChangeTracking": "[steps('esGoalState').esChangeTracking]", "enableUpdateMgmt": "[steps('esGoalState').esUpdateMgmt]", "enableVmInsights": "[steps('esGoalState').esVmInsights]", "enableSqlAssessment": "[steps('esGoalState').esSqlAssessment]", "enableSqlVulnerabilityAssessment": "[steps('esGoalState').esSqlVulnerabilityAssessment]", "enableSqlAdvancedThreatProtection": "[steps('esGoalState').esSqlAdvancedThreatProtection]", "enterpriseScaleCompanyPrefix": "[steps('lzSettings').esMgmtGroup]", "enableSqlAudit": "[steps('lzGoalState').esSqlAudit]", "enableSqlEncryption": "[steps('lzGoalState').esSqlEncryption]", "enableVmBackup": "[steps('lzGoalState').esAzBackup]", "enableLzDdoS": "[steps('lzGoalState').esLzDdoS]", "denyPublicEndpoints": "[steps('lzGoalState').esLzPrivateLink]", "enableEncryptionInTransit": "[steps('lzGoalState').esEncryptionInTransit]", "enableAksPolicy": "[steps('lzGoalState').esAksPolicy]", "denyAksPrivileged": "[steps('lzGoalState').esAksPriv]", "denyAksPrivilegedEscalation": "[steps('lzGoalState').esAksNoPriv]", "denyHttpIngressForAks": "[steps('lzGoalState').esAksIngress]", "denyRdp": "[steps('lzGoalState').esDenyRdp]", "enableStorageHttps": "[steps('lzGoalState').esHttpsStorage]", "denyIpForwarding": "[steps('lzGoalState').esIpForwarding]", "denySubnetWithoutNsg": "[steps('lzGoalState').esNsg]", "denyDatabricksPip": "[steps('lzGoalState').esLzDbPip]", "denyDatabricksVnet": "[steps('lzGoalState').esLzDbVnet]", "denyDatabricksSku": "[steps('lzGoalState').esLzDbSku]", "retentionInDays": "[string(steps('esGoalState').esLogRetention)]", "enableVmMonitoring": "[steps('lzGoalState').esVmMonitoring]", "enableVmssMonitoring": "[steps('lzGoalState').esVmssMonitoring]", "vpnOrErZones": "[steps('esConnectivityGoalState').esGwRegionalOrAz]", "firewallSku": "[steps('esConnectivityGoalState').esAzFwSku]", "firewallZones": "[steps('esConnectivityGoalState').esFwAz]", "gwRegionalOrAz": "[steps('esConnectivityGoalState').esGwRegionalOrAz]", "gwAzSku": "[steps('esConnectivityGoalState').esGwAzSku]", "gwRegionalSku": "[if(empty(steps('esConnectivityGoalState').esGwRegionalSku), steps('esConnectivityGoalState').esGwNoAzSku, steps('esConnectivityGoalState').esGwRegionalSku)]", "erRegionalOrAz": "[steps('esConnectivityGoalState').esErRegionalOrAz]", "erAzSku": "[steps('esConnectivityGoalState').esErAzSku]", "erRegionalSku": "[if(empty(steps('esConnectivityGoalState').esErRegionalSku), steps('esConnectivityGoalState').esErNoAzSku, steps('esConnectivityGoalState').esErRegionalSku)]", "singlePlatformSubscriptionId": "[steps('lzSettings').esSingleSubSection.esSingleSub.subscriptionId]", "denyClassicResources": "[steps('lzSettings').denyClassicResources]", "expressRouteScaleUnit": "[steps('esConnectivityGoalState').esVwanErScaleUnits]", "vpnGateWayScaleUnit": "[steps('esConnectivityGoalState').esVwanGwScaleUnits]", "enablePrivateDnsZones": "[steps('esConnectivityGoalState').esPrivateDns]", "enablePrivateDnsZonesForLzs": "[steps('lzGoalState').esPrivateDnsZones]", "identityAddressPrefix": "[steps('esIdentityGoalState').esIdentityCidr]", "corpConnectedLzSubscriptionId": "[if(or(not(contains(steps('lzGoalState').esCorpLzSub,steps('esGoalState').esMgmtSubSection.esMgmtSub.subscriptionId)),not(contains(steps('lzGoalState').esCorpLzSub,steps('esConnectivityGoalState').esNwSubSection.esNwSub.subscriptionId))),steps('lzGoalState').lzConnectedSubs,'')]"}, "kind": "Tenant", "location": "[steps('basics').resourceScope.location.name]"}}}