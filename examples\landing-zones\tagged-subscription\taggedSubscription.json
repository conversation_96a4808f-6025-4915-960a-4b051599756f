{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "subscriptionAliasName": {
            "type": "string",
            "metadata": {
                "description": "Provide alias (and displayName) for the subscription"
            }
        },
        "targetManagementGroup": {
            "type": "string",
            "metadata": {
                "details": "Select targeted management group that the subscription will land into"
            }
        },
        "billingAccountId": {
            "type": "string",
            "metadata": {
                "description": "Provide the resourceId for the enrollment account or MCA"
            }
        },
        "tagValue": {
            "type": "string",
            "metadata": {
                "description": "Provide a value of the required application key tag."
            }
        }
    },
    "variables": {},
    "resources": [
        {
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[concat('create-', parameters('subscriptionAliasName'))]",
            "scope": "[concat('Microsoft.Management/managementGroups/', parameters('targetManagementGroup'))]",
            "location": "[deployment().location]",
            "properties": {
                "mode": "Incremental",
                "expressionEvaluationOptions": {
                    "scope": "inner"
                },
                "parameters": {
                    // Sharing parameter values from outer to inner execution scope
                    "subAliasName": {
                        "value": "[parameters('subscriptionAliasName')]"
                    },
                    "mgmtGroupId": {
                        "value": "[parameters('targetManagementGroup')]"
                    },
                    "billingId": {
                        "value": "[parameters('billingAccountId')]"
                    }
                },
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                        // parameters for inner scope
                        "subAliasName": {
                            "type": "string"
                        },
                        "mgmtGroupId": {
                            "type": "string"
                        },
                        "billingId": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            "scope": "/", // routing the request to tenant root
                            "name": "[parameters('subAliasName')]",
                            "type": "Microsoft.Subscription/aliases",
                            "apiVersion": "2020-09-01",
                            "properties": {
                                "workLoad": "Production",
                                "displayName": "[parameters('subAliasName')]",
                                "billingScope": "[parameters('billingId')]",
                                "managementGroupId": "[tenantResourceId('Microsoft.Management/managementGroups/', parameters('mgmtGroupId'))]"
                            }
                        }
                    ],
                    "outputs": {
                        // Referencing the guid generated for the subscription to be used in subsequent (optional) deployments to this subscription
                        "subscriptionId": {
                            "type": "string",
                            "value": "[reference(parameters('subAliasName')).subscriptionId]"
                        }
                    }
                }
            }
        },
        {
            // Creating deployment to invoke policyAssignment of newly created subscription
            "scope": "[concat('Microsoft.Management/managementGroups/', parameters('targetManagementGroup'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-08-01",
            "name": "[concat('tag-', parameters('subscriptionAliasName'))]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[concat('Microsoft.Resources/deployments/', 'create-', parameters('subscriptionAliasName'))]"
            ],
            "properties": {
                "mode": "Incremental",
                "expressionEvaluationOptions": {
                    "scope": "inner"
                },
                "parameters": {
                    // Value coming from the previous deployment's output inner scope to be used to target subscription deployments
                    "targetSubscriptionId": {
                        "value": "[reference(concat('create-', parameters('subscriptionAliasName'))).outputs.subscriptionId.value]"
                    },
                    "tagInputValue": {
                        "value": "[parameters('tagValue')]"
                    }
                },
                "template": {
                    "$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#",
                    "contentVersion": "*******",
                    "parameters": {
                         // parameters for inner scope
                        "targetSubscriptionId": {
                            "type": "string"
                        },
                        "tagInputValue": {
                            "type": "string"
                        }
                    },
                    "resources": [
                        {
                            // subscription scoped deployment to make policy assignment
                            "type": "Microsoft.Resources/deployments",
                            "apiVersion": "2019-08-01",
                            "name": "subscription-dep",
                            "location": "[deployment().location]",
                            "subscriptionId": "[parameters('targetSubscriptionId')]",
                            "properties": {
                                "mode": "Incremental",
                                "expressionEvaluationOptions": {
                                    "scope": "inner"
                                },
                                "parameters": {
                                    "tagInputValueInner": {
                                        "value": "[parameters('tagInputValue')]"
                                    }
                                },
                                "template": {
                                    "$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#",
                                    "contentVersion": "*******",
                                    "parameters": {
                                         // parameters for inner scope
                                        "tagInputValueInner": {
                                            "type": "string"
                                        }
                                    },
                                    "variables": {},
                                    "resources": [
                                        {
                                            "type": "Microsoft.Resources/tags",
                                            "apiVersion": "2020-06-01",
                                            "name": "default",
                                            "properties": {
                                                "tags": {
                                                    "appKey": "[parameters('tagInputValueInner')]"
                                                }
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    ]
                }
            }
        }
    ],
    "outputs": {
        "subscriptionIdOuter": {
            "type": "string",
            "value": "[reference(concat('create-', parameters('subscriptionAliasName'))).outputs.subscriptionId.value]"
        }
    }
}