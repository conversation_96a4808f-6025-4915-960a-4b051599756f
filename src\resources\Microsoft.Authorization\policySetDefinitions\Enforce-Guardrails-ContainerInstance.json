{"name": "Enforce-Guardrails-ContainerInstance", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Container Instance", "description": "This policy initiative is a group of policies that ensures Container Apps is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "Container Instances", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"containerInstanceVnet": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8af8f826-edcb-4178-b35f-851ea6fea615", "policyDefinitionReferenceId": "Deny-ContainerInstance-Vnet", "definitionVersion": "2.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('containerInstanceVnet')]"}}}], "policyDefinitionGroups": null}}