{"name": "Deploy-Private-DNS-Azure-KeyVault", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Preview: Configure Azure Key Vaults to use private DNS zones", "description": "Use private DNS zones to override the DNS resolution for a private endpoint. A private DNS zone links to your virtual network to resolve to key vault. Learn more at: https://aka.ms/akvprivatelink.", "metadata": {"version": "1.0.0-preview", "category": "<PERSON>", "source": "https://github.com/Azure/Enterprise-Scale/", "preview": true, "alzCloudEnvironments": ["AzureChinaCloud"]}, "parameters": {"privateDnsZoneId": {"type": "String", "metadata": {"displayName": "Private DNS Zone ID", "description": "A private DNS zone ID to connect to the private endpoint.", "strongType": "Microsoft.Network/privateDnsZones", "assignPermissions": true}}, "effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/privateEndpoints"}, {"count": {"field": "Microsoft.Network/privateEndpoints/privateLinkServiceConnections[*].groupIds[*]", "where": {"field": "Microsoft.Network/privateEndpoints/privateLinkServiceConnections[*].groupIds[*]", "equals": "vault"}}, "greaterOrEquals": 1}]}, "then": {"effect": "[[parameters('effect')]", "details": {"type": "Microsoft.Network/privateEndpoints/privateDnsZoneGroups", "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/4d97b98b-1d4f-4787-a291-c67834d212e7"], "deployment": {"properties": {"mode": "incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"privateDnsZoneId": {"type": "string"}, "privateEndpointName": {"type": "string"}, "location": {"type": "string"}}, "resources": [{"name": "[[concat(parameters('privateEndpointName'), '/deployedByPolicy')]", "type": "Microsoft.Network/privateEndpoints/privateDnsZoneGroups", "apiVersion": "2020-03-01", "location": "[[parameters('location')]", "properties": {"privateDnsZoneConfigs": [{"name": "keyvault-privateDnsZone", "properties": {"privateDnsZoneId": "[[parameters('privateDnsZoneId')]"}}]}}]}, "parameters": {"privateDnsZoneId": {"value": "[[parameters('privateDnsZoneId')]"}, "privateEndpointName": {"value": "[[field('name')]"}, "location": {"value": "[[field('location')]"}}}}}}}}}