id: 
name: GitO<PERSON>.PullRequestIssueManagement
description: GitOps.PullRequestIssueManagement primitive
owner: 
resource: repository
disabled: false
where: 
configuration:
  resourceManagementConfiguration:
    scheduledSearches:
    - description: 
      frequencies:
      - hourly:
          hour: 3
      filters:
      - isIssue
      - isOpen
      - hasLabel:
          label: 'Needs: Author Feedback'
      - noActivitySince:
          days: 5
      - isNotLabeledWith:
          label: 'Status: No Recent Activity'
      - isNotLabeledWith:
          label: long term
      actions:
      - addLabel:
          label: 'Status: No Recent Activity'
      - addReply:
          reply: This issue has been automatically marked as stale because it has been marked as requiring author feedback but has not had any activity for **5 days**.
    - description: 
      frequencies:
      - hourly:
          hour: 3
      filters:
      - isIssue
      - isOpen
      - hasLabel:
          label: duplicate
      - noActivitySince:
          days: 1
      actions:
      - addReply:
          reply: This issue has been marked as duplicate and has not had any activity for **1 day**. It will be closed for housekeeping purposes.
      - closeIssue
    - description: 
      frequencies:
      - hourly:
          hour: 3
      filters:
      - isPullRequest
      - isOpen
      - hasLabel:
          label: 'Needs: Author Feedback'
      - noActivitySince:
          days: 7
      - isNotLabeledWith:
          label: 'Status: No Recent Activity'
      actions:
      - addLabel:
          label: 'Status: No Recent Activity'
      - addReply:
          reply: This pull request has been automatically marked as stale because it has been marked as requiring author feedback but has not had any activity for **7 days**.
    eventResponderTasks:
    - if:
      - payloadType: Issue_Comment
      - isAction:
          action: Created
      - isActivitySender:
          issueAuthor: True
      - hasLabel:
          label: 'Needs: Author Feedback'
      - isOpen
      then:
      - addLabel:
          label: 'Needs: Attention :wave:'
      - removeLabel:
          label: 'Needs: Author Feedback'
      description: 
    - if:
      - payloadType: Issues
      - not:
          isAction:
            action: Closed
      - hasLabel:
          label: 'Status: No Recent Activity'
      then:
      - removeLabel:
          label: 'Status: No Recent Activity'
      description: 
    - if:
      - payloadType: Issue_Comment
      - hasLabel:
          label: 'Status: No Recent Activity'
      then:
      - removeLabel:
          label: 'Status: No Recent Activity'
      description: 
    - if:
      - payloadType: Pull_Request
      then:
      - inPrLabel:
          label: 'Status: In PR'
      description: 
    - if:
      - payloadType: Pull_Request_Review
      - isAction:
          action: Submitted
      - isReviewState:
          reviewState: Changes_requested
      then:
      - addLabel:
          label: 'Needs: Author Feedback'
      description: 
    - if:
      - payloadType: Pull_Request
      - isActivitySender:
          issueAuthor: True
      - not:
          isAction:
            action: Closed
      - hasLabel:
          label: 'Needs: Author Feedback'
      then:
      - removeLabel:
          label: 'Needs: Author Feedback'
      description: 
    - if:
      - payloadType: Issue_Comment
      - isActivitySender:
          issueAuthor: True
      - hasLabel:
          label: 'Needs: Author Feedback'
      then:
      - removeLabel:
          label: 'Needs: Author Feedback'
      description: 
    - if:
      - payloadType: Pull_Request_Review
      - isActivitySender:
          issueAuthor: True
      - hasLabel:
          label: 'Needs: Author Feedback'
      then:
      - removeLabel:
          label: 'Needs: Author Feedback'
      description: 
    - if:
      - payloadType: Pull_Request
      - not:
          isAction:
            action: Closed
      - hasLabel:
          label: 'Status: No Recent Activity'
      then:
      - removeLabel:
          label: 'Status: No Recent Activity'
      description: 
    - if:
      - payloadType: Issue_Comment
      - hasLabel:
          label: 'Status: No Recent Activity'
      then:
      - removeLabel:
          label: 'Status: No Recent Activity'
      description: 
    - if:
      - payloadType: Pull_Request_Review
      - hasLabel:
          label: 'Status: No Recent Activity'
      then:
      - removeLabel:
          label: 'Status: No Recent Activity'
      description: 
    - if:
      - payloadType: Issue_Comment
      then:
      - cleanEmailReply
      description: 
onFailure: 
onSuccess: 
