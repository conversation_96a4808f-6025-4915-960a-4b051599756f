{"name": "Enforce-ALZ-Decomm", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce policies in the Decommissioned Landing Zone", "description": "Enforce policies in the Decommissioned Landing Zone.", "metadata": {"version": "1.1.0", "category": "Decommissioned", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"listOfResourceTypesAllowed": {"type": "Array", "defaultValue": [], "metadata": {"displayName": "Allowed resource types in the Decommissioned landing zone", "description": "Allowed resource types in the Decommissioned landing zone, default is none.", "strongType": "resourceTypes"}}}, "policyDefinitions": [{"policyDefinitionReferenceId": "DecomDenyResources", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a08ec900-254a-4555-9bf5-e42af04b5c5c", "definitionVersion": "1.*.*", "parameters": {"listOfResourceTypesAllowed": {"value": "[[parameters('listOfResourceTypesAllowed')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "DecomShutdownMachines", "policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deploy-Vm-autoShutdown", "definitionVersion": "1.*.*", "parameters": {}, "groupNames": []}], "policyDefinitionGroups": null}}