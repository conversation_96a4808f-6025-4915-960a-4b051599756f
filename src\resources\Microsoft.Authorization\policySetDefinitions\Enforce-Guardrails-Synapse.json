{"name": "Enforce-Guardrails-Synapse", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Synapse workspaces", "description": "This policy initiative is a group of policies that ensures Synapse workspaces is compliant per regulated Landing Zones.", "metadata": {"version": "1.2.0", "category": "Synapse", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"synapseLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "synapseManagedVnet": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "synapseDataTraffic": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "synapseTenants": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "synapseAllowedTenantIds": {"type": "array", "defaultValue": ["[[subscription().tenantId]"]}, "synapseFwRules": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"]}, "synapseModifyLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "synapseDefender": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "synapseModifyTlsVersion": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "synapseModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/951c1558-50a5-4ca3-abb6-a93e3e2367a6", "policyDefinitionReferenceId": "Dine-Synapse-Defender", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('synapseDefender')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c3624673-d2ff-48e0-b28c-5de1c6767c3c", "policyDefinitionReferenceId": "Modify-Synapse-Local-Auth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('synapseModifyLocalAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/56fd377d-098c-4f02-8406-81eb055902b8", "policyDefinitionReferenceId": "Deny-Synapse-Fw-Rules", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('synapseFwRules')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3a003702-13d2-4679-941b-937e58c443f0", "policyDefinitionReferenceId": "Deny-Synapse-Tenant-Access", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('synapseTenants')]"}, "allowedTenantIds": {"value": "[[parameters('synapseAllowedTenantIds')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/3484ce98-c0c5-4c83-994b-c5ac24785218", "policyDefinitionReferenceId": "Deny-Synapse-Data-Traffic", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('synapseDataTraffic')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2d9dbfa3-927b-4cf0-9d0f-08747f971650", "policyDefinitionReferenceId": "Deny-Synapse-Managed-Vnet", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('synapseManagedVnet')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2158ddbe-fefa-408e-b43f-d4faef8ff3b8", "policyDefinitionReferenceId": "Deny-Synapse-Local-Auth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('synapseLocalAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/8b5c654c-fb07-471b-aa8f-15fea733f140", "policyDefinitionReferenceId": "Modify-Synapse-Tls-Version", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('synapseModifyTlsVersion')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5c8cad01-ef30-4891-b230-652dadb4876a", "policyDefinitionReferenceId": "Modify-Synapse-Public-Network-Access", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('synapseModifyPublicNetworkAccess')]"}}}], "policyDefinitionGroups": null}}