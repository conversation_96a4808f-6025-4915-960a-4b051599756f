{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}, "logAnalyticsResourceId": {"type": "string", "metadata": {"description": "Provide the resourceId to the central Log Analytics workspace."}}, "emailContactAsc": {"type": "string", "metadata": {"description": "Provide the email address for the Microsoft Defender for Cloud security contact."}}, "enableAscForServers": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForServersVulnerabilityAssessments": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForSql": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForAppServices": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForStorage": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForContainers": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForKeyVault": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForSqlOnVm": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForArm": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForOssDb": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForCosmosDbs": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForCspm": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}}, "variables": {"policyDefinitions": {"deployAzureSecurity": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Deploy-MDFC-Config_20240319')]"}, "policyAssignmentNames": {"azureSecurity": "Deploy-MDFC-Config-H224", "description": "Deploy Microsoft Defender for Cloud and Security Contacts", "displayName": "Deploy Microsoft Defender for Cloud configuration"}, "nonComplianceMessage": {"message": "Microsoft Defender for Cloud and Security Contacts {enforcementMode} be deployed.", "Default": "must", "DoNotEnforce": "should"}, "rbacOwner": "8e3af657-a8ff-443c-a75c-2fe8c4bcb635", "roleAssignmentNames": {"deployAzureSecurity": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureSecurity))]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentNames').azureSecurity]", "location": "[deployment().location]", "identity": {"type": "SystemAssigned"}, "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').deployAzureSecurity]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {"emailSecurityContact": {"value": "[parameters('emailContactAsc')]"}, "logAnalytics": {"value": "[parameters('logAnalyticsResourceId')]"}, "ascExportResourceGroupName": {"value": "[concat(parameters('topLevelManagementGroupPrefix'), '-asc-export')]"}, "ascExportResourceGroupLocation": {"value": "[deployment().location]"}, "enableAscForServers": {"value": "[parameters('enableAscForServers')]"}, "enableAscForServersVulnerabilityAssessments": {"value": "[parameters('enableAscForServersVulnerabilityAssessments')]"}, "enableAscForSql": {"value": "[parameters('enableAscForSql')]"}, "enableAscForAppServices": {"value": "[parameters('enableAscForAppServices')]"}, "enableAscForStorage": {"value": "[parameters('enableAscForStorage')]"}, "enableAscForContainers": {"value": "[parameters('enableAscForContainers')]"}, "enableAscForKeyVault": {"value": "[parameters('enableAscForKeyVault')]"}, "enableAscForSqlOnVm": {"value": "[parameters('enableAscForSqlOnVm')]"}, "enableAscForArm": {"value": "[parameters('enableAscForArm')]"}, "enableAscForOssDb": {"value": "[parameters('enableAscForOssDb')]"}, "enableAscForCosmosDbs": {"value": "[parameters('enableAscForCosmosDbs')]"}, "enableAscForCspm": {"value": "[parameters('enableAscForCspm')]"}}}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').deployAzureSecurity]", "dependsOn": ["[variables('policyAssignmentNames').azureSecurity]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureSecurity), '2019-09-01', 'Full' ).identity.principalId)]"}}], "outputs": {}}