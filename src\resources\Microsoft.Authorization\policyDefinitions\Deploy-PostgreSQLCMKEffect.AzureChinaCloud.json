{"name": "Deploy-PostgreSQLCMKEffect", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "PostgreSQL servers should use customer-managed keys to encrypt data at rest", "description": "Use customer-managed keys to manage the encryption at rest of your PostgreSQL servers. By default, the data is encrypted at rest with service-managed keys, but customer-managed keys are commonly required to meet regulatory compliance standards. Customer-managed keys enable the data to be encrypted with an Azure Key Vault key created and owned by you. You have full control and responsibility for the key lifecycle, including rotation and management.", "metadata": {"version": "1.0.4", "category": "SQL", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureChinaCloud"]}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["AuditIfNotExists", "Disabled"], "defaultValue": "AuditIfNotExists"}}, "policyRule": {"if": {"field": "type", "equals": "Microsoft.DBforPostgreSQL/servers"}, "then": {"effect": "[[parameters('effect')]", "details": {"type": "Microsoft.DBforPostgreSQL/servers/keys", "existenceCondition": {"allOf": [{"field": "Microsoft.DBforPostgreSQL/servers/keys/serverKeyType", "equals": "AzureKey<PERSON>ault"}, {"field": "Microsoft.DBforPostgreSQL/servers/keys/uri", "notEquals": ""}, {"field": "Microsoft.DBforPostgreSQL/servers/keys/uri", "exists": "true"}]}}}}}}