{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"targetManagementGroupId": {"type": "string", "metadata": {"description": "Provide the management group id (e.g. 'eslz-corp')"}}, "subscriptionId": {"type": "string", "metadata": {"description": "Provide the subscriptionId you will place into the management group"}}}, "resources": [{"scope": "/", "type": "Microsoft.Management/managementGroups/subscriptions", "apiVersion": "2020-05-01", "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]", "properties": {}}], "outputs": {}}