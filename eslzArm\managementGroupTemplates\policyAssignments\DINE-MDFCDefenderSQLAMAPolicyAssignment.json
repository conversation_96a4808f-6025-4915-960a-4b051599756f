{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}, "userWorkspaceResourceId": {"type": "String", "metadata": {"displayName": "Workspace Resource Id", "description": "Workspace resource Id of the Log Analytics workspace destination for the Data Collection Rule.", "strongType": "omsWorkspace"}}, "workspaceRegion": {"type": "String", "metadata": {"displayName": "Workspace region", "description": "Region of the Log Analytics workspace destination for the Data Collection Rule.", "strongType": "location"}}, "enableCollectionOfSqlQueriesForSecurityResearch": {"type": "Bool", "metadata": {"displayName": "Enable collection of SQL queries for security research", "description": "Enable or disable the collection of SQL queries for security research."}, "allowedValues": [true, false], "defaultValue": false}, "bringYourOwnUserAssignedManagedIdentity": {"type": "Bool", "metadata": {"displayName": "Bring your own User-Assigned Managed Identity", "description": "Enable this to use your own user-assigned managed identity. The pre-created identity MUST exist otherwise the policy deployment will fail. If enabled, ensure that the user-assigned managed identity resource ID parameter matches the pre-created user-assigned managed identity resource ID. If not enabled, the policy will create a new user-assigned managed identitiy per subscription, in a new resource group named 'Built-In-Identity-RG'."}, "allowedValues": [true, false], "defaultValue": true}, "userAssignedIdentityResourceId": {"type": "String", "metadata": {"displayName": "User-Assigned Managed Identity Resource ID", "description": "The resource ID of the pre-created user-assigned managed identity. This parameter is only used when bringYourOwnUserAssignedManagedIdentity is set to true."}, "defaultValue": ""}, "bringYourOwnDcr": {"type": "Bool", "metadata": {"displayName": "Bring your own DCR", "description": "Enable this to use your own Data Collection Rule. The pre-created Data Collection Rule MUST exist otherwise the policy deployment will fail. If enabled, ensure that the Data Collection Rule Resource ID parameter matches the pre-created Data Collection Rule Resource ID. If not enabled, the policy will create a new Data Collection Rule per subscription."}, "allowedValues": [true, false], "defaultValue": true}, "dcrResourceId": {"type": "String", "metadata": {"displayName": "Data Collection Rule Resource ID", "description": "The resource ID of the user-defined Data Collection Rule. This parameter is only used when bringYourOwnDcr is set to true."}, "defaultValue": ""}, "scope": {"type": "String", "metadata": {"displayName": "<PERSON><PERSON>", "description": "Scope of the policy assignment"}}, "platformScope": {"type": "String", "metadata": {"displayName": "Platform Scope", "description": "<PERSON>ope of the reader role assignment"}, "defaultValue": "[parameters('scope')]"}}, "variables": {"policyDefinitions": {"deployazureDefenderSQL": "/providers/Microsoft.Authorization/policySetDefinitions/de01d381-bae9-4670-8870-786f89f49e26", "policyVersion": "1.*.*"}, "policyAssignmentNames": {"azureDefenderSQL": "Deploy-MDFC-DefSQL-AMA", "description": "Microsoft Defender for SQL collects events from the agents and uses them to provide security alerts and tailored hardening tasks (recommendations).", "displayName": "Enable Defender for SQL on SQL VMs and Arc-enabled SQL Servers"}, "nonComplianceMessage": {"message": "Microsoft Defender for SQL {enforcementMode} be deployed.", "Default": "must", "DoNotEnforce": "should"}, "rbacVMContributor": "9980e02c-c2be-4d73-94e8-173b1dc7cf3c", "rbacLogAnalyticsContributor": "92aaf0da-9dab-42b6-94a3-d43ce8d16293", "rbacMonitoringContributor": "749f88d5-cbae-40b8-bcfc-e573ddc772fa", "rbacManagedIdentityOperator": "f1a07417-d97a-45cb-824c-7a7467783830", "rbacContributor": "b24988ac-6180-42a0-ab88-20f7382dd24c", "rbacReader": "acdd72a7-3385-48ef-bd42-f606fba81ae7", "roleAssignmentNames": {"roleAssignmentNameLogAnalyticsContributor": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureDefenderSQL,parameters('scope')))]", "roleAssignmentNameVmContributor": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureDefenderSQL,'-2',parameters('scope')))]", "roleAssignmentNameMonitoringContributor": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureDefenderSQL,'-3',parameters('scope')))]", "roleAssignmentNameManagedIdentityOperator": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureDefenderSQL,'-4',parameters('scope')))]", "roleAssignmentNameContributor": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureDefenderSQL,'-5',parameters('scope')))]", "roleAssignmentNameReader": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureDefenderSQL,'-6',parameters('scope')))]", "roleAssignmentNamePlatformManagedIdentityOperator": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureDefenderSQL,'-7',parameters('scope')))]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').azureDefenderSQL]", "location": "[deployment().location]", "identity": {"type": "SystemAssigned"}, "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').deployazureDefenderSQL]", "definitionVersion": "[variables('policyDefinitions').policyVersion]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {"workspaceRegion": {"value": "[parameters('workspaceRegion')]"}, "userWorkspaceResourceId": {"value": "[parameters('userWorkspaceResourceId')]"}, "bringYourOwnDcr": {"value": "[parameters('bringYourOwnDcr')]"}, "dcrResourceId": {"value": "[parameters('dcrResourceId')]"}, "enableCollectionOfSqlQueriesForSecurityResearch": {"value": "[parameters('enableCollectionOfSqlQueriesForSecurityResearch')]"}, "bringYourOwnUserAssignedManagedIdentity": {"value": "[parameters('bringYourOwnUserAssignedManagedIdentity')]"}, "userAssignedIdentityResourceId": {"value": "[parameters('userAssignedIdentityResourceId')]"}}}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[variables('roleAssignmentNames').roleAssignmentNameLogAnalyticsContributor]", "dependsOn": ["[variables('policyAssignmentNames').azureDefenderSQL]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacLogAnalyticsContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureDefenderSQL), '2019-09-01', 'Full' ).identity.principalId)]"}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[variables('roleAssignmentNames').roleAssignmentNameVmContributor]", "dependsOn": ["[variables('policyAssignmentNames').azureDefenderSQL]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacVMContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureDefenderSQL), '2019-09-01', 'Full' ).identity.principalId)]"}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[variables('roleAssignmentNames').roleAssignmentNameMonitoringContributor]", "dependsOn": ["[variables('policyAssignmentNames').azureDefenderSQL]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacMonitoringContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureDefenderSQL), '2019-09-01', 'Full' ).identity.principalId)]"}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[variables('roleAssignmentNames').roleAssignmentNameManagedIdentityOperator]", "dependsOn": ["[variables('policyAssignmentNames').azureDefenderSQL]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacManagedIdentityOperator'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureDefenderSQL), '2019-09-01', 'Full' ).identity.principalId)]"}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[variables('roleAssignmentNames').roleAssignmentNameContributor]", "dependsOn": ["[variables('policyAssignmentNames').azureDefenderSQL]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureDefenderSQL), '2019-09-01', 'Full' ).identity.principalId)]"}}, {"condition": "[not(equals(parameters('platformScope'), parameters('scope')))]", "type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[variables('roleAssignmentNames').roleAssignmentNameReader]", "scope": "[parameters('platformScope')]", "dependsOn": ["[variables('policyAssignmentNames').azureDefenderSQL]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacReader'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureDefenderSQL), '2019-09-01', 'Full' ).identity.principalId)]"}}, {"condition": "[not(equals(parameters('platformScope'), parameters('scope')))]", "type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2022-04-01", "name": "[variables('roleAssignmentNames').roleAssignmentNamePlatformManagedIdentityOperator]", "scope": "[parameters('platformScope')]", "dependsOn": ["[variables('policyAssignmentNames').azureDefenderSQL]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacManagedIdentityOperator'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureDefenderSQL), '2019-09-01', 'Full' ).identity.principalId)]"}}], "outputs": {}}