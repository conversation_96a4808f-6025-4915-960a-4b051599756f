{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"logAnalyticsResourceId": {"type": "string", "metadata": {"description": "Provide the resourceId to the central Log Analytics workspace."}}}, "variables": {}, "resources": [{"type": "Microsoft.Insights/diagnosticSettings", "apiVersion": "2021-05-01-preview", "name": "toLa", "properties": {"workspaceId": "[parameters('logAnalyticsResourceId')]", "logs": [{"category": "Administrative", "enabled": true}, {"category": "Policy", "enabled": true}]}}]}