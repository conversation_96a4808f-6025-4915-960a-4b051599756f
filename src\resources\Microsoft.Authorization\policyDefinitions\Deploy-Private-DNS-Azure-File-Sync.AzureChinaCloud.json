{"name": "Deploy-Private-DNS-Azure-File-Sync", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Configure Azure File Sync to use private DNS zones", "description": "To access the private endpoint(s) for Storage Sync Service resource interfaces from a registered server, you need to configure your DNS to resolve the correct names to your private endpoint's private IP addresses. This policy creates the requisite Azure Private DNS Zone and A records for the interfaces of your Storage Sync Service private endpoint(s).", "metadata": {"version": "1.0.0", "category": "Storage", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureChinaCloud"]}, "parameters": {"privateDnsZoneId": {"type": "String", "metadata": {"displayName": "privateDnsZoneId", "strongType": "Microsoft.Network/privateDnsZones", "description": "Private DNS Zone Identifier"}}, "effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/privateEndpoints"}, {"count": {"field": "Microsoft.Network/privateEndpoints/privateLinkServiceConnections[*].groupIds[*]", "where": {"field": "Microsoft.Network/privateEndpoints/privateLinkServiceConnections[*].groupIds[*]", "equals": "afs"}}, "greaterOrEquals": 1}]}, "then": {"effect": "[[parameters('effect')]", "details": {"type": "Microsoft.Network/privateEndpoints/privateDnsZoneGroups", "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/b12aa53e-6015-4669-85d0-8515ebb3ae7f", "/providers/Microsoft.Authorization/roleDefinitions/4d97b98b-1d4f-4787-a291-c67834d212e7"], "deployment": {"properties": {"mode": "incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"privateDnsZoneId": {"type": "string"}, "privateEndpointName": {"type": "string"}, "location": {"type": "string"}}, "resources": [{"name": "[[concat(parameters('privateEndpointName'), '/deployedByPolicy')]", "type": "Microsoft.Network/privateEndpoints/privateDnsZoneGroups", "apiVersion": "2020-03-01", "location": "[[parameters('location')]", "properties": {"privateDnsZoneConfigs": [{"name": "privatelink-afs", "properties": {"privateDnsZoneId": "[[parameters('privateDnsZoneId')]"}}]}}]}, "parameters": {"privateDnsZoneId": {"value": "[[parameters('privateDnsZoneId')]"}, "privateEndpointName": {"value": "[[field('name')]"}, "location": {"value": "[[field('location')]"}}}}}}}}}