{"name": "Enforce-Guardrails-AppServices", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for App Service", "description": "This policy initiative is a group of policies that ensures App Service is compliant per regulated Landing Zones.", "metadata": {"version": "1.1.0", "category": "App Service", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"functionAppDebugging": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "appServiceDisableLocalAuth": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "appServiceSkuPl": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "appServiceDisableLocalAuthFtp": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "appServiceRouting": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "appServiceScmAuth": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "appServiceRfc": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "appServiceAppsRfc": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "appServiceAppsVnetRouting": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "appServiceEnvLatestVersion": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "appServiceAppSlotsRemoteDebugging": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "appServiceAppsRemoteDebugging": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "appServiceByoc": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "functionAppSlotsModifyHttps": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "appServiceAppHttps": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "functionAppSlotsModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "appServiceAppsModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "appServiceAppModifyPublicNetworkAccess": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-AppService-without-BYOC", "policyDefinitionReferenceId": "Deny-AppService-<PERSON><PERSON>", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceByoc')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a5e3fe8f-f6cd-4f1d-bbf6-c749754a724b", "policyDefinitionReferenceId": "Dine-AppService-Apps-Remote-Debugging", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceAppsRemoteDebugging')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/cca5adfe-626b-4cc6-8522-f5b6ed2391bd", "policyDefinitionReferenceId": "Deny-AppService-Slots-Remote-Debugging", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceAppSlotsRemoteDebugging')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/eb4d34ab-0929-491c-bbf3-61e13da19f9a", "policyDefinitionReferenceId": "Deny-AppService-Latest-Version", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceEnvLatestVersion')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/801543d1-1953-4a90-b8b0-8cf6d41473a5", "policyDefinitionReferenceId": "Deny-AppService-Vnet-Routing", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceAppsVnetRouting')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/f5c0bfb3-acea-47b1-b477-b0edcdf6edc1", "policyDefinitionReferenceId": "Deny-AppService-Rfc", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceRfc')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/a691eacb-474d-47e4-b287-b4813ca44222", "policyDefinitionReferenceId": "Deny-AppServiceApps-Rfc", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceAppsRfc')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/70adbb40-e092-42d5-a6f8-71c540a5efdb", "policyDefinitionReferenceId": "DINE-FuncApp-Debugging", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('functionAppDebugging')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5e97b776-f380-4722-a9a3-e7f0be029e79", "policyDefinitionReferenceId": "DINE-AppService-ScmAuth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceScmAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/5747353b-1ca9-42c1-a4dd-b874b894f3d4", "policyDefinitionReferenceId": "Deny-AppServ-Routing", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceRouting')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/572e342c-c920-4ef5-be2e-1ed3c6a51dc5", "policyDefinitionReferenceId": "Deny-AppServ-FtpAuth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceDisableLocalAuthFtp')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/546fe8d2-368d-4029-a418-6af48a7f61e5", "policyDefinitionReferenceId": "Deny-AppServ-SkuPl", "definitionVersion": "4.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceSkuPl')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2c034a29-2a5f-4857-b120-f800fe5549ae", "policyDefinitionReferenceId": "DINE-AppService-LocalAuth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceDisableLocalAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/25a5046c-c423-4805-9235-e844ae9ef49b", "policyDefinitionReferenceId": "DINE-AppService-Debugging", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('functionAppDebugging')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/08cf2974-d178-48a0-b26d-f6b8e555748b", "policyDefinitionReferenceId": "Modify-Function-Apps-Slots-Https", "definitionVersion": "2.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('functionAppSlotsModifyHttps')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0f98368e-36bc-4716-8ac2-8f8067203b63", "policyDefinitionReferenceId": "Modify-AppService-Https", "definitionVersion": "2.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceAppHttps')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/242222f3-4985-4e99-b5ef-086d6a6cb01c", "policyDefinitionReferenceId": "Modify-Function-Apps-Slots-Public-Network-Access", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('functionAppSlotsModifyPublicNetworkAccess')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/2374605e-3e0b-492b-9046-229af202562c", "policyDefinitionReferenceId": "Modify-AppService-Apps-Public-Network-Access", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceAppsModifyPublicNetworkAccess')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c6c3e00e-d414-4ca4-914f-406699bb8eee", "policyDefinitionReferenceId": "Modify-AppService-App-Public-Network-Access", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('appServiceAppModifyPublicNetworkAccess')]"}}}], "policyDefinitionGroups": null}}