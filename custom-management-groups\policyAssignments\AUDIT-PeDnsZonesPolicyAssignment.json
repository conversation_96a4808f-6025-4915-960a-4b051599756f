{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "policyEffect": {"type": "string", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "Audit"}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}, "privateLinkDnsZones": {"type": "array", "metadata": {"displayName": "Private Link Private DNS Zones", "description": "An array of Private Link Private DNS Zones to check for the existence of in the assigned scope."}, "defaultValue": ["privatelink.adf.azure.com", "privatelink.afs.azure.net", "privatelink.agentsvc.azure-automation.net", "privatelink.analysis.windows.net", "privatelink.api.azureml.ms", "privatelink.azconfig.io", "privatelink.azure-api.net", "privatelink.azure-automation.net", "privatelink.azurecr.io", "privatelink.azure-devices.net", "privatelink.azure-devices-provisioning.net", "privatelink.azuredatabricks.net", "privatelink.azurehdinsight.net", "privatelink.azurehealthcareapis.com", "privatelink.azurestaticapps.net", "privatelink.azuresynapse.net", "privatelink.azurewebsites.net", "privatelink.batch.azure.com", "privatelink.blob.core.windows.net", "privatelink.cassandra.cosmos.azure.com", "privatelink.cognitiveservices.azure.com", "privatelink.database.windows.net", "privatelink.datafactory.azure.net", "privatelink.dev.azuresynapse.net", "privatelink.dfs.core.windows.net", "privatelink.dicom.azurehealthcareapis.com", "privatelink.digitaltwins.azure.net", "privatelink.directline.botframework.com", "privatelink.documents.azure.com", "privatelink.eventgrid.azure.net", "privatelink.file.core.windows.net", "privatelink.gremlin.cosmos.azure.com", "privatelink.guestconfiguration.azure.com", "privatelink.his.arc.azure.com", "privatelink.kubernetesconfiguration.azure.com", "privatelink.managedhsm.azure.net", "privatelink.mariadb.database.azure.com", "privatelink.media.azure.net", "privatelink.mongo.cosmos.azure.com", "privatelink.monitor.azure.com", "privatelink.mysql.database.azure.com", "privatelink.notebooks.azure.net", "privatelink.ods.opinsights.azure.com", "privatelink.oms.opinsights.azure.com", "privatelink.pbidedicated.windows.net", "privatelink.postgres.database.azure.com", "privatelink.prod.migration.windowsazure.com", "privatelink.purview.azure.com", "privatelink.purviewstudio.azure.com", "privatelink.queue.core.windows.net", "privatelink.redis.cache.windows.net", "privatelink.redisenterprise.cache.azure.net", "privatelink.search.windows.net", "privatelink.service.signalr.net", "privatelink.servicebus.windows.net", "privatelink.siterecovery.windowsazure.com", "privatelink.sql.azuresynapse.net", "privatelink.table.core.windows.net", "privatelink.table.cosmos.azure.com", "privatelink.tip1.powerquery.microsoft.com", "privatelink.token.botframework.com", "privatelink.vaultcore.azure.net", "privatelink.web.core.windows.net", "privatelink.webpubsub.azure.com"]}}, "variables": {"policyDefinitions": {"auditPeDnsZones": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyDefinitions/Audit-PrivateLinkDnsZones')]"}, "policyAssignmentNames": {"auditPeDnsZones": "Audit-PeDnsZones", "description": "Audits the deployment of Private Link Private DNS Zone resources in the Corp landing zone.", "displayName": "Audit Private Link Private DNS Zone resources"}, "nonComplianceMessage": {"message": "Private Link Private DNS Zone resources {enforcementMode} be deployed in the Corp landing zone.", "Default": "must", "DoNotEnforce": "should"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentNames').auditPeDnsZones]", "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').auditPeDnsZones]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {"privateLinkDnsZones": {"value": "[parameters('privateLinkDnsZones')]"}, "effect": {"value": "[parameters('policyEffect')]"}}}}], "outputs": {}}