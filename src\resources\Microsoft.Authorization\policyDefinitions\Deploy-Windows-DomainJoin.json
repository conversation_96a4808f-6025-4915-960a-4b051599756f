{"name": "Deploy-Windows-DomainJoin", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "Indexed", "displayName": "Deploy Windows Domain Join Extension with keyvault configuration", "description": "Deploy Windows Domain Join Extension with keyvault configuration when the extension does not exist on a given windows Virtual Machine", "metadata": {"version": "1.1.0", "category": "Guest Configuration", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"domainUsername": {"type": "String", "metadata": {"displayName": "domainUsername"}}, "domainPassword": {"type": "String", "metadata": {"displayName": "domainPassword"}}, "domainFQDN": {"type": "String", "metadata": {"displayName": "domainFQDN"}}, "domainOUPath": {"type": "String", "metadata": {"displayName": "domainOUPath"}}, "keyVaultResourceId": {"type": "String", "metadata": {"displayName": "keyVaultResourceId"}}, "effect": {"type": "String", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Compute/virtualMachines"}, {"field": "Microsoft.Compute/imagePublisher", "equals": "MicrosoftWindowsServer"}, {"field": "Microsoft.Compute/imageOffer", "equals": "WindowsServer"}, {"anyOf": [{"field": "Microsoft.Compute/imageSku", "like": "2008-R2-SP1*"}, {"field": "Microsoft.Compute/imageSku", "like": "2012-*"}, {"field": "Microsoft.Compute/imageSku", "like": "2016-*"}, {"field": "Microsoft.Compute/imageSku", "like": "2019-*"}, {"field": "Microsoft.Compute/imageSku", "like": "2022-*"}, {"field": "Microsoft.Compute/imageSku", "like": "2025-*"}]}]}, "then": {"effect": "[[parameters('effect')]", "details": {"type": "Microsoft.Compute/virtualMachines/extensions", "roleDefinitionIds": ["/providers/Microsoft.Authorization/roleDefinitions/9980e02c-c2be-4d73-94e8-173b1dc7cf3c", "/providers/Microsoft.Authorization/roleDefinitions/4633458b-17de-408a-b874-0445c86b69e6"], "existenceCondition": {"allOf": [{"field": "Microsoft.Compute/virtualMachines/extensions/type", "equals": "JsonADDomainExtension"}, {"field": "Microsoft.Compute/virtualMachines/extensions/publisher", "equals": "Microsoft.Compute"}]}, "deployment": {"properties": {"mode": "Incremental", "parameters": {"vmName": {"value": "[[field('name')]"}, "location": {"value": "[[field('location')]"}, "domainUsername": {"reference": {"keyVault": {"id": "[[parameters('keyVaultResourceId')]"}, "secretName": "[[parameters('domainUsername')]"}}, "domainPassword": {"reference": {"keyVault": {"id": "[[parameters('keyVaultResourceId')]"}, "secretName": "[[parameters('domainPassword')]"}}, "domainOUPath": {"value": "[[parameters('domainOUPath')]"}, "domainFQDN": {"value": "[[parameters('domainFQDN')]"}, "keyVaultResourceId": {"value": "[[parameters('keyVaultResourceId')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"vmName": {"type": "String"}, "location": {"type": "String"}, "domainUsername": {"type": "String"}, "domainPassword": {"type": "securestring"}, "domainFQDN": {"type": "String"}, "domainOUPath": {"type": "String"}, "keyVaultResourceId": {"type": "String"}}, "variables": {"domainJoinOptions": 3, "vmName": "[[parameters('vmName')]"}, "resources": [{"apiVersion": "2015-06-15", "type": "Microsoft.Compute/virtualMachines/extensions", "name": "[[concat(variables('vmName'),'/joindomain')]", "location": "[[resourceGroup().location]", "properties": {"publisher": "Microsoft.Compute", "type": "JsonADDomainExtension", "typeHandlerVersion": "1.3", "autoUpgradeMinorVersion": true, "settings": {"Name": "[[parameters('domainFQDN')]", "User": "[[parameters('domainUserName')]", "Restart": "true", "Options": "[[variables('domainJoinOptions')]", "OUPath": "[[parameters('domainOUPath')]"}, "protectedSettings": {"Password": "[[parameters('domainPassword')]"}}}], "outputs": {}}}}}}}}}