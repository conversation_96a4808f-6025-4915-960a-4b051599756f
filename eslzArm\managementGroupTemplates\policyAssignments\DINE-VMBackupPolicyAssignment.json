{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}}, "variables": {"policyDefinitions": {"deployVmBackup": "/providers/Microsoft.Authorization/policyDefinitions/98d0b9f8-fd90-49c9-88e2-d3baf3b0dd86", "policyVersion": "9.*.*"}, "policyAssignmentNames": {"deployVmBackup": "Deploy-VM-Backup", "description": "Enforce backup for all virtual machines by deploying a recovery services vault in the same location and resource group as the virtual machine. Doing this is useful when different application teams in your organization are allocated separate resource groups and need to manage their own backups and restores. You can optionally exclude virtual machines containing a specified tag to control the scope of assignment. See https://aka.ms/AzureVMAppCentricBackupExcludeTag.", "displayName": "Configure backup on virtual machines without a given tag to a new recovery services vault with a default policy"}, "nonComplianceMessage": {"message": "Backup on virtual machines without a given tag {enforcementMode} be configured to a new recovery services vault with a default policy.", "Default": "must", "DoNotEnforce": "should"}, "rbacVMContributor": "9980e02c-c2be-4d73-94e8-173b1dc7cf3c", "rbacBackupContributor": "5e467623-bb1f-42f4-a55d-6e525e11384b", "roleAssignmentNames": {"roleAssignmentNameBackupContributor": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').deployVmBackup,'-1'))]", "roleAssignmentNameVmContributor": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').deployVmBackup,'-2'))]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2024-04-01", "name": "[variables('policyAssignmentNames').deployVmBackup]", "location": "[deployment().location]", "identity": {"type": "SystemAssigned"}, "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').deployVmBackup]", "definitionVersion": "[variables('policyDefinitions').policyVersion]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}], "parameters": {}}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').roleAssignmentNameBackupContributor]", "dependsOn": ["[variables('policyAssignmentNames').deployVmBackup]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacBackupContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').deployVmBackup), '2019-09-01', 'Full' ).identity.principalId)]"}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').roleAssignmentNameVmContributor]", "dependsOn": ["[variables('policyAssignmentNames').deployVmBackup]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacVMContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').deployVmBackup), '2019-09-01', 'Full' ).identity.principalId)]"}}], "outputs": {}}