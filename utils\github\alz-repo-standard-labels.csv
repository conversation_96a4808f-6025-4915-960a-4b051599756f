Name,Description,HEX
Area: Accelerator :zap:,Issues / PR's related to Accelerators,ECBA82
Area: Bicep Registry :file_cabinet:,Issues / PR's related to Bicep Registry,81C14B
Area: Diagnostic Settings :test_tube:,Issues / PR's related to Diagnostic Settings,2E933C
Area: Logging & Automation :camera:,Issues / PR's related to Logging & Automation,BFCC94
Area: Management Groups :beers:,Issues / PR's related to Management Groups,204E4A
Area: MDFC :lock:,Issues / PR's related to Microsoft Defender for Cloud,B24C63
Area: Networking :globe_with_meridians:,Issues / PR's related to Networking,5438DC
Area: Non-Resource Specific :label:,"Things like tags, location etc.",357DED
Area: Orchestration Modules :recycle:,Modules that wrap/orchestrate other modules,56EEF4
Area: Policy :pencil:,Issues / PR's related to Policy,32E875
Area: RBAC :passport_control:,Issues / PR's related to RBAC,C1EEFF
Area: Sovereign :alien:,"GH issues raised for sovereign clouds (US Gov, China)",655356
Needs: Attention :wave:,Needs attention from the maintainers,E99695
Needs: Author Feedback :ear:,Needs the author to provide feedback,F18A07
Needs: External Changes :gear:,When an issue/PR requires changes that are outside of the control of this repo,DE389D
Needs: More Evidence :balance_scale:,We are looking for more evidence to make a decision on this,F64872
Needs: Triage :mag:,Needs triaging by the team,FBCA04
Needs: Upstream Policy Changes :arrows_clockwise:,Upstream ESLZ repo policy changes required,513B3C
Status: Awaiting Release To Be Cut :scissors:,"This is fixed in the main branch but not in the latest release, will be fixed with next release cut",017438
Status: Blocked :brick:,Something is blocking us from fixing this,D8DBE2
Status: Do Not Merge :no_entry:,Do not merge PRs with this label attached as they are not ready etc.,C62A4B
Status: External Contribution :earth_americas:,This is being worked on by someone outside of the owners/contributors or core team,D8FA2C
Status: Fixed :white_check_mark:,Auto label applied when issue fixed by merged PR,ededed
Status: Help Wanted :sos:,Extra attention is needed,008672
Status: In PR :point_right:,This is when an issue is due to be fixed in an open PR,344966
Status: Invalid :x:,This doesn't seem right,e4e669
Status: Long Term :hourglass:,"We will do it, but will take a longer amount of time due to complexity/priorities",B60205
Status: Module Orphaned :eyes:,The module has no owner and is therefore orphaned at this time,A9BCD0
Status: No Recent Activity :zzz:,"No recent activity, will eb closed automatically soon unless modified",58A4B0
Status: PR Merged :arrow_up:,Issue has been merged in a PR,373F51
Status: PR Modified Workflows :warning:,PR contains changes to GitHub Actions,DAA49A
Status: PR Referenced :link:,Issue is referenced in a PR,BF4E30
Status: PR Safe To Test :ballot_box_with_check:,PRs can run deployment tests,78FECF
Status: Waiting For Response :speech_balloon:,Waiting for a response ,A11692
Status: Wont Fix :-1:,This will not be worked on,821028
Type: Auto-Merge :heavy_check_mark:,Automatically merges,17183B
Type: Bot :robot_face:,Created by an actual robot,7B7554
Type: Bug :beetle:,Something isn't working,d73a4a
Type: Documentation :page_facing_up:,Improvements or additions to documentation,0075ca
Type: Duplicate :palms_up_together:,This issue or pull request already exists,cfd3d7
Type: Enhancement :sparkles:,New feature or request,FFE347
Type: External Contribution :construction_worker:,This issue or pull request already exists,DEC1FF
Type: Feature Request :heavy_plus_sign:,New feature or request,a2eeef
Type: Good First Issue :green_heart:,Good for newcomers,5CC8FF
Type: Hygiene :broom:,"Things related to testing, issue triage etc.",7D7ABC
Type: New Module Proposal :bulb:,A new module for AVM is being proposed,17016A
Type: Question / Feedback :question::ear:,Further information is requested or just some feedback,CB6BA2
Type: Resolution Duplicate :palms_up_together:,Issue is a duplicate and will be closed,6457A6
Type: Security Bug :lock:,This is a security bug,344966
Type: Upstream Dependency :arrow_up:,something must happen before start something else,E6AACE
Test: Standard :test_tube:, Standard portal deployment test without networking,#E83F1D
Test: Hub & Spoke :test_tube:, Hub and spoke portal deployment test with full networking,#E83F1D
Test: VWAN :test_tube:, Virtual WAN portal deployment test with full networking,#E83F1D