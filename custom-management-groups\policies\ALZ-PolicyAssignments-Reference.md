# Azure Landing Zones - Policy Assignments Reference

Đ<PERSON>y là danh sách tất cả các policy assignments được sử dụng trong Azure Landing Zones managementGroupTemplates.

## 🔍 AUDIT Policies

### AUDIT-AppGwWafPolicyAssignment
- **<PERSON><PERSON><PERSON> đích**: Audit Application Gateway WAF configuration
- **Scope**: Landing Zones
- **Effect**: Audit

### AUDIT-PeDnsZonesPolicyAssignment  
- **Mục đích**: Audit Private Endpoint DNS Zones deployment
- **Scope**: Corp Landing Zone
- **Effect**: Audit

### AUDIT-ResourceRGLocationPolicyAssignment
- **M<PERSON><PERSON> đích**: Audit resource and resource group location compliance
- **Scope**: Root Management Group
- **Effect**: Audit

### AUDIT-TrustedLaunchPolicyAssignment
- **M<PERSON><PERSON> đích**: Audit VMs for Trusted Launch compliance
- **Scope**: Landing Zones
- **Effect**: Audit

### AUDIT-UnusedResourcesPolicyAssignment
- **<PERSON><PERSON>c đích**: Audit unused resources for cost optimization
- **Scope**: Landing Zones
- **Effect**: Audit

### AUDIT-ZoneResilientPolicyAssignment
- **M<PERSON><PERSON> đích**: Audit resources for zone resiliency
- **Scope**: Landing Zones
- **Effect**: Audit

## 🚫 DENY Policies

### DENY-AksPrivEscalationPolicyAssignment
- **Mục đích**: Deny AKS privilege escalation
- **Scope**: Landing Zones
- **Effect**: Deny

### DENY-AksPrivilegedPolicyAssignment
- **Mục đích**: Deny privileged containers in AKS
- **Scope**: Landing Zones
- **Effect**: Deny

### DENY-AksWithoutHttpsPolicyAssignment
- **Mục đích**: Deny AKS ingress without HTTPS
- **Scope**: Landing Zones
- **Effect**: Deny

### DENY-ClassicResourceTypesPolicyAssignment
- **Mục đích**: Deny classic resource types
- **Scope**: Root Management Group
- **Effect**: Deny

### DENY-HybridNetworkingPolicyAssignment
- **Mục đích**: Deny hybrid networking in Corp Landing Zone
- **Scope**: Corp Landing Zone
- **Effect**: Deny

### DENY-IPForwardingPolicyAssignment
- **Mục đích**: Deny IP forwarding on NICs
- **Scope**: Landing Zones
- **Effect**: Deny

### DENY-MgmtPortsFromInternetPolicyAssignment
- **Mục đích**: Deny management ports from internet
- **Scope**: Landing Zones
- **Effect**: Deny

### DENY-PublicEndpointPolicyAssignment
- **Mục đích**: Deny public endpoints for PaaS services
- **Scope**: Corp Landing Zone
- **Effect**: Deny

### DENY-PublicIpAddressPolicyAssignment
- **Mục đích**: Deny public IP addresses
- **Scope**: Corp Landing Zone
- **Effect**: Deny

### DENY-StorageWithoutHttpsPolicyAssignment
- **Mục đích**: Deny storage accounts without HTTPS
- **Scope**: Landing Zones
- **Effect**: Deny

### DENY-SubnetWithoutNsgPolicyAssignment
- **Mục đích**: Deny subnets without NSG
- **Scope**: Landing Zones
- **Effect**: Deny

### DENY-VMUnmanagedDiskPolicyAssignment
- **Mục đích**: Deny VMs with unmanaged disks
- **Scope**: Landing Zones
- **Effect**: Deny

## 🔧 DINE (DeployIfNotExists) Policies

### DINE-ASBPolicyAssignment
- **Mục đích**: Deploy Azure Security Benchmark
- **Scope**: Root Management Group
- **Effect**: DeployIfNotExists

### DINE-ActivityLogPolicyAssignment
- **Mục đích**: Deploy Activity Log to Log Analytics
- **Scope**: Root Management Group
- **Effect**: DeployIfNotExists

### DINE-AtpOssDbPolicyAssignment
- **Mục đích**: Deploy Advanced Threat Protection for OSS databases
- **Scope**: Landing Zones
- **Effect**: DeployIfNotExists

### DINE-AtpSqlDbPolicyAssignment
- **Mục đích**: Deploy Advanced Threat Protection for SQL databases
- **Scope**: Landing Zones
- **Effect**: DeployIfNotExists

### DINE-ChangeTrackingVMPolicyAssignment
- **Mục đích**: Deploy Change Tracking for VMs
- **Scope**: Landing Zones
- **Effect**: DeployIfNotExists

### DINE-MDEndpointsAMAPolicyAssignment
- **Mục đích**: Deploy Microsoft Defender for Endpoints with AMA
- **Scope**: Landing Zones
- **Effect**: DeployIfNotExists

### DINE-MDFCConfigPolicyAssignment
- **Mục đích**: Deploy Microsoft Defender for Cloud configuration
- **Scope**: Root Management Group
- **Effect**: DeployIfNotExists

### DINE-PrivateDNSZonesPolicyAssignment
- **Mục đích**: Deploy Private DNS Zones for PaaS services
- **Scope**: Connectivity Management Group
- **Effect**: DeployIfNotExists

### DINE-ResourceDiagnosticsPolicyAssignment
- **Mục đích**: Deploy diagnostic settings for resources
- **Scope**: Landing Zones
- **Effect**: DeployIfNotExists

### DINE-SQLAuditingPolicyAssignment
- **Mục đích**: Deploy SQL auditing
- **Scope**: Landing Zones
- **Effect**: DeployIfNotExists

### DINE-SQLEncryptionPolicyAssignment
- **Mục đích**: Deploy SQL Transparent Data Encryption
- **Scope**: Landing Zones
- **Effect**: DeployIfNotExists

### DINE-SQLThreatPolicyAssignment
- **Mục đích**: Deploy SQL Threat Detection
- **Scope**: Landing Zones
- **Effect**: DeployIfNotExists

### DINE-VMBackupPolicyAssignment
- **Mục đích**: Deploy VM backup
- **Scope**: Landing Zones
- **Effect**: DeployIfNotExists

### DINE-VMMonitoringPolicyAssignment
- **Mục đích**: Deploy VM monitoring with Azure Monitor Agent
- **Scope**: Landing Zones
- **Effect**: DeployIfNotExists

## 🛡️ ENFORCE Policies (Guardrails)

### ENFORCE-ALZ-SandboxPolicyAssignment
- **Mục đích**: Enforce ALZ Sandbox guardrails
- **Scope**: Sandbox Management Group
- **Effect**: Various (Deny, Audit)

### ENFORCE-ALZ-DecommissionedPolicyAssignment
- **Mục đích**: Enforce policies for decommissioned resources
- **Scope**: Decommissioned Management Group
- **Effect**: Deny

### ENFORCE-GuardrailsAPIMPolicyAssignment
- **Mục đích**: Enforce guardrails for API Management
- **Scope**: Landing Zones
- **Effect**: Various

### ENFORCE-GuardrailsAppServicesPolicyAssignment
- **Mục đích**: Enforce guardrails for App Services
- **Scope**: Landing Zones
- **Effect**: Various

### ENFORCE-GuardrailsComputePolicyAssignment
- **Mục đích**: Enforce guardrails for Compute resources
- **Scope**: Landing Zones
- **Effect**: Various

### ENFORCE-GuardrailsKeyVaultPolicyAssignment
- **Mục đích**: Enforce guardrails for Key Vault
- **Scope**: Landing Zones
- **Effect**: Various

### ENFORCE-GuardrailsKubernetesPolicyAssignment
- **Mục đích**: Enforce guardrails for Kubernetes
- **Scope**: Landing Zones
- **Effect**: Various

### ENFORCE-GuardrailsNetworkPolicyAssignment
- **Mục đích**: Enforce guardrails for Network resources
- **Scope**: Landing Zones
- **Effect**: Various

### ENFORCE-GuardrailsSQLPolicyAssignment
- **Mục đích**: Enforce guardrails for SQL resources
- **Scope**: Landing Zones
- **Effect**: Various

### ENFORCE-GuardrailsStoragePolicyAssignment
- **Mục đích**: Enforce guardrails for Storage accounts
- **Scope**: Landing Zones
- **Effect**: Various

## 🔄 MODIFY Policies

### MODIFY-AUM-CheckUpdatesPolicyAssignment
- **Mục đích**: Modify Azure Update Manager check updates
- **Scope**: Landing Zones
- **Effect**: Modify

### MODIFY-DDoSPolicyAssignment
- **Mục đích**: Modify DDoS protection on VNets
- **Scope**: Landing Zones
- **Effect**: Modify

## 🚨 DENYACTION Policies

### DENYACTION-DeleteUAMIAMAPolicyAssignment
- **Mục đích**: Deny deletion of User Assigned Managed Identity used by AMA
- **Scope**: Landing Zones
- **Effect**: DenyAction
