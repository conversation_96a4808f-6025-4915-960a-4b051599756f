{"name": "Deny-PublicPaaSEndpoints", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Public network access should be disabled for PaaS services", "description": "This policy initiative is a group of policies that prevents creation of Azure PaaS services with exposed public endpoints", "metadata": {"version": "1.0.0", "category": "Network", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureUSGovernment"]}, "parameters": {"CosmosPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for CosmosDB", "description": "This policy denies that  Cosmos database accounts  are created with out public network access is disabled."}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "KeyVaultPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for KeyVault", "description": "This policy denies creation of Key Vaults with IP Firewall exposed to all public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "SqlServerPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access on Azure SQL Database should be disabled", "description": "This policy denies creation of Sql servers with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "StoragePublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access onStorage accounts should be disabled", "description": "This policy denies creation of storage accounts with IP Firewall exposed to all public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "AKSPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access on AKS API should be disabled", "description": "This policy denies  the creation of  Azure Kubernetes Service non-private clusters"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "ACRPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access on Azure Container Registry disabled", "description": "This policy denies the creation of Azure Container Registires with exposed public endpoints "}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "AFSPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access on Azure File Sync disabled", "description": "This policy denies the creation of Azure File Sync instances with exposed public endpoints "}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "BatchPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for Azure Batch Instances", "description": "This policy denies creation of Azure Batch Instances with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}, "MariaDbPublicIpDenyEffect": {"type": "String", "metadata": {"displayName": "Public network access should be disabled for Azure MariaDB", "description": "This policy denies creation of Azure MariaDB with exposed public endpoints"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyDefinitions": [{"policyDefinitionReferenceId": "CosmosDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/797b37f7-06b8-444c-b1ad-fc62867f335a", "parameters": {"effect": {"value": "[[parameters('CosmosPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "KeyVaultDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/55615ac9-af46-4a59-874e-391cc3dfb490", "parameters": {"effect": {"value": "[[parameters('KeyVaultPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "SqlServerDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1b8ca024-1d5c-4dec-8995-b1a932b41780", "parameters": {"effect": {"value": "[[parameters('SqlServerPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "StorageDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/34c877ad-507e-4c82-993e-3452a6e0ad3c", "parameters": {"effect": {"value": "[[parameters('StoragePublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AKSDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/040732e8-d947-40b8-95d6-854c95024bf8", "parameters": {"effect": {"value": "[[parameters('AKSPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "ACRDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0fdf0491-d080-4575-b627-ad0e843cba0f", "parameters": {"effect": {"value": "[[parameters('ACRPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "AFSDenyPaasPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/21a8cd35-125e-4d13-b82d-2e19b7208bb7", "parameters": {"effect": {"value": "[[parameters('AFSPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "BatchDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/74c5a0ae-5e48-4738-b093-65e23a060488", "parameters": {"effect": {"value": "[[parameters('BatchPublicIpDenyEffect')]"}}, "groupNames": []}, {"policyDefinitionReferenceId": "MariaDbDenyPublicIP", "policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-PublicEndpoint-MariaDB", "parameters": {"effect": {"value": "[[parameters('MariaDbPublicIpDenyEffect')]"}}, "groupNames": []}], "policyDefinitionGroups": null}}