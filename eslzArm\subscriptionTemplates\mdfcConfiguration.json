{"$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"enableAscForServers": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForCosmosDbs": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForSql": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForSqlOnVm": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForArm": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForOssDb": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForAppServices": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForKeyVault": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForStorage": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForContainers": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForApis": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "enableAscForCspm": {"type": "String", "allowedValues": ["DeployIfNotExists", "Disabled"], "defaultValue": "DeployIfNotExists", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "resourceGroupLocation": {"type": "String", "metadata": {"displayName": "Resource group location", "description": "The resource group name where the export to Log Analytics workspace configuration is created. If you enter a name for a resource group that doesn't exist, it'll be created in the subscription. Note that each resource group can only have one export to Log Analytics workspace configured.", "strongType": "location"}}, "resourceGroupName": {"type": "String", "metadata": {"displayName": "Resource group name", "description": "The name of the resource group hosting the Log Analytics workspace."}}, "logAnalyticsResourceId": {"type": "String", "metadata": {"displayName": "Log Analytics workspace", "description": "The Log Analytics workspace of where the data should be exported to.", "strongType": "Microsoft.OperationalInsights/workspaces", "assignPermissions": true}}, "emailContactAsc": {"type": "String", "metadata": {"displayName": "Resource group name", "description": "The resource group name where the export to Log Analytics workspace configuration is created. If you enter a name for a resource group that doesn't exist, it'll be created in the subscription. Note that each resource group can only have one export to Log Analytics workspace configured."}}, "exportedDataTypes": {"type": "Array", "metadata": {"displayName": "Exported data types", "description": "The data types to be exported. To export a snapshot (preview) of the data once a week, choose the data types which contains 'snapshot', other data types will be sent in real-time streaming."}, "allowedValues": ["Security recommendations", "Security alerts", "Overall secure score", "Secure score controls", "Regulatory compliance", "Overall secure score - snapshot", "Secure score controls - snapshot", "Regulatory compliance - snapshot", "Security recommendations - snapshot", "Security findings - snapshot"], "defaultValue": ["Security recommendations", "Security alerts", "Overall secure score", "Secure score controls", "Regulatory compliance", "Overall secure score - snapshot", "Secure score controls - snapshot", "Regulatory compliance - snapshot", "Security recommendations - snapshot", "Security findings - snapshot"]}, "recommendationNames": {"type": "Array", "metadata": {"displayName": "Recommendation IDs", "description": "Applicable only for export of security recommendations. To export all recommendations, leave this empty. To export specific recommendations, enter a list of recommendation IDs separated by semicolons (';'). Recommendation IDs are available through the Assessments API (https://docs.microsoft.com/rest/api/securitycenter/assessments), or Azure Resource Graph Explorer, choose securityresources and microsoft.security/assessments."}, "defaultValue": []}, "recommendationSeverities": {"type": "Array", "metadata": {"displayName": "Recommendation severities", "description": "Applicable only for export of security recommendations. Determines recommendation severities. Example: High;Medium;Low;"}, "allowedValues": ["High", "Medium", "Low"], "defaultValue": ["High", "Medium", "Low"]}, "isSecurityFindingsEnabled": {"type": "bool", "metadata": {"displayName": "Include security findings", "description": "Security findings are results from vulnerability assessment solutions, and can be thought of as 'sub' recommendations grouped into a 'parent' recommendation."}, "allowedValues": [true, false], "defaultValue": true}, "secureScoreControlsNames": {"type": "Array", "metadata": {"displayName": "Secure Score Controls IDs", "description": "Applicable only for export of secure score controls. To export all secure score controls, leave this empty. To export specific secure score controls, enter a list of secure score controls IDs separated by semicolons (';'). Secure score controls IDs are available through the Secure score controls API (https://docs.microsoft.com/rest/api/securitycenter/securescorecontrols), or Azure Resource Graph Explorer, choose securityresources and microsoft.security/securescores/securescorecontrols."}, "defaultValue": []}, "alertSeverities": {"type": "Array", "metadata": {"displayName": "Alert severities", "description": "Applicable only for export of security alerts. Determines alert severities. Example: High;Medium;Low;"}, "allowedValues": ["High", "Medium", "Low"], "defaultValue": ["High", "Medium", "Low"]}, "regulatoryComplianceStandardsNames": {"type": "Array", "metadata": {"displayName": "Regulatory compliance standards names", "description": "Applicable only for export of regulatory compliance. To export all regulatory compliance, leave this empty. To export specific regulatory compliance standards, enter a list of these standards names separated by semicolons (';'). Regulatory compliance standards names are available through the regulatory compliance standards API (https://docs.microsoft.com/rest/api/securitycenter/regulatorycompliancestandards), or Azure Resource Graph Explorer, choose securityresources and microsoft.security/regulatorycompliancestandards."}, "defaultValue": []}, "guidValue": {"type": "string", "defaultValue": "[newGuid()]"}}, "variables": {"scopeDescription": "scope for subscription {0}", "subAssessmentRuleExpectedValue": "/assessments/{0}/", "recommendationNamesLength": "[length(parameters('recommendationNames'))]", "secureScoreControlsNamesLength": "[length(parameters('secureScoreControlsNames'))]", "secureScoreControlsLengthIfEmpty": "[if(equals(variables('secureScoreControlsNamesLength'), 0), 1, variables('secureScoreControlsNamesLength'))]", "regulatoryComplianceStandardsNamesLength": "[length(parameters('regulatoryComplianceStandardsNames'))]", "regulatoryComplianceStandardsNamesLengthIfEmpty": "[if(equals(variables('regulatoryComplianceStandardsNamesLength'), 0), 1, variables('regulatoryComplianceStandardsNamesLength'))]", "recommendationSeveritiesLength": "[length(parameters('recommendationSeverities'))]", "alertSeveritiesLength": "[length(parameters('alertSeverities'))]", "recommendationNamesLengthIfEmpty": "[if(equals(variables('recommendationNamesLength'), 0), 1, variables('recommendationNamesLength'))]", "recommendationSeveritiesLengthIfEmpty": "[if(equals(variables('recommendationSeveritiesLength'), 0), 1, variables('recommendationSeveritiesLength'))]", "alertSeveritiesLengthIfEmpty": "[if(equals(variables('alertSeveritiesLength'), 0), 1, variables('alertSeveritiesLength'))]", "totalRuleCombinationsForOneRecommendationName": "[variables('recommendationSeveritiesLengthIfEmpty')]", "totalRuleCombinationsForOneRecommendationSeverity": 1, "exportedDataTypesLength": "[length(parameters('exportedDataTypes'))]", "exportedDataTypesLengthIfEmpty": "[if(equals(variables('exportedDataTypesLength'), 0), 1, variables('exportedDataTypesLength'))]", "dataTypeMap": {"Security recommendations": "Assessments", "Security alerts": "<PERSON><PERSON><PERSON>", "Overall secure score": "SecureScores", "Secure score controls": "SecureScoreControls", "Regulatory compliance": "RegulatoryComplianceAssessment", "Overall secure score - snapshot": "SecureScoresSnapshot", "Secure score controls - snapshot": "SecureScoreControlsSnapshot", "Regulatory compliance - snapshot": "RegulatoryComplianceAssessmentSnapshot", "Security recommendations - snapshot": "AssessmentsSnapshot", "Security findings - snapshot": "SubAssessmentsSnapshot"}, "alertSeverityMap": {"High": "high", "Medium": "medium", "Low": "low"}, "ruleSetsForAssessmentsObj": {"copy": [{"name": "ruleSetsForAssessmentsArr", "count": "[mul(variables('recommendationNamesLengthIfEmpty'),variables('recommendationSeveritiesLengthIfEmpty'))]", "input": {"rules": [{"propertyJPath": "[if(equals(variables('recommendationNamesLength'),0),'type','name')]", "propertyType": "string", "expectedValue": "[if(equals(variables('recommendationNamesLength'),0),'Microsoft.Security/assessments',parameters('recommendationNames')[mod(div(copyIndex('ruleSetsForAssessmentsArr'),variables('totalRuleCombinationsForOneRecommendationName')),variables('recommendationNamesLength'))])]", "operator": "Contains"}, {"propertyJPath": "properties.metadata.severity", "propertyType": "string", "expectedValue": "[parameters('recommendationSeverities')[mod(div(copyIndex('ruleSetsForAssessmentsArr'),variables('totalRuleCombinationsForOneRecommendationSeverity')),variables('recommendationSeveritiesLength'))]]", "operator": "Equals"}]}}]}, "customRuleSetsForSubAssessmentsObj": {"copy": [{"name": "ruleSetsForSubAssessmentsArr", "count": "[variables('recommendationNamesLengthIfEmpty')]", "input": {"rules": [{"propertyJPath": "id", "propertyType": "string", "expectedValue": "[if(equals(variables('recommendationNamesLength'), 0), json('null'), replace(variables('subAssessmentRuleExpectedValue'),'{0}', parameters('recommendationNames')[copyIndex('ruleSetsForSubAssessmentsArr')]))]", "operator": "Contains"}]}}]}, "ruleSetsForAlertsObj": {"copy": [{"name": "ruleSetsForAlertsArr", "count": "[variables('alertSeveritiesLengthIfEmpty')]", "input": {"rules": [{"propertyJPath": "Severity", "propertyType": "string", "expectedValue": "[variables('alertSeverityMap')[parameters('alertSeverities')[mod(copyIndex('ruleSetsForAlertsArr'),variables('alertSeveritiesLengthIfEmpty'))]]]", "operator": "Equals"}]}}]}, "customRuleSetsForSecureScoreControlsObj": {"copy": [{"name": "ruleSetsForSecureScoreControlsArr", "count": "[variables('secureScoreControlsLengthIfEmpty')]", "input": {"rules": [{"propertyJPath": "name", "propertyType": "string", "expectedValue": "[if(equals(variables('secureScoreControlsNamesLength'), 0), json('null'), parameters('secureScoreControlsNames')[copyIndex('ruleSetsForSecureScoreControlsArr')])]", "operator": "Equals"}]}}]}, "customRuleSetsForRegulatoryComplianceObj": {"copy": [{"name": "ruleSetsForRegulatoryCompliancArr", "count": "[variables('regulatoryComplianceStandardsNamesLengthIfEmpty')]", "input": {"rules": [{"propertyJPath": "id", "propertyType": "string", "expectedValue": "[if(equals(variables('regulatoryComplianceStandardsNamesLength'), 0), json('null'), parameters('regulatoryComplianceStandardsNames')[copyIndex('ruleSetsForRegulatoryCompliancArr')])]", "operator": "Contains"}]}}]}, "ruleSetsForSecureScoreControlsObj": "[if(equals(variables('secureScoreControlsNamesLength'), 0), json('null'), variables('customRuleSetsForSecureScoreControlsObj').ruleSetsForSecureScoreControlsArr)]", "ruleSetsForSecureRegulatoryComplianceObj": "[if(equals(variables('regulatoryComplianceStandardsNamesLength'), 0), json('null'), variables('customRuleSetsForRegulatoryComplianceObj').ruleSetsForRegulatoryCompliancArr)]", "ruleSetsForSubAssessmentsObj": "[if(equals(variables('recommendationNamesLength'), 0), json('null'), variables('customRuleSetsForSubAssessmentsObj').ruleSetsForSubAssessmentsArr)]", "subAssessmentSource": [{"eventSource": "SubAssessments", "ruleSets": "[variables('ruleSetsForSubAssessmentsObj')]"}], "ruleSetsMap": {"Security recommendations": "[variables('ruleSetsForAssessmentsObj').ruleSetsForAssessmentsArr]", "Security alerts": "[variables('ruleSetsForAlertsObj').ruleSetsForAlertsArr]", "Overall secure score": null, "Secure score controls": "[variables('ruleSetsForSecureScoreControlsObj')]", "Regulatory compliance": "[variables('ruleSetsForSecureRegulatoryComplianceObj')]", "Overall secure score - snapshot": null, "Secure score controls - snapshot": "[variables('ruleSetsForSecureScoreControlsObj')]", "Regulatory compliance - snapshot": "[variables('ruleSetsForSecureRegulatoryComplianceObj')]", "Security recommendations - snapshot": "[variables('ruleSetsForAssessmentsObj').ruleSetsForAssessmentsArr]", "Security findings - snapshot": "[variables('ruleSetsForSubAssessmentsObj')]"}, "sourcesWithoutSubAssessments": {"copy": [{"name": "sources", "count": "[variables('exportedDataTypesLengthIfEmpty')]", "input": {"eventSource": "[variables('dataTypeMap')[parameters('exportedDataTypes')[copyIndex('sources')]]]", "ruleSets": "[variables('ruleSetsMap')[parameters('exportedDataTypes')[copyIndex('sources')]]]"}}]}, "sourcesWithSubAssessments": "[concat(variables('subAssessmentSource'),variables('sourcesWithoutSubAssessments').sources)]", "sources": "[if(equals(parameters('isSecurityFindingsEnabled'),bool('true')),variables('sourcesWithSubAssessments'),variables('sourcesWithoutSubAssessments').sources)]"}, "resources": [{"condition": "[equals(parameters('enableAscForStorage'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "StorageAccounts", "properties": {"pricingTier": "Standard", "subPlan": "DefenderForStorageV2", "extensions": [{"name": "OnUploadMalwareScanning", "isEnabled": "True", "additionalExtensionProperties": {"CapGBPerMonthPerStorageAccount": "5000"}}, {"name": "SensitiveDataDiscovery", "isEnabled": "True"}]}}, {"condition": "[equals(parameters('enableAscForServers'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "VirtualMachines", "dependsOn": ["[resourceId('Microsoft.Security/pricings', 'StorageAccounts')]"], "properties": {"pricingTier": "Standard", "subPlan": "P2", "resourcesCoverageStatus": "FullyCovered"}}, {"condition": "[equals(parameters('enableAscForSql'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "SqlServers", "dependsOn": ["[resourceId('Microsoft.Security/pricings', 'VirtualMachines')]"], "properties": {"pricingTier": "Standard"}}, {"condition": "[equals(parameters('enableAscForAppServices'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "AppServices", "dependsOn": ["[resourceId('Microsoft.Security/pricings', 'SqlServers')]"], "properties": {"pricingTier": "Standard"}}, {"condition": "[equals(parameters('enableAscForSqlOnVm'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "SqlServerVirtualMachines", "dependsOn": ["[resourceId('Microsoft.Security/pricings', 'AppServices')]"], "properties": {"pricingTier": "Standard"}}, {"condition": "[equals(parameters('enableAscForContainers'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "Containers", "dependsOn": ["[resourceId('Microsoft.Security/pricings', 'SqlServerVirtualMachines')]"], "properties": {"pricingTier": "Standard"}}, {"condition": "[equals(parameters('enableAscForKeyVault'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "KeyVaults", "dependsOn": ["[resourceId('Microsoft.Security/pricings', 'Containers')]"], "properties": {"pricingTier": "Standard"}}, {"condition": "[equals(parameters('enableAscForArm'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "Arm", "dependsOn": ["[resourceId('Microsoft.Security/pricings', 'KeyVaults')]"], "properties": {"pricingTier": "Standard"}}, {"condition": "[equals(parameters('enableAscForOssDb'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "OpenSourceRelationalDatabases", "dependsOn": ["[resourceId('Microsoft.Security/pricings', 'Arm')]"], "properties": {"pricingTier": "Standard"}}, {"condition": "[equals(parameters('enableAscForCosmosDbs'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "CosmosDbs", "dependsOn": ["[resourceId('Microsoft.Security/pricings', 'OpenSourceRelationalDatabases')]"], "properties": {"pricingTier": "Standard"}}, {"condition": "[equals(parameters('enableAscForCspm'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "CloudPosture", "dependsOn": ["[resourceId('Microsoft.Security/pricings', 'CosmosDbs')]"], "properties": {"pricingTier": "Standard"}}, {"condition": "[equals(parameters('enableAscForApis'), 'DeployIfNotExists')]", "type": "Microsoft.Security/pricings", "apiVersion": "2024-01-01", "name": "Api", "dependsOn": ["[resourceId('Microsoft.Security/pricings', 'CloudPosture')]"], "properties": {"pricingTier": "Standard", "subPlan": "P1"}}, {"type": "Microsoft.Security/securityContacts", "apiVersion": "2020-01-01-preview", "name": "default", "properties": {"description": "Defender for Cloud security contacts", "emails": "[parameters('emailContactAsc')]", "notificationsByRole": {"state": "On", "roles": ["Owner"]}, "alertNotifications": {"state": "On", "minimalSeverity": "Medium"}}}, {"name": "[parameters('resourceGroupName')]", "type": "Microsoft.Resources/resourceGroups", "apiVersion": "2019-10-01", "location": "[parameters('resourceGroupLocation')]"}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2019-10-01", "name": "[concat('nestedAutomationDeployment', '_', parameters('guidValue'))]", "resourceGroup": "[parameters('resourceGroupName')]", "dependsOn": ["[resourceId('Microsoft.Resources/resourceGroups/', parameters('resourceGroupName'))]"], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "variables": {}, "resources": [{"tags": {}, "apiVersion": "2019-01-01-preview", "location": "[parameters('resourceGroupLocation')]", "name": "ExportToWorkspace", "type": "Microsoft.Security/automations", "dependsOn": [], "properties": {"description": "Export Microsoft Defender for Cloud data to Log Analytics workspace via policy", "isEnabled": true, "scopes": [{"description": "[replace(variables('scopeDescription'),'{0}', subscription().subscriptionId)]", "scopePath": "[subscription().id]"}], "sources": "[variables('sources')]", "actions": [{"actionType": "Workspace", "workspaceResourceId": "[parameters('logAnalyticsResourceId')]"}]}}]}}}], "outputs": {}}