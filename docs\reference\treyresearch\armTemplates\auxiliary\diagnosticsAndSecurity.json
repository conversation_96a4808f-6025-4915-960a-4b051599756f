{
    "$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#",
    "contentVersion": "*******",
    "parameters": {
        "topLevelManagementGroupPrefix": {
            "type": "string",
            "maxLength": 10,
            "metadata": {
                "description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale."
            }
        },
        "managementSubscriptionId": {
            "type": "string",
            "maxLength": 36,
            "defaultValue": ""
        },
        "enableLogAnalytics": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "metadata": {
                "description": "If 'Yes' is selected when also adding a subscription for management, ARM will assign two policies to enable auditing in your environment, into the Log Analytics workspace for platform monitoring. If 'No', it will be ignored."
            }
        },
        "enableAsc": {
            "type": "string",
            "defaultValue": "No",
            "allowedValues": [
                "Yes",
                "No"
            ],
            "metadata": {
                "description": "If 'Yes' is selected when also adding a subscription for management, ARM will assign two policies to enable auditing in your environment, into the Log Analytics workspace for platform monitoring. If 'No', it will be ignored."
            }
        },
        "emailContactAsc": {
            "type": "string",
            "metadata": {
                "description": "Email address for Azure Security Center contact details."
            }
        },
        "enableAscForServers": {
            "type": "string",
            "defaultValue": "Free",
            "allowedValues": [
                "Standard",
                "Free"
            ]
        },
        "enableAscForAppServices": {
            "type": "string",
            "defaultValue": "Free",
            "allowedValues": [
                "Standard",
                "Free"
            ]
        },
        "enableAscForStorage": {
            "type": "string",
            "defaultValue": "Free",
            "allowedValues": [
                "Standard",
                "Free"
            ]
        },
        "enableAscForSql": {
            "type": "string",
            "defaultValue": "Free",
            "allowedValues": [
                "Standard",
                "Free"
            ]
        },
        "enableAscForSqlOnVm": {
            "type": "string",
            "defaultValue": "Free",
            "allowedValues": [
                "Standard",
                "Free"
            ]
        },
        "enableAscForKeyVault": {
            "type": "string",
            "defaultValue": "Free",
            "allowedValues": [
                "Standard",
                "Free"
            ]
        },
        "enableAscForArm": {
            "type": "string",
            "defaultValue": "Free",
            "allowedValues": [
                "Standard",
                "Free"
            ]
        },
        "enableAscForDns": {
            "type": "string",
            "defaultValue": "Free",
            "allowedValues": [
                "Standard",
                "Free"
            ]
        },
        "enableAscForKubernetes": {
            "type": "string",
            "defaultValue": "Free",
            "allowedValues": [
                "Standard",
                "Free"
            ]
        },
        "enableAscForRegistries": {
            "type": "string",
            "defaultValue": "Free",
            "allowedValues": [
                "Standard",
                "Free"
            ]
        },
        "onlineLzSubscriptionId": {
            "type": "array",
            "defaultValue": []
        },
        "corpLzSubscriptionId": {
            "type": "array",
            "defaultValue": []
        },
        "identitySubscriptionId": {
            "type": "string",
            "defaultValue": ""
        },
        "connectivitySubscriptionId": {
            "type": "string",
            "defaultValue": ""
        }
    },
    "variables": {
        "policyDefinitions": {
            "deployAzureActivityLog": "/providers/Microsoft.Authorization/policyDefinitions/2465583e-4e78-4c15-b6be-a36cbc7c8b0f",
            "deployResourceDiagnostics": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Deploy-Diag-LogAnalytics')]",
            "deployAzureSecurity": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Deploy-ASC-Config')]",
            "ascMonitoring": "/providers/Microsoft.Authorization/policySetDefinitions/1f3afdf9-d0c9-4c3d-847f-89da613e70a8"
        },
        "policyAssignmentNames": {
            "azureActivityLog": "Deploy-AzActivity-Log",
            "resourceDiagnostics": "Deploy-Resource-Diag",
            "azureSecurity": "Deploy-ASC-Security",
            "ascMonitoring": "Deploy-ASC-Monitoring"
        },
        "rbacOwner": "8e3af657-a8ff-443c-a75c-2fe8c4bcb635",
        "roleAssignmentNames": {
            "deployAzureActivityLog": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureActivityLog))]",
            "deployAzureSecurity": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureSecurity))]",
            "deployAscMonitoring": "[guid(concat(parameters('toplevelManagementGroupPrefix'), variables('policyAssignmentNames').ascMonitoring))]",
            "deployResourceRiagnostics": "[guid(concat(parameters('topLevelManagementGroupPrefix'), variables('policyAssignmentNames').resourceDiagnostics))]"
        },
        "blankTemplateEscaped": "{\"$schema\":\"https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#\",\"contentVersion\":\"*******\",\"parameters\":{},\"variables\":{},\"resources\":[],\"outputs\":{}}",
        "lzAscResourceDeploymentName": "[take(concat('asc-lz', deployment().location, deployment().name), 40)]",
        "lzActivityResourceDeploymentName": "[take(concat('activity-lz', deployment().location, deployment().name), 40)]",
        "mgAscResourceDeploymentName": "[take(concat('asc-mgmt', deployment().location, deployment().name), 40)]",
        "mgActivityResourceDeploymentName": "[take(concat('activity-mgmt', deployment().location, deployment().name), 40)]",
        "idAscResourceDeploymentName": "[take(concat('asc-identity', deployment().location, deployment().name), 40)]",
        "idActivityResourceDeploymentName": "[take(concat('activity-identity', deployment().location, deployment().name), 40)]",
        "connAscResourceDeploymentName": "[take(concat('asc-conn', deployment().location, deployment().name), 40)]",
        "connActivityResourceDeploymentName": "[take(concat('activity-conn', deployment().location, deployment().name), 40)]",
        "noOnlineLzActivityDeployment": "naOnlineActivity",
        "noOnlineLzAscDeployment": "naOnlineAsc",
        "noCorpLzActivityDeployment": "naCorpActivity",
        "noCorpLzAscDeployment": "naCorpAsc",
        "noIdActivityDeployment": "noIdActivity",
        "noIdAscDeployment": "noIdAsc",
        "noConnActivityDeployment": "noConnActivity",
        "noConnAscDeployment": "noConnAscDeployment"
    },
    "resources": [
        {
            // Conditional assignment of policy to enforce activity logs from subscriptions to Log Analytics
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'))]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').azureActivityLog]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-AzActivity-Log",
                "displayName": "Deploy-AzActivity-Log",
                "policyDefinitionId": "[variables('policyDefinitions').deployAzureActivityLog]",
                "scope": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'))]",
                "parameters": {
                    "logAnalytics": {
                        "value": "[toLower(concat('/subscriptions/', parameters('managementSubscriptionId'), '/resourceGroups/', parameters('topLevelManagementGroupPrefix'), '-mgmt', '/providers/Microsoft.OperationalInsights/workspaces/', parameters('topLevelManagementGroupPrefix'), '-la-', parameters('managementSubscriptionId')))]"
                    },
                    "logsEnabled": {
                        "value": "True"
                    }
                }
            }
        },
        {
            // Role assignment for the conditional policy assignment
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'))]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployAzureActivityLog]",
            "dependsOn": [
                "[variables('policyAssignmentNames').azureActivityLog]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(equals(parameters('enableLogAnalytics'), 'Yes'), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureActivityLog), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },     
        {
            // Conditional assignment of policy to enforce Azure Resource Diagnostics to Log Analytics
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'))]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').resourceDiagnostics]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-Resource-Diag",
                "displayName": "Deploy-Resource-Diag",
                "policyDefinitionId": "[variables('policyDefinitions').deployResourceDiagnostics]",
                "scope": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'))]",
                "parameters": {
                    "logAnalytics": {
                        "value": "[toLower(concat('/subscriptions/', parameters('managementSubscriptionId'), '/resourceGroups/', parameters('topLevelManagementGroupPrefix'), '-mgmt', '/providers/Microsoft.OperationalInsights/workspaces/', parameters('topLevelManagementGroupPrefix'), '-la-', parameters('managementSubscriptionId')))]"
                    }
                }
            }
        },
        {
            // Role assignment of the conditional Azure Resource Diagnostics policy assignment
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'))]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployResourceRiagnostics]",
            "dependsOn": [
                "[variables('policyAssignmentNames').resourceDiagnostics]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(equals(parameters('enableLogAnalytics'), 'Yes'), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').resourceDiagnostics), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },        
        {
            // Conditional assignment of policy to enforce Azure Security Center on subscriptions
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableAsc'), 'Yes'))]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').azureSecurity]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "Deploy-ASC-Defender",
                "displayName": "Deploy-ASC-Defender",
                "policyDefinitionId": "[variables('policyDefinitions').deployAzureSecurity]",
                "scope": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'))]",
                "parameters": {
                    "emailSecurityContact": {
                        "value": "[parameters('emailContactAsc')]"
                    },
                    "logAnalytics": {
                        "value": "[toLower(concat('/subscriptions/', parameters('managementSubscriptionId'), '/resourceGroups/', parameters('topLevelManagementGroupPrefix'), '-mgmt', '/providers/Microsoft.OperationalInsights/workspaces/', parameters('topLevelManagementGroupPrefix'), '-la'))]"
                    },
                    "ascExportResourceGroupName": {
                        "value": "[concat(parameters('topLevelManagementGroupPrefix'), '-asc-export')]"
                    },
                    "ascExportResourceGroupLocation": {
                        "value": "[deployment().location]"
                    },
                    "pricingTierVms": {
                        "value": "[parameters('enableAscForServers')]"
                    },
                    "pricingTierSqlServers": {
                        "value": "[parameters('enableAscForSql')]"
                    },
                    "pricingTierAppServices": {
                        "value": "[parameters('enableAscForAppServices')]"
                    },
                    "pricingTierStorageAccounts": {
                        "value": "[parameters('enableAscForStorage')]"
                    },
                    "pricingTierContainerRegistry": {
                        "value": "[parameters('enableAscForRegistries')]"
                    },
                    "pricingTierKeyVaults": {
                        "value": "[parameters('enableAscForKeyVault')]"
                    },
                    "pricingTierSqlServerVirtualMachines": {
                        "value": "[parameters('enableAscForSqlOnVm')]"
                    },
                    "pricingTierKubernetesService": {
                        "value": "[parameters('enableAscForKubernetes')]"
                    },
                    "pricingTierArm": {
                        "value": "[parameters('enableAscForArm')]"
                    },
                    "pricingTierDns": {
                        "value": "[parameters('enableAscForDns')]"
                    }
                }
            }
        },
        {
            // Role assignment of the conditional ASC policy assignment
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableAsc'), 'Yes'))]",
            "type": "Microsoft.Authorization/roleAssignments",
            "apiVersion": "2019-04-01-preview",
            "name": "[variables('roleAssignmentNames').deployAzureSecurity]",
            "dependsOn": [
                "[variables('policyAssignmentNames').azureSecurity]"
            ],
            "properties": {
                "principalType": "ServicePrincipal",
                "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]",
                "principalId": "[if(and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableAsc'), 'Yes')), toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureSecurity), '2018-05-01', 'Full' ).identity.principalId), 'na')]"
            }
        },
        {
            // Conditional assignment of policy to enable ASC monitoring
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableAsc'), 'Yes'))]",
            "type": "Microsoft.Authorization/policyAssignments",
            "apiVersion": "2018-05-01",
            "name": "[variables('policyAssignmentNames').ascMonitoring]",
            "location": "[deployment().location]",
            "identity": {
                "type": "SystemAssigned"
            },
            "properties": {
                "description": "ASC-Monitoring",
                "displayName": "ASC-Monitoring",
                "policyDefinitionId": "[variables('policyDefinitions').ascMonitoring]",
                "scope": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'))]",
                                "parameters" : {
                    "identityDesignateLessThanOwnersMonitoringEffect": {
                        "value": "Disabled"
                    },
                    "useRbacRulesMonitoringEffect": {
                        "value": "Disabled"
                    },
                    "useServicePrincipalToProtectSubscriptionsMonitoringEffect": {
                        "value": "Disabled"
                    },
                    "identityEnableMFAForOwnerPermissionsMonitoringEffect": {
                        "value": "Disabled"
                    },
                    "networkWatcherShouldBeEnabledMonitoringEffect": {
                        "value": "Disabled"
                    },
                    "autoProvisioningOfTheLogAnalyticsAgentShouldBeEnabledOnYourSubscriptionMonitoringEffect": {
                        "value": "Disabled"
                    }
                }
            }
        },         
        {
            // Conditional ARM deployments to invoke template from ActivityLog diagnostics on management subscription
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[variables('mgActivityResourceDeploymentName')]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/roleAssignments/', variables('roleAssignmentNames').deployAzureActivityLog)]"
            ],
            "subscriptionId": "[parameters('managementSubscriptionId')]",
            "properties": {
                "mode": "incremental",
                "template": "[if(equals(parameters('enableLogAnalytics'), 'Yes'), reference(variables('policyDefinitions').deployAzureActivityLog, '2018-05-01').policyRule.then.details.deployment.properties.template, variables('blankTemplateEscaped'))]",
                "parameters": "[if(equals(parameters('enableLogAnalytics'), 'Yes'), reference(concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureActivityLog), '2018-05-01').parameters, json('null'))]"
            }
        },
        {
            // Conditional ARM deployments to invoke template from ActivityLog diagnostics on identity subscription
            "condition": "[and(not(empty(parameters('identitySubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[if(not(empty(parameters('identitySubscriptionId'))), variables('idActivityResourceDeploymentName'), variables('noIdActivityDeployment'))]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/roleAssignments/', variables('roleAssignmentNames').deployAzureActivityLog)]"
            ],
            "subscriptionId": "[parameters('identitySubscriptionId')]",
            "properties": {
                "mode": "incremental",
                "template": "[if(and(not(empty(parameters('identitySubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes')), reference(variables('policyDefinitions').deployAzureActivityLog, '2018-05-01').policyRule.then.details.deployment.properties.template, variables('blankTemplateEscaped'))]",
                "parameters": "[if(and(not(empty(parameters('identitySubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes')), reference(concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureActivityLog), '2018-05-01').parameters, json('null'))]"
            }
        },
        {
            // Conditional ARM deployments to invoke template from ActivityLog diagnostics on connectivity subscription
            "condition": "[and(not(empty(parameters('connectivitySubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[if(not(empty(parameters('connectivitySubscriptionId'))), variables('connActivityResourceDeploymentName'), variables('noConnActivityDeployment'))]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/roleAssignments/', variables('roleAssignmentNames').deployAzureActivityLog)]"
            ],
            "subscriptionId": "[parameters('connectivitySubscriptionId')]",
            "properties": {
                "mode": "incremental",
                "template": "[if(and(not(empty(parameters('connectivitySubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes')), reference(variables('policyDefinitions').deployAzureActivityLog, '2018-05-01').policyRule.then.details.deployment.properties.template, variables('blankTemplateEscaped'))]",
                "parameters": "[if(and(not(empty(parameters('connectivitySubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes')), reference(concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureActivityLog), '2018-05-01').parameters, json('null'))]"
            }
        },
        {
            // Conditional ARM deployments to invoke template from ASC on management subscription
            "condition": "[and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableAsc'), 'Yes'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[variables('mgAscResourceDeploymentName')]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/roleAssignments/', variables('roleAssignmentNames').deployAzureSecurity)]"
            ],
            "subscriptionId": "[parameters('managementSubscriptionId')]",
            "properties": {
                "mode": "incremental",
                //"template": "[if(and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableAsc'), 'Yes')), reference(variables('policyDefinitions').deployAzureSecurity, '2018-05-01').policyRule.then.details.deployment.properties.template, 'na')]",
                //"parameters": "[if(and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableAsc'), 'Yes')) ,reference(concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureSecurity), '2018-05-01').parameters, json('null'))]"
            "templateLink": {
                    "contentVersion": "*******",
                    "uri": "[uri(deployment().properties.templateLink.uri, 'subscriptioSecurityConfig.json')]"
                },
                "parameters": {
                    "emailSecurityContact": {
                        "value": "[parameters('emailContactAsc')]"
                    },
                    "pricingTierVMs": {
                        "value": "[parameters('enableAscForServers')]"
                    },
                    "pricingTierSqlServers": {
                        "value": "[parameters('enableAscForSql')]"
                    },
                    "pricingTierAppServices": {
                        "value": "[parameters('enableAscForAppServices')]"
                    },
                    "pricingTierStorageAccounts": {
                        "value": "[parameters('enableAscForStorage')]"
                    },
                    "pricingTierSqlServerVirtualMachines": {
                        "value": "[parameters('enableAscForSqlOnVm')]"
                    },
                    "pricingTierKubernetesService": {
                        "value": "[parameters('enableAscForKubernetes')]"
                    },
                    "pricingTierContainerRegistry": {
                        "value": "[parameters('enableAscForRegistries')]"
                    },
                    "pricingTierKeyVaults": {
                        "value": "[parameters('enableAscForKeyVault')]"
                    },
                    "pricingTierDns": {
                        "value": "[parameters('enableAscForDns')]"
                    },
                    "pricingTierArm": {
                        "value": "[parameters('enableAscForArm')]"
                    },
                    "topLevelManagementGroupPrefix": {
                        "value": "[parameters('topLevelManagementGroupPrefix')]"
                    },
                    "workspaceResourceId":{
                        "value": "[toLower(concat('/subscriptions/', parameters('managementSubscriptionId'), '/resourceGroups/', parameters('topLevelManagementGroupPrefix'), '-mgmt', '/providers/Microsoft.OperationalInsights/workspaces/', parameters('topLevelManagementGroupPrefix'), '-la-', parameters('managementSubscriptionId')))]"
                    }
                }
            }
        },
        {
            // Conditional ARM deployments to invoke template from ASC on identity subscription
            "condition": "[and(not(empty(parameters('identitySubscriptionId'))), equals(parameters('enableAsc'), 'Yes'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[if(not(empty(parameters('identitySubscriptionId'))), variables('idAscResourceDeploymentName'), variables('noIdAscDeployment'))]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/roleAssignments/', variables('roleAssignmentNames').deployAzureSecurity)]"
            ],
            "subscriptionId": "[parameters('identitySubscriptionId')]",
            "properties": {
                "mode": "incremental",
                "template": "[if(and(not(empty(parameters('identitySubscriptionId'))), equals(parameters('enableAsc'), 'Yes')), reference(variables('policyDefinitions').deployAzureSecurity, '2018-05-01').policyRule.then.details.deployment.properties.template, 'na')]",
                "parameters": "[if(and(not(empty(parameters('identitySubscriptionId'))), equals(parameters('enableAsc'), 'Yes')) ,reference(concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureSecurity), '2018-05-01').parameters, json('null'))]"
            }
        },
        {
            // Conditional ARM deployments to invoke template from ASC on connectivity subscription
            "condition": "[and(not(empty(parameters('connectivitySubscriptionId'))), equals(parameters('enableAsc'), 'Yes'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[if(not(empty(parameters('connectivitySubscriptionId'))), variables('connAscResourceDeploymentName'), variables('noConnAscDeployment'))]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/roleAssignments/', variables('roleAssignmentNames').deployAzureSecurity)]"
            ],
            "subscriptionId": "[parameters('connectivitySubscriptionId')]",
            "properties": {
                "mode": "incremental",
                "template": "[if(and(not(empty(parameters('connectivitySubscriptionId'))), equals(parameters('enableAsc'), 'Yes')), reference(variables('policyDefinitions').deployAzureSecurity, '2018-05-01').policyRule.then.details.deployment.properties.template, 'na')]",
                "parameters": "[if(and(not(empty(parameters('connectivitySubscriptionId'))), equals(parameters('enableAsc'), 'Yes')), reference(concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureSecurity), '2018-05-01').parameters, json('null'))]"
            }
        },        
        {
            // Conditional ARM deployments to invoke template from ActivityLog diagnostics on online subscription(s)
            "condition": "[and(not(empty(parameters('onlineLzSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[if(not(empty(parameters('onlineLzSubscriptionId'))), concat(variables('lzActivityResourceDeploymentName'), copyIndex()), variables('noOnlineLzActivityDeployment'))]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/roleAssignments/', variables('roleAssignmentNames').deployAzureActivityLog)]"
            ],
            "copy": {
                "name": "lzOnlineActivityCopy",
                "count": "[if(not(empty(parameters('onlineLzSubscriptionId'))), length(parameters('onlineLzSubscriptionId')), 1)]"
            },
            "subscriptionId": "[if(not(empty(parameters('onlineLzSubscriptionId'))), parameters('onlineLzSubscriptionId')[copyIndex()], '')]",
            "properties": {
                "mode": "incremental",
                "template": "[if(and(not(empty(parameters('onlineLzSubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes')), reference(variables('policyDefinitions').deployAzureActivityLog, '2018-05-01').policyRule.then.details.deployment.properties.template, variables('blankTemplateEscaped'))]",
                "parameters": "[if(and(not(empty(parameters('onlineLzSubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes')), reference(concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureActivityLog), '2018-05-01').parameters, json('null'))]"
            }
        },        
        {
            // Conditional ARM deployments to invoke template from ActivityLog diagnostics on corp subscription(s)
            "condition": "[and(not(empty(parameters('corpLzSubscriptionId'))),equals(parameters('enableLogAnalytics'), 'Yes'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[if(not(empty(parameters('corpLzSubscriptionId'))), concat(variables('lzActivityResourceDeploymentName'), copyIndex()), variables('noCorpLzActivityDeployment'))]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/roleAssignments/', variables('roleAssignmentNames').deployAzureActivityLog)]"
            ],
            "copy": {
                "name": "lzCorpActivityCopy",
                "count": "[if(not(empty(parameters('corpLzSubscriptionId'))), length(parameters('corpLzSubscriptionId')), 1)]"
            },
            "subscriptionId": "[if(not(empty(parameters('corpLzSubscriptionId'))), parameters('corpLzSubscriptionId')[copyIndex()], '')]",
            "properties": {
                "mode": "incremental",
                "template": "[if(and(not(empty(parameters('corpLzSubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes')), reference(variables('policyDefinitions').deployAzureActivityLog, '2018-05-01').policyRule.then.details.deployment.properties.template, variables('blankTemplateEscaped'))]",
                "parameters": "[if(and(not(empty(parameters('corpLzSubscriptionId'))), equals(parameters('enableLogAnalytics'), 'Yes')), reference(concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureActivityLog), '2018-05-01').parameters, json('null'))]"
            }
        },
        {
            // Conditional ARM deployments to invoke template from ASC on online subscription(s)
            "condition": "[and(not(empty(parameters('onlineLzSubscriptionId'))), equals(parameters('enableAsc'), 'Yes'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[if(not(empty(parameters('onlineLzSubscriptionId'))), concat(variables('lzAscResourceDeploymentName'), copyIndex()), variables('noOnlineLzAscDeployment'))]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/roleAssignments/', variables('roleAssignmentNames').deployAzureSecurity)]"
            ],
            "copy": {
                "name": "lzOnlineAscCopy",
                "count": "[if(not(empty(parameters('onlineLzSubscriptionId'))), length(parameters('onlineLzSubscriptionId')), 1)]"
            },
            "subscriptionId": "[if(not(empty(parameters('onlineLzSubscriptionId'))), parameters('onlineLzSubscriptionId')[copyIndex()], '')]",
            "properties": {
                "mode": "incremental",
               // "template": "[if(and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableAsc'), 'Yes')), reference(variables('policyDefinitions').deployAzureSecurity, '2018-05-01').policyRule.then.details.deployment.properties.template, 'na')]",
               // "parameters": "[if(and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableAsc'), 'Yes')), reference(concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureSecurity), '2018-05-01').parameters, json('null'))]"
                "templateLink": {
                    "contentVersion": "*******",
                    "uri": "[uri(deployment().properties.templateLink.uri, 'subscriptioSecurityConfig.json')]"
                },
                "parameters": {
                    "emailSecurityContact": {
                        "value": "[parameters('emailContactAsc')]"
                    },
                    "pricingTierVMs": {
                        "value": "[parameters('enableAscForServers')]"
                    },
                    "pricingTierSqlServers": {
                        "value": "[parameters('enableAscForSql')]"
                    },
                    "pricingTierAppServices": {
                        "value": "[parameters('enableAscForAppServices')]"
                    },
                    "pricingTierStorageAccounts": {
                        "value": "[parameters('enableAscForStorage')]"
                    },
                    "pricingTierSqlServerVirtualMachines": {
                        "value": "[parameters('enableAscForSqlOnVm')]"
                    },
                    "pricingTierKubernetesService": {
                        "value": "[parameters('enableAscForKubernetes')]"
                    },
                    "pricingTierContainerRegistry": {
                        "value": "[parameters('enableAscForRegistries')]"
                    },
                    "pricingTierKeyVaults": {
                        "value": "[parameters('enableAscForKeyVault')]"
                    },
                    "pricingTierDns": {
                        "value": "[parameters('enableAscForDns')]"
                    },
                    "pricingTierArm": {
                        "value": "[parameters('enableAscForArm')]"
                    },
                    "topLevelManagementGroupPrefix": {
                        "value": "[parameters('topLevelManagementGroupPrefix')]"
                    },
                    "workspaceResourceId":{
                        "value": "[toLower(concat('/subscriptions/', parameters('managementSubscriptionId'), '/resourceGroups/', parameters('topLevelManagementGroupPrefix'), '-mgmt', '/providers/Microsoft.OperationalInsights/workspaces/', parameters('topLevelManagementGroupPrefix'), '-la-', parameters('managementSubscriptionId')))]"
                    }
                }
            }
        },
        {
            // Conditional ARM deployments to invoke template from ASC on corp subscription(s)
            "condition": "[and(not(empty(parameters('corpLzSubscriptionId'))), equals(parameters('enableAsc'), 'Yes'))]",
            "type": "Microsoft.Resources/deployments",
            "apiVersion": "2019-10-01",
            "name": "[if(not(empty(parameters('corpLzSubscriptionId'))), concat(variables('lzAscResourceDeploymentName'), copyIndex()), variables('noCorpLzAscDeployment'))]",
            "location": "[deployment().location]",
            "dependsOn": [
                "[resourceId('Microsoft.Authorization/roleAssignments/', variables('roleAssignmentNames').deployAzureSecurity)]"
            ],
            "copy": {
                "name": "lzCorpAscCopy",
                "count": "[if(not(empty(parameters('corpLzSubscriptionId'))), length(parameters('corpLzSubscriptionId')), 1)]"
            },
            "subscriptionId": "[if(not(empty(parameters('corpLzSubscriptionId'))), parameters('corpLzSubscriptionId')[copyIndex()], '')]",
            "properties": {
                "mode": "incremental",
               // "template": "[if(and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableAsc'), 'Yes')), reference(variables('policyDefinitions').deployAzureSecurity, '2018-05-01').policyRule.then.details.deployment.properties.template, 'na')]",
               // "parameters": "[if(and(not(empty(parameters('managementSubscriptionId'))), equals(parameters('enableAsc'), 'Yes')), reference(concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureSecurity), '2018-05-01').parameters, json('null'))]"
                           "templateLink": {
                    "contentVersion": "*******",
                    "uri": "[uri(deployment().properties.templateLink.uri, 'subscriptioSecurityConfig.json')]"
                },
                "parameters": {
                    "emailSecurityContact": {
                        "value": "[parameters('emailContactAsc')]"
                    },
                    "pricingTierVMs": {
                        "value": "[parameters('enableAscForServers')]"
                    },
                    "pricingTierSqlServers": {
                        "value": "[parameters('enableAscForSql')]"
                    },
                    "pricingTierAppServices": {
                        "value": "[parameters('enableAscForAppServices')]"
                    },
                    "pricingTierStorageAccounts": {
                        "value": "[parameters('enableAscForStorage')]"
                    },
                    "pricingTierSqlServerVirtualMachines": {
                        "value": "[parameters('enableAscForSqlOnVm')]"
                    },
                    "pricingTierKubernetesService": {
                        "value": "[parameters('enableAscForKubernetes')]"
                    },
                    "pricingTierContainerRegistry": {
                        "value": "[parameters('enableAscForRegistries')]"
                    },
                    "pricingTierKeyVaults": {
                        "value": "[parameters('enableAscForKeyVault')]"
                    },
                    "pricingTierDns": {
                        "value": "[parameters('enableAscForDns')]"
                    },
                    "pricingTierArm": {
                        "value": "[parameters('enableAscForArm')]"
                    },
                    "topLevelManagementGroupPrefix": {
                        "value": "[parameters('topLevelManagementGroupPrefix')]"
                    },
                    "workspaceResourceId":{
                        "value": "[toLower(concat('/subscriptions/', parameters('managementSubscriptionId'), '/resourceGroups/', parameters('topLevelManagementGroupPrefix'), '-mgmt', '/providers/Microsoft.OperationalInsights/workspaces/', parameters('topLevelManagementGroupPrefix'), '-la-', parameters('managementSubscriptionId')))]"
                    }
                }
            }
        }                     
    ],
    "outputs": {}
}