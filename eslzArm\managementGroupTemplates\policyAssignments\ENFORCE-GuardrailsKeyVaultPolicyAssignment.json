{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}}, "variables": {"policyDefinitions": {"enforceGuardrailsKeyVault": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Enforce-Guardrails-KeyVault')]"}, "policyAssignmentNames": {"enforceGuardrailsKeyVault": "Enforce-GR-Key<PERSON>ault", "description": "This initiative assignment enables recommended ALZ guardrails for Azure Key Vault.", "displayName": "Enforce recommended guardrails for Azure Key Vault"}, "nonComplianceMessage": {"message": "Recommended guardrails {enforcementMode} be enforced for Azure Key Vault.", "Default": "must", "DoNotEnforce": "should"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentNames').enforceGuardrailsKeyVault]", "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').enforceGuardrailsKeyVault]", "enforcementMode": "[parameters('enforcementMode')]"}}], "outputs": {}}