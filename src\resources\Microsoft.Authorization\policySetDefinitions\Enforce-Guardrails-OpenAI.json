{"name": "Enforce-Guardrails-OpenAI", "type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2023-04-01", "scope": null, "properties": {"policyType": "Custom", "displayName": "Enforce recommended guardrails for Open AI (Cognitive Service)", "description": "This policy initiative is a group of policies that ensures Open AI (Cognitive Service) is compliant per regulated Landing Zones.", "metadata": {"version": "1.2.0", "category": "Cognitive Services", "source": "https://github.com/Azure/Enterprise-Scale/", "alzCloudEnvironments": ["AzureCloud", "AzureChinaCloud", "AzureUSGovernment"]}, "parameters": {"cognitiveServicesOutboundNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesNetworkAcls": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesModifyDisableLocalAuth": {"type": "string", "defaultValue": "Modify", "allowedValues": ["Modify", "Disabled"]}, "cognitiveServicesDisableLocalAuth": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesCustomerStorage": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "cognitiveServicesManagedIdentity": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "azureAiNetworkAccess": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"]}, "azureAiPrivateLink": {"type": "string", "defaultValue": "Audit", "allowedValues": ["Audit", "Disabled"]}, "azureAiDisableLocalKey": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "azureAiDisableLocalKey2": {"type": "string", "defaultValue": "DeployIfNotExists", "allowedValues": ["DeployIfNotExists", "Disabled"]}, "azureAiDiagSettings": {"type": "string", "defaultValue": "AuditIfNotExists", "allowedValues": ["AuditIfNotExists", "Disabled"]}}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-CognitiveServices-RestrictOutboundNetworkAccess", "policyDefinitionReferenceId": "Deny-OpenAi-OutboundNetworkAccess", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('cognitiveServicesOutboundNetworkAccess')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Management/managementGroups/contoso/providers/Microsoft.Authorization/policyDefinitions/Deny-CognitiveServices-NetworkAcls", "policyDefinitionReferenceId": "Deny-OpenAi-NetworkAcls", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('cognitiveServicesNetworkAcls')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fe3fd216-4f83-4fc1-8984-2bbec80a3418", "policyDefinitionReferenceId": "Deny-Cognitive-Services-Managed-Identity", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('cognitiveServicesManagedIdentity')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/71ef260a-8f18-47b7-abcb-62d0673d94dc", "policyDefinitionReferenceId": "Deny-Cognitive-Services-Local-Auth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('cognitiveServicesDisableLocalAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/46aa9b05-0e60-4eae-a88b-1e9d374fa515", "policyDefinitionReferenceId": "Deny-Cognitive-Services-Cust-Storage", "definitionVersion": "2.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('cognitiveServicesCustomerStorage')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/14de9e63-1b31-492e-a5a3-c3f7fd57f555", "policyDefinitionReferenceId": "Modify-Cognitive-Services-Local-Auth", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('cognitiveServicesModifyDisableLocalAuth')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/037eea7a-bd0a-46c5-9a66-03aea78705d3", "policyDefinitionReferenceId": "Deny-AzureAI-Network-Access", "definitionVersion": "3.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('azureAiNetworkAccess')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d6759c02-b87f-42b7-892e-71b3f471d782", "policyDefinitionReferenceId": "Audit-AzureAI-Private-Link", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('azureAiPrivateLink')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/d45520cb-31ca-44ba-8da2-fcf914608544", "policyDefinitionReferenceId": "Dine-AzureAI-Local-Key", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('azureAiDisableLocalKey')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/55eff01b-f2bd-4c32-9203-db285f709d30", "policyDefinitionReferenceId": "Dine-AzureAI-Local-Key2", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('azureAiDisableLocalKey2')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/1b4d1c4e-934c-4703-944c-27c82c06bebb", "policyDefinitionReferenceId": "Aine-AzureAI-Diag-Settings", "definitionVersion": "1.*.*", "groupNames": [], "parameters": {"effect": {"value": "[[parameters('azureAiDiagSettings')]"}}}], "policyDefinitionGroups": null}}