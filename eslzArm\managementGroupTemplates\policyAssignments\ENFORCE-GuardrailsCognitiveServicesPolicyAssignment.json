{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}, "assignmentIndex": {"type": "int", "defaultValue": 0}}, "variables": {"policyDefinitions": {"enforceGuardrailsCognitiveServices": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Enforce-Guardrails-CognitiveServices')]"}, "policyAssignmentNames": {"enforceGuardrailsCognitiveServices": "[concat('Enforce-GR-CogServ', parameters('assignmentIndex'))]", "description": "This initiative assignment enables additional ALZ guardrails for Cognitive Services.", "displayName": "Enforce recommended guardrails for Cognitive Services"}, "nonComplianceMessage": {"message": "Recommended guardrails {enforcementMode} be enforced for Cognitive Services.", "Default": "must", "DoNotEnforce": "should"}, "rbacContributor": "b24988ac-6180-42a0-ab88-20f7382dd24c", "roleAssignmentNames": {"deployRoles": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').enforceGuardrailsCognitiveServices))]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentNames').enforceGuardrailsCognitiveServices]", "location": "[deployment().location]", "identity": {"type": "SystemAssigned"}, "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').enforceGuardrailsCognitiveServices]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}]}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').deployRoles]", "dependsOn": ["[resourceId('Microsoft.Authorization/policyAssignments', variables('policyAssignmentNames').enforceGuardrailsCognitiveServices)]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').enforceGuardrailsCognitiveServices), '2019-09-01', 'Full' ).identity.principalId)]"}}], "outputs": {}}