{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"enterpriseScaleCompanyPrefix": {"value": "testPortal"}, "telemetryOptOut": {"value": "Yes"}, "enableLogAnalytics": {"value": "Yes"}, "enableMonitorBaselines": {"value": "Yes"}, "enableMonitorConnectivity": {"value": "Yes"}, "enableMonitorIdentity": {"value": "Yes"}, "enableMonitorManagement": {"value": "Yes"}, "enableMonitorLandingZones": {"value": "Yes"}, "monitorAlertsResourceGroup": {"value": "rg-amba-monitoring-001"}, "emailContactActionGroup": {"value": "<EMAIL>"}, "retentionInDays": {"value": "30"}, "enableAsc": {"value": "Yes"}, "emailContactAsc": {"value": "<EMAIL>"}, "enableAscForServers": {"value": "DeployIfNotExists"}, "enableAscForServersVulnerabilityAssessments": {"value": "DeployIfNotExists"}, "enableAscForOssDb": {"value": "DeployIfNotExists"}, "enableAscForCosmosDbs": {"value": "DeployIfNotExists"}, "enableAscForAppServices": {"value": "DeployIfNotExists"}, "enableAscForStorage": {"value": "DeployIfNotExists"}, "enableAscForSql": {"value": "DeployIfNotExists"}, "enableAscForSqlOnVm": {"value": "DeployIfNotExists"}, "enableAscForKeyVault": {"value": "DeployIfNotExists"}, "enableAscForArm": {"value": "DeployIfNotExists"}, "enableAscForApis": {"value": "DeployIfNotExists"}, "enableAscForCspm": {"value": "DeployIfNotExists"}, "enableAscForContainers": {"value": "DeployIfNotExists"}, "enableMDEndpoints": {"value": "DeployIfNotExists"}, "addressPrefix": {"value": "**********/16"}, "enableDdoS": {"value": "No"}, "enablePrivateDnsZones": {"value": "Yes"}, "enableVpnGw": {"value": "No"}, "enableVpnActiveActive": {"value": "Yes"}, "gwRegionalOrAz": {"value": ""}, "gwRegionalSku": {"value": ""}, "gwAzSku": {"value": ""}, "vpnGateWayScaleUnit": {"value": "1"}, "subnetMaskForGw": {"value": ""}, "enableErGw": {"value": "No"}, "erAzSku": {"value": ""}, "erRegionalSku": {"value": ""}, "erRegionalOrAz": {"value": ""}, "expressRouteScaleUnit": {"value": "1"}, "enableHub": {"value": "vhub"}, "enableAzFw": {"value": "No"}, "enableAzFwDnsProxy": {"value": "No"}, "firewallSku": {"value": "Standard"}, "firewallZones": {"value": []}, "subnetMaskForAzFw": {"value": ""}, "addressPrefixSecondary": {"value": "**********/16"}, "enablePrivateDnsZonesSecondary": {"value": "Yes"}, "enableVpnGwSecondary": {"value": "No"}, "enableVpnActiveActiveSecondary": {"value": "Yes"}, "gwRegionalOrAzSecondary": {"value": ""}, "gwRegionalSkuSecondary": {"value": ""}, "gwAzSkuSecondary": {"value": ""}, "vpnGateWayScaleUnitSecondary": {"value": "1"}, "subnetMaskForGwSecondary": {"value": ""}, "enableErGwSecondary": {"value": "No"}, "erAzSkuSecondary": {"value": ""}, "erRegionalSkuSecondary": {"value": ""}, "erRegionalOrAzSecondary": {"value": ""}, "expressRouteScaleUnitSecondary": {"value": "1"}, "enableHubSecondary": {"value": "vhub"}, "enableAzFwSecondary": {"value": "No"}, "enableAzFwDnsProxySecondary": {"value": "No"}, "firewallSkuSecondary": {"value": "Standard"}, "firewallZonesSecondary": {"value": []}, "subnetMaskForAzFwSecondary": {"value": ""}, "denyMgmtPortsForIdentity": {"value": "Yes"}, "denySubnetWithoutNsgForIdentity": {"value": "Yes"}, "denyPipForIdentity": {"value": "Yes"}, "denyPipOnNicForCorp": {"value": "Yes"}, "enableVmBackupForIdentity": {"value": "Yes"}, "identityAddressPrefix": {"value": "**********/24"}, "identityAddressPrefixSecondary": {"value": "**********/24"}, "enableLzDdoS": {"value": "No"}, "denyPublicEndpoints": {"value": "Yes"}, "enablePrivateDnsZonesForLzs": {"value": "Yes"}, "enableEncryptionInTransit": {"value": "Yes"}, "enableVmMonitoring": {"value": "Yes"}, "enableVmssMonitoring": {"value": "Yes"}, "denyAksPrivileged": {"value": "Yes"}, "denyAksPrivilegedEscalation": {"value": "Yes"}, "denyHttpIngressForAks": {"value": "Yes"}, "enableVmBackup": {"value": "Yes"}, "denyMgmtPorts": {"value": "Yes"}, "denySubnetWithoutNsg": {"value": "Yes"}, "denyIpForwarding": {"value": "Yes"}, "denyClassicResources": {"value": "Yes"}, "denyVMUnmanagedDisk": {"value": "Yes"}, "enableSqlEncryption": {"value": "Yes"}, "enableSqlAudit": {"value": "Yes"}, "enableDecommissioned": {"value": "Yes"}, "enableSandbox": {"value": "Yes"}, "enableStorageHttps": {"value": "Yes"}, "enforceKvGuardrails": {"value": "Yes"}, "enforceBackup": {"value": "Yes"}, "denyHybridNetworking": {"value": "Yes"}, "auditPeDnsZones": {"value": "Yes"}, "enforceAcsb": {"value": "Yes"}, "delayCount": {"value": 35}}}