{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy and other resources created as part of Enterprise-scale."}}, "location": {"type": "string", "metadata": {"displayName": "location", "description": "Location of the HUB"}, "defaultValue": "[deployment().location]"}, "enableAzFwDnsProxy": {"type": "string", "allowedValues": ["Yes", "No"], "defaultValue": "No", "metadata": {"description": "Select whether the Azure Firewall should be used as DNS Proxy or not."}}, "connectivitySubscriptionId": {"type": "string", "defaultValue": "", "metadata": {"description": "Provide the subscription id for the dedicated connectivity subscription."}}, "firewallSku": {"type": "string", "allowedValues": ["Basic", "Standard", "Premium"], "defaultValue": "Standard"}}, "variables": {"rgName": "[concat(parameters('topLevelManagementGroupPrefix'), '-fwBasePolicy-', parameters('location'))]", "azFwPolicyName": "[concat(parameters('topLevelManagementGroupPrefix'), '-azfwpolicy-base-', parameters('location'))]", "resourceDeploymentName": "[take(concat(deployment().name, '-azfwpolicy-base-', parameters('location')), 64)]", "azFirewallPolicyId": {"id": "[concat('/subscriptions/', parameters('connectivitySubscriptionId'), '/resourceGroups/', variables('rgName'), '/providers/Microsoft.Network/firewallPolicies/', variables('azFwPolicyName'))]"}, "azFirewallDnsSettings": {"enableProxy": true}}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2019-10-01", "location": "[parameters('location')]", "name": "[concat('alz-', parameters('location'), '-', substring(uniqueString(parameters('connectivitySubscriptionId')),0,6), '-azFwBasePolicy')]", "subscriptionId": "[parameters('connectivitySubscriptionId')]", "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "apiVersion": "2019-10-01", "location": "[parameters('location')]", "name": "[variables('rgName')]", "properties": {}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2019-10-01", "name": "[variables('resourceDeploymentName')]", "resourceGroup": "[variables('rgName')]", "dependsOn": ["[concat('Microsoft.Resources/resourceGroups/', variables('rgName'))]"], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "resources": [{"type": "Microsoft.Network/firewallPolicies", "apiVersion": "2020-11-01", "name": "[variables('azFwPolicyName')]", "location": "[parameters('location')]", "properties": {"dnsSettings": "[if(equals(parameters('enableAzFwDnsProxy'), 'Yes'), variables('azFirewallDnsSettings'), json('null'))]", "sku": {"tier": "[parameters('firewallSku')]"}}}]}}}]}}}], "outputs": {}}