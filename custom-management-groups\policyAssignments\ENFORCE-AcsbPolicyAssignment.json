{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "nonComplianceMessagePlaceholder": {"type": "string", "defaultValue": "{enforcementMode}"}}, "variables": {"policyDefinitions": {"enforceAcsb": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Enforce-ACSB')]"}, "policyAssignmentNames": {"enforceAcsb": "Enforce-ACSB", "description": "This initiative assignment enables Azure Compute Security Baseline compliance auditing for Windows and Linux virtual machines.", "displayName": "Enforce Azure Compute Security Baseline compliance auditing"}, "nonComplianceMessage": {"message": "Azure Compute Security Baseline compliance auditing {enforcementMode} be enforced.", "Default": "must", "DoNotEnforce": "should"}, "rbacContributor": "b24988ac-6180-42a0-ab88-20f7382dd24c", "roleAssignmentNames": {"deployRoles": "[guid(concat(parameters('topLevelManagementGroupPrefix'), variables('policyAssignmentNames').enforceAcsb))]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentNames').enforceAcsb]", "location": "[deployment().location]", "identity": {"type": "SystemAssigned"}, "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').enforceAcsb]", "enforcementMode": "[parameters('enforcementMode')]", "nonComplianceMessages": [{"message": "[replace(variables('nonComplianceMessage').message, parameters('nonComplianceMessagePlaceholder'), variables('nonComplianceMessage')[parameters('enforcementMode')])]"}]}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').deployRoles]", "dependsOn": ["[variables('policyAssignmentNames').enforceAcsb]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacContributor'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').enforceAcsb), '2019-09-01', 'Full' ).identity.principalId)]"}}], "outputs": {}}