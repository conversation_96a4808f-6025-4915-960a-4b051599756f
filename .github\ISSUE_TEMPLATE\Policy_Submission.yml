name: Policy Submission
description: Submit an Azure policy/initiative for Azure Landing Zone
title: "[Policy]: "
labels: ["policy"]
projects: []
assignees:
  - springstone
body:
  - type: markdown
    attributes:
      value: Thanks for taking the time to fill out this policy submission!
  - type: dropdown
    id: policytype
    attributes:
      label: Policy Definition or Initiative
      description: Are you proposing a policy definition or initiative definition?
      options:
        - Definition
        - Initiative
        - Not sure
      default: 0
    validations:
      required: true
  - type: dropdown
    id: builtincustom
    attributes:
      label: Built-in/Custom
      description: Is the policy definition or initiative built-in or are you proposing a custom one?
      options:
        - Built-in
        - Custom
        - Not sure
      default: 0
    validations:
      required: true
  - type: input
    id: resourceid
    attributes:
      label: Built-in policy definition or initiative ID
      description: If this is for a built in policy definition or initiative, please provide the resource ID
      value: "<GUID>"
    validations:
      required: false
  - type: textarea
    id: description
    attributes:
      label: Custom policy definition or initiative description
      description: If this is a custom policy definition or initiative, please provide a description of what it should do.
      placeholder: A policy that
      value: "A policy that does ..."
    validations:
      required: true
  - type: dropdown
    id: assignmentscope
    attributes:
      label: Scope
      description: What scope (Management Group) should the policy definition or initiative be assigned to?
      options:
        - Intermediate Root
        - Platform
        - Connectivity
        - Management
        - Identity
        - Landing Zones
        - Corp
        - Online
        - Decommissioned
        - Sandbox
        - Multiple / Other
      default: 0
    validations:
      required: true
  - type: checkboxes
    id: defaultassignment
    attributes:
      label: Default Assignment
      description: Should the policy definition or initiative be assigned by default to the scope above in Azure Landing Zone?
      options:
        - label: "Yes"
  - type: textarea
    id: Comments
    attributes:
      label: Comments/thoughts
      description: Do you have any additional comments/thoughts?
