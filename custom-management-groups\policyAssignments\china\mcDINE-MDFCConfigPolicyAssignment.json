{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide the ESLZ company prefix to the intermediate root management group containing the policy definitions."}}, "enforcementMode": {"type": "string", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "defaultValue": "<PERSON><PERSON><PERSON>"}, "logAnalyticsResourceId": {"type": "string", "metadata": {"description": "Provide the resourceId to the central Log Analytics workspace."}}, "emailContactAsc": {"type": "string", "metadata": {"description": "Provide the email address for the Microsoft Defender for Cloud security contact."}}, "enableAscForServers": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForSql": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}, "enableAscForContainers": {"type": "string", "allowedValues": ["Disabled", "DeployIfNotExists"], "defaultValue": "Disabled"}}, "variables": {"policyDefinitions": {"deployAzureSecurity": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/Deploy-MDFC-Config')]"}, "policyAssignmentNames": {"azureSecurity": "Deploy-MDFC-Config", "description": "Deploy Microsoft Defender for Cloud and Security Contacts", "displayName": "Deploy Microsoft Defender for Cloud configuration"}, "rbacOwner": "8e3af657-a8ff-443c-a75c-2fe8c4bcb635", "roleAssignmentNames": {"deployAzureSecurity": "[guid(concat(parameters('toplevelManagementGroupPrefix'),variables('policyAssignmentNames').azureSecurity))]"}}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "name": "[variables('policyAssignmentNames').azureSecurity]", "location": "[deployment().location]", "identity": {"type": "SystemAssigned"}, "properties": {"description": "[variables('policyAssignmentNames').description]", "displayName": "[variables('policyAssignmentNames').displayName]", "policyDefinitionId": "[variables('policyDefinitions').deployAzureSecurity]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {"emailSecurityContact": {"value": "[parameters('emailContactAsc')]"}, "logAnalytics": {"value": "[parameters('logAnalyticsResourceId')]"}, "ascExportResourceGroupName": {"value": "[concat(parameters('topLevelManagementGroupPrefix'), '-asc-export')]"}, "ascExportResourceGroupLocation": {"value": "[deployment().location]"}, "enableAscForServers": {"value": "[parameters('enableAscForServers')]"}, "enableAscForSql": {"value": "[parameters('enableAscForSql')]"}, "enableAscForContainers": {"value": "[parameters('enableAscForContainers')]"}}}}, {"type": "Microsoft.Authorization/roleAssignments", "apiVersion": "2019-04-01-preview", "name": "[variables('roleAssignmentNames').deployAzureSecurity]", "dependsOn": ["[variables('policyAssignmentNames').azureSecurity]"], "properties": {"principalType": "ServicePrincipal", "roleDefinitionId": "[concat('/providers/Microsoft.Authorization/roleDefinitions/', variables('rbacOwner'))]", "principalId": "[toLower(reference(concat('/providers/Microsoft.Authorization/policyAssignments/', variables('policyAssignmentNames').azureSecurity), '2019-09-01', 'Full' ).identity.principalId)]"}}], "outputs": {}}